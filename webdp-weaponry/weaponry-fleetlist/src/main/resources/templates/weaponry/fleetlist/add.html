<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增舰船列表')" />
    <th:block th:include="include :: select2-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-fleetlist-add">
            <input name="weaponryCode" th:value="${weaponryCode}" type="hidden">
            <input name="relatedWeaponryCode" id="relatedWeaponryCode" type="hidden">
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">中文舰名：</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <input type="text" class="form-control" id="shipNameCn" placeholder="可通过装备编码或名称搜索" name="shipNameCn" required>
                        <div class="input-group-btn">
                            <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-right" role="menu">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">英文舰名：</label>
                <div class="col-sm-8">
                    <input name="shipNameEn" id="shipNameEn" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">舷号：</label>
                <div class="col-sm-8">
                    <input name="shipNo" id="shipNo" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">建造商中文名称：</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <input name="builderCode" id="builderCode" type="hidden">
                        <input type="text" class="form-control" id="builderNameCn" placeholder="可通过建造商机构编码或名称搜索" name="builderNameCn">
                        <div class="input-group-btn">
                            <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-right" role="menu">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">建造商英文名称：</label>
                <div class="col-sm-8">
                    <input name="builderNameEn" id="builderNameEn" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">开工时间：</label>
                <div class="col-sm-8">
                    <div class="row">
                        <div class="col-sm-4">
                            <select  name="laidDownYear" id="laidDownYear" class="form-control">
                                <option value="">年份</option>
                            </select>
                        </div>
                        <div class="col-sm-4">
                            <select  name="laidDownMonth" id="laidDownMonth" class="form-control" disabled="disabled">
                                <option value="">月份</option>
                            </select>
                        </div>
                        <div class="col-sm-4">
                            <select name="laidDownDay" id="laidDownDay" class="form-control" disabled="disabled">
                                <option value="">日</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">下水时间：</label>
                <div class="col-sm-8">
                    <div class="row">
                        <div class="col-sm-4">
                            <select  name="launchedYear" id="launchedYear" class="form-control">
                                <option value="">年份</option>
                            </select>
                        </div>
                        <div class="col-sm-4">
                            <select  name="launchedMonth" id="launchedMonth" class="form-control" disabled="disabled">
                                <option value="">月份</option>
                            </select>
                        </div>
                        <div class="col-sm-4">
                            <select name="launchedDay" id="launchedDay" class="form-control" disabled="disabled">
                                <option value="">日</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">服役时间：</label>
                <div class="col-sm-8">
                    <div class="row">
                        <div class="col-sm-4">
                            <select  name="commissionedYear" id="commissionedYear" class="form-control">
                                <option value="">年份</option>
                            </select>
                        </div>
                        <div class="col-sm-4">
                            <select  name="commissionedMonth" id="commissionedMonth" class="form-control" disabled="disabled">
                                <option value="">月份</option>
                            </select>
                        </div>
                        <div class="col-sm-4">
                            <select name="commissionedDay" id="commissionedDay" class="form-control" disabled="disabled">
                                <option value="">日</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">F/S：</label>
                <div class="col-sm-8">
                    <input name="fs" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">数据来源：</label>
                <div class="col-sm-8">
                    <input name="source" class="form-control" type="text" required>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js" />
    <th:block th:include="include :: bootstrap-suggest-js" />
    <script th:inline="javascript">

        buildSelect("laidDown");//开工时间
        buildSelect("launched");//下水时间
        buildSelect("commissioned");//服役时间

        //构建下拉框
        function buildSelect(prefix) {
            let year = document.getElementById(prefix + "Year");
            let date = new Date();
            let currentYear = date.getFullYear() + 10;
            //组建年份选择器
            for (let i = currentYear; i >= currentYear - 110; i--) {
                year.options.add(new Option(i, i));
            }

            //组建月份选择器
            let month = document.getElementById(prefix + "Month");
            for (let j = 1; j <= 12; j++) {
                if (j < 10){
                    month.options.add(new Option('0'+j, '0'+j));
                }else{
                    month.options.add(new Option(j, j));
                }
            }
            //组建日选择器
            let day = document.getElementById(prefix + "Day");
            for (let j = 1; j <= 31; j++) {
                if (j < 10){
                    day.options.add(new Option('0'+j, '0'+j));
                }else{
                    day.options.add(new Option(j, j));
                }
            }
        }

        $("#laidDownYear").change(function(){
            var laidDownYear = $('#laidDownYear option:selected') .val();
            if (laidDownYear != null && laidDownYear != ''){
                $('#laidDownMonth').attr("disabled",false);
            }else{
                $('#laidDownMonth').attr("disabled",true);
                $("#laidDownMonth").select2("val", [""]);
            }
        });

        $("#laidDownMonth").change(function(){
            let laidDownMonth = $('#laidDownMonth option:selected') .val();
            if (laidDownMonth != null && laidDownMonth != ''){
                $('#laidDownDay').attr("disabled",false);
            }else{
                $("#laidDownDay").select2("val", [""]);
                $('#laidDownDay').attr("disabled",true);
            }
        });

        $("#launchedYear").change(function(){
            var launchedYear = $('#launchedYear option:selected') .val();
            if (launchedYear != null && launchedYear != ''){
                $('#launchedMonth').attr("disabled",false);
            }else{
                $('#launchedMonth').attr("disabled",true);
                $("#launchedMonth").select2("val", [""]);
            }
        });

        $("#launchedMonth").change(function(){
            let launchedMonth = $('#launchedMonth option:selected') .val();
            if (launchedMonth != null && launchedMonth != ''){
                $('#launchedDay').attr("disabled",false);
            }else{
                $("#launchedDay").select2("val", [""]);
                $('#launchedDay').attr("disabled",true);
            }
        });

        $("#commissionedYear").change(function(){
            var commissionedYear = $('#commissionedYear option:selected') .val();
            if (commissionedYear != null && commissionedYear != ''){
                $('#commissionedMonth').attr("disabled",false);
            }else{
                $('#commissionedMonth').attr("disabled",true);
                $("#commissionedMonth").select2("val", [""]);
            }
        });

        $("#commissionedMonth").change(function(){
            let commissionedMonth = $('#commissionedMonth option:selected') .val();
            if (commissionedMonth != null && commissionedMonth != ''){
                $('#commissionedDay').attr("disabled",false);
            }else{
                $("#commissionedDay").select2("val", [""]);
                $('#commissionedDay').attr("disabled",true);
            }
        });

        let prefix = ctx + "weaponry/fleetlist"
        let weaponry = ctx +"weaponry/common"
        $("#form-fleetlist-add").validate({
            focusCleanup: true
        });

        //加载武器装备搜索下拉框 武器装备编码 weaponryCode
        let weaponryBsSuggest = $("#shipNameCn").bsSuggest({
            url: ctx + "weaponry/ship/selectShipByKeyword?weaponryCode=&nationalOrigin=&keyword=",
            //ignorecase : true,//搜索忽略大小写
            getDataMethod: 'url',//获取数据的方式，总是从 URL 获取
            autoSelect: false,// 键盘向上/下方向键时，是否自动选择值
            listStyle: {
                'padding-top': 0,
                'max-height': '250px',
                'max-width': '400px',
                'overflow': 'auto',
                'width': 'auto',
                'transition': '0.3s',
                '-webkit-transition': '0.3s',
                '-moz-transition': '0.3s',
                '-o-transition': '0.3s'
            },//列表的样式控制
            idField: "id",//每组数据的哪个字段作为 data-id，优先级高于 indexId 设置
            keyField: "shipTranslateName",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置
            effectiveFields: ['shipTranslateName', 'nationalOrigin','hullNo'],//设置展示字段
            effectiveFieldsAlias: {
                shipName: '舰船名称',
                nationalOrigin: '国家',
                hullNo: '舷号'
            }//设置字段别名
        }).on('onSetSelectValue', function (e, keyword, result) {// 当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
            //选择后隐藏下拉框
            $("#shipNameCn").bsSuggest("hide");
            $('#relatedWeaponryCode').val(result.weaponryCode);
            $('#shipNo').val(result.hullNo);
            $('#shipNameEn').val(result.shipName);
        }).on("blur", function (e) {//当无匹配项且失去焦点时清除编码
            let background = $('#shipNameCn').css("background-color");
            if (background == 'rgba(255, 0, 0, 0.1)'){
                $('#relatedWeaponryCode').val('');
                $('#shipNo').val('');
                $('#shipNameCn').css("background-color", 'rgb(255, 255, 255)')
            }
        });

        //加载武器装备搜索下拉框 武器装备编码 weaponryCode
        let builderBsSuggest = $("#builderNameCn").bsSuggest({
            url: ctx + "organization/org/selectByKeywordAndIncludeOrgTypes?orgTypes=国防企业&keyword=",
            //ignorecase : true,//搜索忽略大小写
            getDataMethod: 'url',//获取数据的方式，总是从 URL 获取
            autoSelect: false,// 键盘向上/下方向键时，是否自动选择值
            listStyle: {
                'padding-top': 0,
                'max-height': '250px',
                'max-width': '400px',
                'overflow': 'auto',
                'width': 'auto',
                'transition': '0.3s',
                '-webkit-transition': '0.3s',
                '-moz-transition': '0.3s',
                '-o-transition': '0.3s'
            },//列表的样式控制
            idField: "orgCode",//每组数据的哪个字段作为 data-id，优先级高于 indexId 设置
            keyField: "orgNameCn",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置
            effectiveFields: ['orgCode', 'orgNameCn', 'orgNameEn', 'parentName'],//设置展示字段
            effectiveFieldsAlias: {
                orgCode: '单位编码',
                orgNameCn: '中文名称',
                orgNameEn: '英文名称',
                parentName: '父机构名称'
            }//设置字段别名
        }).on('onSetSelectValue', function (e, keyword, result) {// 当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
            //选择后隐藏下拉框
            $("#builderNameCn").bsSuggest("hide");
            $('#builderCode').val(result.orgCode);
            $('#builderNameEn').val(result.orgNameEn);
        }).on("blur", function (e) {//当无匹配项且失去焦点时清除编码
            let background = $('#builderNameCn').css("background-color");
            if (background == 'rgba(255, 0, 0, 0.1)'){
                $('#builderCode').val('');
                $('#builderNameCn').css("background-color", 'rgb(255, 255, 255)')
            }
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-fleetlist-add').serialize());
            }
        }
    </script>
</body>
</html>