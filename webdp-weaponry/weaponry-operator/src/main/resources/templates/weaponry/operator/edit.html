<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改武器装备运营商')"/>
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: select2-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-operator-edit" th:object="${idwWeaponryOperator}">
            <input name="operatorId" th:field="*{operatorId}" type="hidden">
            <input name="weaponryCode" th:field="*{weaponryCode}" type="hidden">
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">国家/地区：</label>
                <div class="col-sm-8">
                    <select name="country" id="country" class="form-control" th:with="type=${@dict.getType('sys_country')}" required>
                        <option value="" style="color: #b6b6b6" disabled selected>选择国家</option>
                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"
                                th:field="*{country}"></option>
                    </select>
                </div>
            </div>
            <div class="form-group" id="variantDiv">
                <label class="col-sm-3 control-label">变体：</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <input type="text" class="form-control" id="variant" placeholder="可通过变体名称搜索" name="variant" th:field="*{variant}">
                        <div class="input-group-btn">
                            <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-right" role="menu">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">用户中文名称：</label>
                <div class="col-sm-8">
                    <input type="hidden" id="operatorCode" name="operatorCode" th:field="*{operatorCode}" >
                    <div class="input-group">
                        <input type="text" class="form-control" id="operatorNameCn" name="operatorNameCn" th:value="${operatorNameCn}" maxlength="60" placeholder="可通过单位编码或名称搜索">
                        <div class="input-group-btn">
                            <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-right" role="menu">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">用户英文名称：</label>
                <div class="col-sm-8">
                    <input name="operatorNameEn" id="operatorNameEn" th:field="*{operatorNameEn}" class="form-control" type="text" maxlength="200">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">单位简介：</label>
                <div class="col-sm-8">
                    <textarea name="operatorProfile" id="operatorProfile" class="form-control" rows="6">[[*{operatorProfile}]]</textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">订单数量：</label>
                <div class="col-sm-8">
                    <input name="orderQuantity" th:field="*{orderQuantity}" class="form-control" type="text" maxlength="9" number="true">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">在役数量/库存数量：</label>
                <div class="col-sm-8">
                    <input name="inService" th:field="*{inService}" class="form-control" type="text" maxlength="9" number="true">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">第一次交付时间：</label>
                <div class="col-sm-8">
                    <div class="row">
                        <div class="col-sm-4">
                            <select name="year" id="year" class="form-control">
                                <option value="">年份</option>
                            </select>
                        </div>
                        <div class="col-sm-4">
                            <select name="month" id="month" class="form-control" disabled="disabled">
                                <option value="">月份</option>
                            </select>
                        </div>
                        <div class="col-sm-4">
                            <select name="day" id="day" class="form-control" disabled="disabled">
                                <option value="">日</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">数据来源：</label>
                <div class="col-sm-8">
                    <input name="source" th:field="*{source}" class="form-control" type="text" required maxlength="500">
                </div>
            </div>
        </form>
    </div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: bootstrap-suggest-js"/>
<th:block th:include="include :: bootstrap-fileinput-js"/>
<th:block th:include="include :: select2-js"/>
<script th:inline="javascript">
    let prefix = ctx + "weaponry/operator";
    let organization = ctx + "organization/org"
    $("#form-operator-edit").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/edit", $('#form-operator-edit').serialize());
        }
    }

    let isShowVariant = [[${isShowVariant}]]
    if (!isShowVariant){
        $("#variantDiv").attr("style","display:none;");
    }

    //加载变体搜索下拉框 variant
    var variantBsSuggest = $("#variant").bsSuggest({
        url: ctx + 'weaponry/variant/selectByWeaponryCodeAndKeyword?weaponryCode='+[[${idwWeaponryOperator.weaponryCode}]]+'&keyword=',
        //ignorecase : true,//搜索忽略大小写
        getDataMethod: 'url',//获取数据的方式，url：一直从url请求；data：从 options.data 获取；firstByUrl：第一次从Url获取全部数据，之后从options.data获取
        autoSelect: false,// 键盘向上/下方向键时，是否自动选择值
        autoMinWidth: false,//是否自动最小宽度，设为 false 则最小宽度不小于输入框宽度
        listStyle: {
            'padding-top': 0,
            'max-height': '375px',
            'max-width': '600px',
            'overflow': 'auto',
            'width': 'auto',
            'transition': '0.3s',
            '-webkit-transition': '0.3s',
            '-moz-transition': '0.3s',
            '-o-transition': '0.3s'
        },//列表的样式控制
        idField: "variantNameCn",//每组数据的哪个字段作为 data-id，优先级高于 indexId 设置
        keyField: "variantNameCn",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置
        effectiveFields: ['variantNameCn','variantNameEn'],//设置展示字段
        effectiveFieldsAlias: {
            variantNameCn: '中文名称',
            variantNameEn: '英文名称'
        }//设置字段别名
    }).on('onSetSelectValue', function (e, keyword, result) {// 当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
        //选择后隐藏下拉框
        $("#variant").bsSuggest("hide");
        let variantNameCn = result.variantNameCn;
        $("#variant").val((variantNameCn != null && variantNameCn != '' && variantNameCn != undefined) ? variantNameCn : result.variantNameEn)
    }).on("blur", function (e) {//当无匹配项且失去焦点时清除编码
        let background = $('#variant').css("background-color");
        if (background == 'rgba(255, 0, 0, 0.1)'){
            $('#variant').css("background-color", 'rgb(255, 255, 255)')
        }
    });

    //加载运营商搜索下拉框 运营商 operatorNameCn
    var orgBsSuggest = $("#operatorNameCn").bsSuggest({
        url: organization + "/selectByKeywordAndIncludeOrgTypes?orgTypes=国防单位,军事基地&keyword=",
        //ignorecase : true,//搜索忽略大小写
        getDataMethod: 'url',//获取数据的方式，url：一直从url请求；data：从 options.data 获取；firstByUrl：第一次从Url获取全部数据，之后从options.data获取
        autoSelect: false,// 键盘向上/下方向键时，是否自动选择值
        autoMinWidth: false,//是否自动最小宽度，设为 false 则最小宽度不小于输入框宽度
        listStyle: {
            'padding-top': 0,
            'max-height': '375px',
            'max-width': '600px',
            'overflow': 'auto',
            'width': 'auto',
            'transition': '0.3s',
            '-webkit-transition': '0.3s',
            '-moz-transition': '0.3s',
            '-o-transition': '0.3s'
        },//列表的样式控制
        idField: "orgCode",//每组数据的哪个字段作为 data-id，优先级高于 indexId 设置
        keyField: "orgNameCn",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置
        effectiveFields: ['orgCode', 'orgType', 'orgNameCn', 'orgNameEn'],//设置展示字段
        effectiveFieldsAlias: {
            orgCode: '单位编码',
            orgType: '用户类型',
            orgNameCn: '中文名称',
            orgNameEn: '英文名称'
        }//设置字段别名
    }).on('onSetSelectValue', function (e, keyword, result) {// 当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
        //选择后隐藏下拉框
        $("#operatorNameCn").bsSuggest("hide");
        $('#operatorCode').val(result.orgCode);
        $('#operatorNameCn').val(result.orgNameCn);
        $('#operatorNameEn').val(result.orgNameEn);
        $('#operatorProfile').val(result.profileCn != '' && result.profileCn != null ? result.profileCn : result.profileEn);
        $('#country').select2("val", [result.country]);
    }).on("blur", function (e) {//当无匹配项且失去焦点时清除编码
        let background = $('#operatorNameCn').css("background-color");
        if (background == 'rgba(255, 0, 0, 0.1)'){
            $('#operatorCode').val("");
            $('#operatorNameCn').css("background-color", 'rgb(255, 255, 255)')
        }
    });

    let year = document.getElementById("year");
    let date = new Date();
    let selectYear = date.getFullYear();
    //组建年份选择器
    for (let i = selectYear; i >= selectYear - 150; i--) {
        year.options.add(new Option(i, i));
    }
    //组建月份选择器
    let month = document.getElementById("month");
    for (let j = 1; j <= 12; j++) {
        if (j < 10) {
            month.options.add(new Option('0' + j, '0' + j));
        } else {
            month.options.add(new Option(j, j));
        }
    }
    //组建日选择器
    let day = document.getElementById("day");
    for (let j = 1; j <= 31; j++) {
        if (j < 10) {
            day.options.add(new Option('0' + j, '0' + j));
        } else {
            day.options.add(new Option(j, j));
        }
    }

    window.onload = function() {
        //给下拉框赋值
        let idwWeaponryOperator = [[${idwWeaponryOperator}]]
        if (idwWeaponryOperator.year != null && idwWeaponryOperator.year != ''){
            $("#year").select2("val", [idwWeaponryOperator.year]);
            $('#month').attr("disabled", false);
        }
        if (idwWeaponryOperator.month != null && idwWeaponryOperator.month != ''){
            $("#month").select2("val", [idwWeaponryOperator.month]);
            $('#day').attr("disabled", false);
        }
        if (idwWeaponryOperator.day != null && idwWeaponryOperator.day != ''){
            $("#day").select2("val", [idwWeaponryOperator.day]);
        }
    }

    $("#year").change(function () {
        let year = $('#year option:selected').val();
        if (year != '' && year != null) {
            $('#month').attr("disabled", false);
        } else {
            $("#month").select2("val", [""]);
            $('#month').attr("disabled", true);
        }
    });

    $("#month").change(function () {
        let month = $('#month option:selected').val();
        if (month != '' && month != null) {
            $('#day').attr("disabled", false);
        } else {
            $("#day").select2("val", [""]);
            $('#day').attr("disabled", true);
        }
    });
</script>
</body>
</html>