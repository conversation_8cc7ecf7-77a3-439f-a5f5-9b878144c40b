<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lirong.weaponry.armament.mapper.IdwWeaponryArmamentMapper">
    
    <resultMap type="com.lirong.weaponry.armament.domain.IdwWeaponryArmament" id="IdwWeaponryArmamentResult">
        <result property="armamentId"    column="armament_id"    />
        <result property="weaponryCode"    column="weaponry_code"    />
        <result property="armamentCode"    column="armament_code"    />
        <result property="armamentNameCn"    column="armament_name_cn"    />
        <result property="armamentNameEn"    column="armament_name_en"    />
        <result property="category"    column="category"    />
        <result property="profile"    column="profile"    />
        <result property="source"    column="source"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectIdwWeaponryArmamentVo">
        SELECT
            armament_id,
            weaponry_code,
            armament_code,
            armament_name_cn,
            armament_name_en,
            category,
            profile,
            source
        FROM
            idw_weaponry_armament
    </sql>

    <select id="selectIdwWeaponryArmamentList" parameterType="com.lirong.weaponry.armament.domain.IdwWeaponryArmament" resultMap="IdwWeaponryArmamentResult">
        <include refid="selectIdwWeaponryArmamentVo"/>
        <where>
            is_delete = 0
            <if test="weaponryCode != null  and weaponryCode != ''"> and weaponry_code = #{weaponryCode}</if>
            <if test="armamentCode != null  and armamentCode != ''"> and armament_code = #{armamentCode}</if>
            <if test="armamentNameCn != null  and armamentNameCn != ''"> and (
                armament_name_cn LIKE CONCAT( '%', #{armamentNameCn}, '%' ) or armament_name_en LIKE CONCAT( '%', #{armamentNameCn}, '%' )
                )</if>
            <if test="category != null  and category != ''"> and category = #{category}</if>
            <if test="profile != null  and profile != ''"> and profile = #{profile}</if>
        </where>
    </select>
    
    <select id="selectIdwWeaponryArmamentById" parameterType="Long" resultMap="IdwWeaponryArmamentResult">
        <include refid="selectIdwWeaponryArmamentVo"/>
        where is_delete = 0 and armament_id = #{armamentId}
    </select>

    <!--根据装备编码查询装备配备的武器-->
    <select id="selectWeaponryArmamentByWeaponryCodes" resultMap="IdwWeaponryArmamentResult">
        <include refid="selectIdwWeaponryArmamentVo"/>
        where
            is_delete = 0
        <if test="weaponryCodes != null and weaponryCodes.length > 0">
            AND weaponry_code IN
            <foreach item="weaponryCode" collection="weaponryCodes" open="(" separator="," close=")">
                #{weaponryCode}
            </foreach>
        </if>
    </select>

    <insert id="insertIdwWeaponryArmament" parameterType="com.lirong.weaponry.armament.domain.IdwWeaponryArmament" useGeneratedKeys="true" keyProperty="armamentId">
        insert into idw_weaponry_armament
        <trim prefix="(" suffix=")" suffixOverrides=",">
            is_delete,
            <if test="weaponryCode != null and weaponryCode != ''">weaponry_code,</if>
            <if test="armamentCode != null">armament_code,</if>
            <if test="armamentNameCn != null">armament_name_cn,</if>
            <if test="armamentNameEn != null">armament_name_en,</if>
            <if test="category != null">category,</if>
            <if test="profile != null">profile,</if>
            <if test="source != null and source != ''">source,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            0,
            <if test="weaponryCode != null and weaponryCode != ''">#{weaponryCode},</if>
            <if test="armamentCode != null">#{armamentCode},</if>
            <if test="armamentNameCn != null">#{armamentNameCn},</if>
            <if test="armamentNameEn != null">#{armamentNameEn},</if>
            <if test="category != null">#{category},</if>
            <if test="profile != null">#{profile},</if>
            <if test="source != null and source != ''">#{source},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateIdwWeaponryArmament" parameterType="com.lirong.weaponry.armament.domain.IdwWeaponryArmament">
        update idw_weaponry_armament
        <trim prefix="SET" suffixOverrides=",">
            <if test="weaponryCode != null">weaponry_code = #{weaponryCode},</if>
            <if test="armamentCode != null">armament_code = #{armamentCode},</if>
            <if test="armamentNameCn != null">armament_name_cn = #{armamentNameCn},</if>
            <if test="armamentNameEn != null">armament_name_en = #{armamentNameEn},</if>
            <if test="category != null">category = #{category},</if>
            <if test="profile != null">profile = #{profile},</if>
            <if test="source != null">source = #{source},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where armament_id = #{armamentId}
    </update>

    <!--根据武器装备编码删除-->
    <update id="deleteByWeaponryCodes">
        update idw_weaponry_armament
        SET update_by = #{loginName},
        update_time = sysdate(),
        weaponry_code = CONCAT( #{deleteTime} , '-' , #{loginName} , '-' , weaponry_code ),
        IS_DELETE = 1
        WHERE weaponry_code in
        <foreach item="weaponryCode" collection="weaponryCodes" open="(" separator="," close=")">
            #{weaponryCode}
        </foreach>
    </update>

    <update id="deleteIdwWeaponryArmamentByIds">
        update idw_weaponry_armament
        SET update_by = #{loginName},
        update_time = sysdate(),
        IS_DELETE = 1
        WHERE armament_id in
        <foreach item="armamentId" collection="armamentIds" open="(" separator="," close=")">
            #{armamentId}
        </foreach>

    </update>

</mapper>