<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('武器装备主题库列表')" />
    <th:block th:include="include :: layout-latest-css"/>
    <th:block th:include="include :: select2-css" />
    <th:block th:include="include :: ztree-css"/>
</head>
<body class="gray-bg">
    <div class="ui-layout-west">
        <div class="box box-main">
            <div class="box-header">
                <div class="box-title">
                    <i class="fa icon-grid"></i> 主题库
                </div>
                <div class="box-tools pull-right">
                    <button type="button" class="btn btn-box-tool" onclick="addTheme()" title="添加顶层主题"><i class="fa fa-plus"></i></button>
                </div>
            </div>
            <div class="ui-layout-content">
                <div class="row">
                    <div class="form-group">
                        <div class="col-sm-8">
                            <input type="text" id="keyword">
                        </div>
                        <div class="col-sm-4">
                            <button type="button" class="btn btn-box-tool" onclick="searchTree()" title="搜索"><i class="fa fa-search"></i></button>
                        </div>
                    </div>
                </div>
                <div id="tree" class="ztree"></div>
            </div>
        </div>
    </div>

    <div class="ui-layout-center">
        <div class="container-div">
            <div class="row">
                <div class="col-sm-12 search-collapse">
                    <form id="form-weaponrt">
                        <input type="hidden" name="themeId" id="themeId"/>
                        <div class="select-list">
                            <ul>
                                <li>
                                    <label style="width: 100px;">国家/地区：</label>
                                    <select class="form-control" name="country" th:with="type=${@dict.getType('sys_country')}">
                                        <option value="">所有</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                                th:value="${dict.dictValue}"></option>
                                    </select>
                                </li>
                                <li>
                                    <label>装备名称：</label>
                                    <input type="text" name="weaponryName"/>
                                </li>
                                <li>
                                    <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search('form-weaponrt', 'bootstrap-table-weaponrt')"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                    <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('form-weaponrt', 'bootstrap-table-weaponrt')"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                                </li>
                            </ul>
                        </div>
                    </form>
                </div>

                <div class="btn-group-sm" id="toolbar-weaponrt" role="group">
                    <a class="btn btn-success" onclick="relevancyWeaponr()" shiro:hasPermission="weaponry:theme:add">
                        <i class="fa fa-exchange"></i> 关联装备
                    </a>
                    <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="weaponry:theme:remove">
                        <i class="fa fa-remove"></i> 删除
                    </a>
                </div>
                <div class="col-sm-12 select-table table-striped">
                    <table id="bootstrap-table-weaponrt"></table>
                </div>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: ztree-js"/>
    <th:block th:include="include :: select2-js" />
    <th:block th:include="include :: layout-latest-js"/>
    <script th:inline="javascript">
        let removeFlag = [[${@permission.hasPermi('weaponry:theme:remove')}]];
        let prefix = ctx + "weaponry/theme";

        $(function () {
            let panehHidden = false;
            if ($(this).width() < 769) {
                panehHidden = true;
            }
            $('body').layout({initClosed: panehHidden, west__size: 300});
            // 回到顶部绑定
            if ($.fn.toTop !== undefined) {
                var opt = {
                    win: $('.ui-layout-center'),
                    doc: $('.ui-layout-center')
                };
                queryWeaponryList();
                $('#scroll-up').toTop(opt);
            }
        })

        //加载数据列表
        var countryDatas = [[${@dict.getType('sys_country')}]];
        function queryWeaponryList() {
            let options = {
                id: "bootstrap-table-weaponrt",          // 指定表格ID
                toolbar: "toolbar-weaponrt",   // 指定工具栏ID
                formId: "form-weaponrt",
                url: prefix + "/weaponry",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/removeAffiliation",
                modalName: "武器装备",
                uniqueId: "affiliationId",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", $.table.serialNumber(index));
                        return columnIndex + $.table.serialNumber(index);
                    }
                },
                {
                    field: 'country',
                    title: '国家/地区',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(countryDatas, value);
                    }
                },
                {
                    field: 'themeName',
                    title: '所属主题'
                },
                {
                    field: 'avatar',
                    title: '缩略图',
                    width: 80,
                    formatter: function (value, row, index) {
                        if (value != null && value != '') {
                            return $.table.imageView(value, 300, 300);
                        } else {
                            return $.table.imageView('/img/default.png', 300, 300);
                        }
                    }
                },
                {
                    field: 'weaponryCode',
                    title: '装备编码'
                },
                {
                    field: 'nameCn',
                    title: '武器装备名称'
                },
                {
                    field: 'firstTime',
                    title: '首次运行时间'
                },
                {
                    width: 220,
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        let actions = [];
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.affiliationId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        }

        //关联装备
         function relevancyWeaponr() {
            let themeId = $('#themeId').val();
            if (themeId != null && themeId != '' && themeId != undefined){
                let options = {
                    title: '关联装备',
                    width: "380",
                    url: prefix + "/relevancyWeaponr/" + themeId,
                    callBack: doSubmit
                };
                $.modal.openOptions(options);
            } else {
                $.modal.msgWarning('请先选择主题！')
            }
        }

        function doSubmit(index, layero){
            let body = layer.getChildFrame('body', index);
            let themeId = $('#themeId').val();
            let insertWeaponryCodes = body.find('#insertWeaponryCodes').val();
            let deleteWeaponryCodes = body.find('#deleteWeaponryCodes').val();
            if (insertWeaponryCodes !== '' || deleteWeaponryCodes !== '') {
                //根据主题ID&装备编码关联/删除装备关系
                $.ajax({
                    type: "POST",
                    url: prefix + "/editRelationship",
                    data: {
                        'themeId': themeId,
                        'insertWeaponryCode': insertWeaponryCodes,
                        'deleteWeaponryCode': deleteWeaponryCodes
                    },
                    success: function(result) {
                        //删除/新增关系完成
                        $.modal.msgSuccess('关联成功！')
                        $.table.search('form-weaponrt', 'bootstrap-table-weaponrt')
                    },
                    error: function(error) {
                        $.modal.alertWarning(error.msg);
                    }
                });
            }
            layer.close(index);
        }

        var setting = {
            view: {
                addHoverDom: addHoverDom,
                removeHoverDom: removeHoverDom,
                selectedMulti: false
            },
            edit: {
                enable: true,
                editNameSelectAll: true,
                showRemoveBtn: showRemoveBtn,
                showRenameBtn: showRenameBtn
            },
            data: {
                simpleData: {
                    enable: true
                }
            },
            callback: {
                beforeDrag: beforeDrag,
                beforeEditName: beforeEditName,
                beforeRemove: beforeRemove,
                beforeRename: beforeRename,
                onRemove: onRemove,
                onRename: onRename,
                onClick: onClick
            }
        };

        var zNodes;
        $.ajax({
            type: "GET",
            url: ctx + "weaponry/theme/buildThemeTree",
            async: false,
            success: function(result) {
                if (result.code == web_status.SUCCESS) {
                    zNodes = result.data
                }
            }
        });

        var log, className = "dark";

        //用于捕获节点被拖拽之前的事件回调函数，并且根据返回值确定是否允许开启拖拽操作
        function beforeDrag(treeId, treeNodes) {
            return false;
        }
        //用于捕获节点编辑按钮的 click 事件，并且根据返回值确定是否允许进入名称编辑状态
        function beforeEditName(treeId, treeNode) {
            className = (className === "dark" ? "":"dark");
            var zTree = $.fn.zTree.getZTreeObj("tree");
            zTree.selectNode(treeNode);
            return true;
        }
        //用于捕获节点被删除之前的事件回调函数，并且根据返回值确定是否允许删除操作
        function beforeRemove(treeId, treeNode) {
            className = (className === "dark" ? "":"dark");
            var zTree = $.fn.zTree.getZTreeObj("tree");
            zTree.selectNode(treeNode);
            return confirm('确定删除：' + treeNode.name + '及其子节点吗？');
        }
        //用于捕获删除节点之后的事件回调函数
        function onRemove(e, treeId, treeNode) {
            //删除
            $.ajax({
                type: "GET",
                url: ctx + "weaponry/theme/remove/" + treeNode.id,
                success: function(result) {
                    if (result.code == web_status.SUCCESS) {
                        $.modal.msgSuccess('删除成功')
                        $.table.search('form-weaponrt', 'bootstrap-table-weaponrt')
                    }
                }
            });
        }
        //用于捕获节点编辑名称结束（Input 失去焦点 或 按下 Enter 键）之后，更新节点名称数据之前的事件回调函数，并且根据返回值确定是否允许更改名称的操作
        function beforeRename(treeId, treeNode, newName, isCancel) {
            oldName = treeNode.name;
            className = (className === "dark" ? "":"dark");
            if (newName.length == 0) {
                setTimeout(function() {
                    var zTree = $.fn.zTree.getZTreeObj("tree");
                    zTree.cancelEditName();
                    $.modal.alertError('主题名称不能为空！')
                }, 0);
                return false;
            }
        }
        let oldName;
        //校验主题是否存在 同一主题下一层名称唯一
        function verifyThemeIsExist(parentId, newName, themeId ) {
            $.ajax({
                type: "GET",
                url: ctx + "weaponry/theme/verify/" + (parentId != null && parentId != '' ? parentId : 0) + '/' + encodeURI(encodeURI(newName)) + '/' + themeId,
                async: false,
                success: function(result) {
                    if (result.code == web_status.SUCCESS) {
                        //修改
                        $.ajax({
                            type: "POST",
                            url: ctx + "weaponry/theme/update",
                            data: {
                                'themeId': themeId,
                                'name': newName
                            },
                            success: function(result) {
                                if (result.code == web_status.SUCCESS) {
                                    $.modal.msgSuccess('修改成功！')
                                }
                            }
                        });
                    } else {
                        $.modal.alertWarning(result.msg);
                        let zTree = $.fn.zTree.getZTreeObj("tree");
                        let nodes = zTree.getSelectedNodes();
                        for (let i = 0; i < nodes.length; i++) {
                            nodes[i].name = oldName;
                            zTree.updateNode(nodes[i]);
                        }
                    }
                },
                error: function(error) {
                    $.modal.alertWarning(error.msg);
                    $.modal.alertWarning(result.msg);
                    let zTree = $.fn.zTree.getZTreeObj("tree");
                    let nodes = zTree.getSelectedNodes();
                    for (let i = 0; i < nodes.length; i++) {
                        nodes[i].name = oldName;
                        zTree.updateNode(nodes[i]);
                    }
                }
            });
        }

        //用于捕获节点编辑名称结束之后的事件回调函数
        function onRename(e, treeId, treeNode, isCancel) {
            verifyThemeIsExist(treeNode.pId, treeNode.name, treeNode.id)
        }

        /**
         * 节点点击事件
         */
        function onClick(event, treeId, treeNode) {
            $('#themeId').val(treeNode.id)
            $.table.search('form-weaponrt', 'bootstrap-table-weaponrt')
        }
        //设置是否显示删除按钮。[setting.edit.enable = true 时生效]
        function showRemoveBtn(treeId, treeNode) {
            return true;//!treeNode.isFirstNode;
        }
        //设置是否显示编辑名称按钮。[setting.edit.enable = true 时生效]
        function showRenameBtn(treeId, treeNode) {
            return true;//!treeNode.isLastNode;
        }

        var newCount = 1;
        //用于当鼠标移动到节点上时，显示用户自定义控件，显示隐藏状态同 zTree 内部的编辑、删除按钮
        function addHoverDom(treeId, treeNode) {
            var sObj = $("#" + treeNode.tId + "_span");
            if (treeNode.editNameFlag || $("#addBtn_"+treeNode.tId).length>0) return;
            var addStr = "<span class='button add' id='addBtn_" + treeNode.tId + "' title='add node' onfocus='this.blur();'></span>";
            sObj.after(addStr);
            var btn = $("#addBtn_"+treeNode.tId);
            if (btn) btn.bind("click", function(){
                //新增节点
                var zTree = $.fn.zTree.getZTreeObj("tree");
                layer.prompt({title: '请输入主题名称，并确认', formType: 3}, function(text, index){
                    //校验主题是否存在 同一主题下一层名称唯一
                    $.ajax({
                        type: "GET",
                        url: ctx + "weaponry/theme/verify/" + treeNode.id + '/' + encodeURI(encodeURI(text)),
                        success: function(result) {
                            if (result.code == web_status.SUCCESS) {
                                $.ajax({//新增
                                    type: "POST",
                                    url: ctx + "weaponry/theme/add",
                                    data: {
                                        'nameCn': text,
                                        'parentId': treeNode.id,
                                        'ancestors': treeNode.ancestors
                                    },
                                    success: function(result) {
                                        if (result.code == web_status.SUCCESS) {
                                            zTree.addNodes(treeNode, {id: result.data, pId:treeNode.id, name: text, ancestors: treeNode.ancestors + ',' + treeNode.id});
                                            layer.close(index);
                                            $.modal.msgSuccess('添加成功！')
                                        }
                                    }
                                });
                            } else {
                                layer.close(index);
                                $.modal.alertWarning(result.msg);
                            }
                        },
                        error: function(error) {
                            layer.close(index);
                            $.modal.alertWarning(error.msg);
                        }
                    });
                });
                return false;
            });
        };

        //新增顶层节点
        function addTheme() {
            layer.prompt({title: '请输入主题名称，并确认', formType: 3}, function(text, index){
                //校验主题是否存在 同一主题下一层名称唯一
                $.ajax({
                    type: "GET",
                    url: ctx + "weaponry/theme/verify/0" + '/' + encodeURI(encodeURI(text)),
                    success: function(result) {
                        if (result.code == web_status.SUCCESS) {
                            $.ajax({
                                type: "POST",
                                url: ctx + "weaponry/theme/add",
                                data: {
                                    'nameCn': text
                                },
                                success: function(result) {
                                    if (result.code == web_status.SUCCESS) {
                                        $.fn.zTree.getZTreeObj("tree").addNodes(null, {id: result.data, pId: '0', name: text, ancestors: '0'});
                                        layer.close(index);
                                        $.modal.msgSuccess('添加成功！')
                                    }
                                }
                            });
                        } else {
                            layer.close(index);
                            $.modal.alertWarning(result.msg);
                        }
                    },
                    error: function(error) {
                        layer.close(index);
                        $.modal.alertWarning(error.msg);
                    }
                });
            });
        }

        //用于当鼠标移出节点时，隐藏用户自定义控件，显示隐藏状态同 zTree 内部的编辑、删除按钮
        function removeHoverDom(treeId, treeNode) {
            $("#addBtn_"+treeNode.tId).unbind().remove();
        };

        function selectAll() {
            var zTree = $.fn.zTree.getZTreeObj("tree");
            zTree.setting.edit.editNameSelectAll =  $("#selectAll").attr("checked");
        }

        $(document).ready(function(){
            $.fn.zTree.init($("#tree"), setting, zNodes);
            $("#selectAll").bind("click", selectAll);
        });


        //input框变化时查询节点
        function searchTree(){
            let value = $('#keyword').val();
            if (value != null && value != '' && value != undefined){
                var treeObj = $.fn.zTree.getZTreeObj("tree");
                var nodes = treeObj.getNodesByParamFuzzy("name", value, null);
                if (nodes.length>0) {
                    treeObj.selectNode(nodes[0]);
                }
            }
        }

    </script>
</body>
</html>