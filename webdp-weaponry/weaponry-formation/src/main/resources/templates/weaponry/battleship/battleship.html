<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('武器装备航母编队舰船列表')" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="form-battleship">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>航母编队ID：</label>
                                <input type="text" name="formationId"/>
                            </li>
                            <li>
                                <label>舷号：</label>
                                <input type="text" name="hullNo"/>
                            </li>
                            <li>
                                <label>是否删除，0-未删除，1-已删除：</label>
                                <input type="text" name="isDelete"/>
                            </li>
                            <li>
                                <label>舰船中文名称：</label>
                                <input type="text" name="nameCn"/>
                            </li>
                            <li>
                                <label>舰船英文名称：</label>
                                <input type="text" name="nameEn"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search('form-battleship', 'bootstrap-table-battleship')"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('form-battleship', 'bootstrap-table-battleship')"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar-battleship" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="weaponry:battleship:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="weaponry:battleship:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="weaponry:battleship:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="weaponry:battleship:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table-battleship"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        let editFlag = [[${@permission.hasPermi('weaponry:battleship:edit')}]];
        let removeFlag = [[${@permission.hasPermi('weaponry:battleship:remove')}]];
        let prefix = ctx + "weaponry/battleship";

        $(function() {
            let options = {
                id: "bootstrap-table-battleship",          // 指定表格ID
                toolbar: "toolbar-battleship",   // 指定工具栏ID
                formId: "form-battleship",
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "武器装备航母编队舰船",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'shipId',
                    title: '武器装备航母编队舰船ID',
                    visible: false
                },
                {
                    field: 'formationId',
                    title: '航母编队ID'
                },
                {
                    field: 'hullNo',
                    title: '舷号'
                },
                {
                    field: 'isDelete',
                    title: '是否删除，0-未删除，1-已删除'
                },
                {
                    field: 'nameCn',
                    title: '舰船中文名称'
                },
                {
                    field: 'nameEn',
                    title: '舰船英文名称'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        let actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.shipId + '\')"><i class="fa fa-edit"></i>修改</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.shipId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>