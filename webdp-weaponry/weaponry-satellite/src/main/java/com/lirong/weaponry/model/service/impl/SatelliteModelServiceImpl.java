package com.lirong.weaponry.model.service.impl;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.lirong.common.config.WebdpConfig;
import com.lirong.common.constant.Constants;
import com.lirong.common.core.text.Convert;
import com.lirong.common.utils.*;
import com.lirong.common.utils.file.FileUploadUtils;
import com.lirong.common.utils.file.FileUtils;
import com.lirong.multimedia.domain.IdwMultimedia;
import com.lirong.multimedia.mapper.IdwMultimediaMapper;
import com.lirong.organization.common.domain.IdwOrg;
import com.lirong.organization.common.mapper.IdwOrgMapper;
import com.lirong.weaponry.classification.domain.WeaponryClassification;
import com.lirong.weaponry.classification.mapper.WeaponryClassificationMapper;
import com.lirong.weaponry.contractor.domain.IdwWeaponryContractor;
import com.lirong.weaponry.contractor.mapper.IdwWeaponryContractorMapper;
import com.lirong.weaponry.model.service.SatelliteModelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lirong.weaponry.model.mapper.SatelliteModelMapper;
import com.lirong.weaponry.model.domain.SatelliteModel;

/**
 * 卫星型号Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-09-18
 */
@Service
public class SatelliteModelServiceImpl implements SatelliteModelService {
    @Autowired//机构
    private IdwOrgMapper idwOrgMapper;
    @Autowired
    private SatelliteModelMapper satelliteModelMapper;
    @Autowired
    private IdwMultimediaMapper multimediaMapper;
    @Autowired
    private IdwWeaponryContractorMapper weaponryContractorMapper;
    @Autowired//武器装备分类
    private WeaponryClassificationMapper weaponryClassificationMapper;

    /**
     * 查询卫星型号
     *
     * @param modelId 卫星型号ID
     * @return 卫星型号
     */
    @Override
    public SatelliteModel selectSatelliteModelById(Long modelId) {
        return satelliteModelMapper.selectSatelliteModelById(modelId);
    }

    /**
     * 查询卫星型号列表
     *
     * @param satelliteModel 卫星型号
     * @return 卫星型号
     */
    @Override
    public List<SatelliteModel> selectSatelliteModelList(SatelliteModel satelliteModel) {
        return satelliteModelMapper.selectSatelliteModelList(satelliteModel);
    }

    /**
     * 新增卫星型号
     *
     * @param satelliteModel 卫星型号
     * @return 结果
     */
    @Override
    public int insertSatelliteModel(SatelliteModel satelliteModel) {
        String userName = ShiroUtils.getUserName();
        Date nowDate = DateUtils.getNowDate();
        long id = SnowIdUtils.uniqueLong();
        satelliteModel.setModelId(id);//赋值雪花ID
        satelliteModel.setWeaponryCode("model_" + id);
        satelliteModel.setCreateBy(userName);
        satelliteModel.setCreateTime(nowDate);
        //承包商
        String developmentStage = DictUtils.getDictValue("sys_development_stage", "建造");
        String contractor = "";
        String[] contractorCode = satelliteModel.getContractorCode();
        String[] contractorNameCn = satelliteModel.getContractorNameCn();
        String[] contractorNameEn = satelliteModel.getContractorNameEn();
        if (StringUtils.isNotNull(contractorNameCn) && contractorNameCn.length > 0) {
            for (int i = 0; i < contractorNameCn.length; i++) {
                String nameCn = contractorNameCn[i];
                if (StringUtils.isNotBlank(nameCn)) {
                    if (StringUtils.isNotBlank(contractor)) {
                        contractor += "," + nameCn;
                    } else {
                        contractor = nameCn;
                    }
                    IdwWeaponryContractor weaponryContractor = new IdwWeaponryContractor();
                    weaponryContractor.setWeaponryCode(satelliteModel.getWeaponryCode());
                    weaponryContractor.setStructureCode("0");
                    weaponryContractor.setOrgCode(StringUtils.isNotNull(contractorCode) && contractorCode.length > i ? contractorCode[i] : "");
                    weaponryContractor.setOrgNameCn(nameCn);
                    weaponryContractor.setOrgNameEn(StringUtils.isNotNull(contractorNameEn) && contractorNameEn.length > i ? contractorNameEn[i] : "");
                    weaponryContractor.setDevelopmentStage(developmentStage);
                    weaponryContractor.setSource(satelliteModel.getSource());
                    weaponryContractor.setCreateBy(userName);
                    weaponryContractor.setCreateTime(nowDate);
                    weaponryContractorMapper.insertIdwWeaponryContractor(weaponryContractor);
                }
            }
        }
        return satelliteModelMapper.insertSatelliteModel(satelliteModel);
    }

    /**
     * 修改卫星型号
     *
     * @param satelliteModel 卫星型号
     * @return 结果
     */
    @Override
    public int updateSatelliteModel(SatelliteModel satelliteModel) {
        String weaponryCode = satelliteModel.getWeaponryCode();
        String userName = ShiroUtils.getUserName();
        Date nowDate = DateUtils.getNowDate();
        satelliteModel.setUpdateBy(userName);
        satelliteModel.setUpdateTime(nowDate);
        //承包商
        String developmentStage = DictUtils.getDictValue("sys_development_stage", "建造");
        String contractor = "";
        Long[] contractorIds = satelliteModel.getContractorId();
        Boolean[] isUpdateContractors = satelliteModel.getIsUpdateContractor();
        String[] contractorCodeArr = satelliteModel.getContractorCode();
        String[] contractorNameCnArr = satelliteModel.getContractorNameCn();
        String[] contractorNameEnArr = satelliteModel.getContractorNameEn();
        if (StringUtils.isNotNull(contractorNameCnArr) && contractorNameCnArr.length > 0) {
            for (int i = 0; i < contractorNameCnArr.length; i++) {
                String nameCn = contractorNameCnArr[i];
                if (StringUtils.isNotBlank(nameCn)) {
                    String contractorCode = "";
                    if (StringUtils.isNotNull(contractorCodeArr) && contractorCodeArr.length > i) {
                        contractorCode = contractorCodeArr[i];
                    }
                    String contractorNameEn = "";
                    if (StringUtils.isNotNull(contractorNameEnArr) && contractorNameEnArr.length > i) {
                        contractorNameEn = contractorNameEnArr[i];
                    }
                    if (StringUtils.isNotNull(contractorIds) && contractorIds.length > i && StringUtils.isNotNull(contractorIds[i]) && StringUtils.isNotNull(isUpdateContractors[i]) && isUpdateContractors[i]) {
                        if (StringUtils.isNotBlank(contractor)) {
                            contractor += "," + nameCn;
                        } else {
                            contractor = nameCn;
                        }
                        //更新
                        IdwWeaponryContractor weaponryContractor = new IdwWeaponryContractor();
                        weaponryContractor.setContractorId(contractorIds[i]);
                        weaponryContractor.setWeaponryCode(weaponryCode);
                        weaponryContractor.setStructureCode("0");
                        weaponryContractor.setOrgCode(contractorCode);
                        weaponryContractor.setOrgNameCn(nameCn);
                        weaponryContractor.setOrgNameEn(contractorNameEn);
                        weaponryContractor.setDevelopmentStage(developmentStage);
                        weaponryContractor.setSource(satelliteModel.getSource());
                        weaponryContractor.setUpdateBy(userName);
                        weaponryContractor.setUpdateTime(nowDate);
                        weaponryContractorMapper.updateIdwWeaponryContractor(weaponryContractor);
                    } else if (StringUtils.isNotBlank(nameCn) && (StringUtils.isNull(contractorIds) || contractorIds.length <= i || StringUtils.isNull(contractorIds[i]))) {
                        if (StringUtils.isNotBlank(contractor)) {
                            contractor += "," + nameCn;
                        } else {
                            contractor = nameCn;
                        }
                        //新增
                        IdwWeaponryContractor weaponryContractor = new IdwWeaponryContractor();
                        weaponryContractor.setWeaponryCode(weaponryCode);
                        weaponryContractor.setStructureCode("0");
                        weaponryContractor.setOrgCode(contractorCode);
                        weaponryContractor.setOrgNameCn(nameCn);
                        weaponryContractor.setOrgNameEn(contractorNameEn);
                        weaponryContractor.setDevelopmentStage(developmentStage);
                        weaponryContractor.setSource(satelliteModel.getSource());
                        weaponryContractor.setCreateBy(userName);
                        weaponryContractor.setCreateTime(nowDate);
                        weaponryContractorMapper.insertIdwWeaponryContractor(weaponryContractor);
                    }
                }
            }
        }
        //删除承包商
        long[] deleteContractorId = satelliteModel.getDeleteContractorId();
        if (StringUtils.isNotNull(deleteContractorId) && deleteContractorId.length > 0) {
            weaponryContractorMapper.deleteByContractorIds(deleteContractorId, userName);
        }
        return satelliteModelMapper.updateSatelliteModel(satelliteModel);
    }

    /**
     * 查询卫星型号当前最大的排序号
     *
     * @return 当前卫星型号最大的排序号
     */
    @Override
    public Integer selectSatelliteModelMaxOrderNum() {
        return satelliteModelMapper.selectSatelliteModelMaxOrderNum();
    }

    /**
     * 根据卫星型号装备编码查询
     *
     * @param weaponryCode 卫星型号装备编码
     * @return 结果
     */
    @Override
    public SatelliteModel selectSatelliteModelByWeaponryCode(String weaponryCode) {
        return satelliteModelMapper.selectSatelliteModelByWeaponryCode(weaponryCode);
    }

    /**
     * 导入校验数据格式
     *
     * @param satelliteModelList 卫星型号数据列表
     * @param filePathIndexMap   key 文件在压缩包中的相对路径 value 文件对应在filePathList中的索引
     * @param filePathList       上传后的文件路径
     * @param baseDir            临时文件夹目录
     * @param updateSupport      是否更新
     */
    @Override
    public List<String> verifyImportSatelliteModel(List<SatelliteModel> satelliteModelList, Map<String, Integer> filePathIndexMap, List<String> filePathList, String baseDir, boolean updateSupport) {
        if (StringUtils.isNull(satelliteModelList) || satelliteModelList.size() < 1) {
            return null;
        }
        //处理完成卫星型号数据列表
        List<SatelliteModel> treatingAfterSatelliteModelList = new ArrayList<>();
        int row = 1;
        boolean isFailure = false;
        //需要拷贝的文件路径
        List<String> temporaryFilePathList = new ArrayList<>();
        //拷贝文件目标路径
        List<String> newFilePathList = new ArrayList<>();
        List<String> msgList = new ArrayList<>();
        String userName = ShiroUtils.getUserName();
        //武器装备编码
        List<String> weaponryCodeList = (List<String>) CacheUtils.get("weaponryImportTreatingAfterWeaponryCodeList-" + userName);
        if (StringUtils.isNull(weaponryCodeList)) {
            weaponryCodeList = new ArrayList<>();
        }
        for (SatelliteModel satelliteModel : satelliteModelList) {
            row++;
            if (StringUtils.isNotNull(satelliteModel)) {
                //校验数据是否存在
                SatelliteModel oldSatelliteModel = satelliteModelMapper.selectSatelliteModelBySatelliteNameAndSatelliteModelName(satelliteModel.getSatelliteNameCn(), satelliteModel.getSatelliteNameEn(), satelliteModel.getSatelliteModelNameCn(), satelliteModel.getSatelliteModelNameEn());
                if (StringUtils.isNull(oldSatelliteModel)) {
                    //如果装备编码为空，认为该数据为新增数据
                    long id = SnowIdUtils.uniqueLong();
                    satelliteModel.setModelId(id);//赋值雪花ID
                    satelliteModel.setWeaponryCode("model_" + id);
                } else {
                    satelliteModel.setModelId(oldSatelliteModel.getModelId());
                }
                if (StringUtils.isBlank(satelliteModel.getCountry())) {
                    isFailure = true;
                    msgList.add("卫星型号,第" + row + "行," + " 国家/地区为空");
                } else {
                    String country = DictUtils.getDictValue("sys_country", satelliteModel.getCountry());
                    if (StringUtils.isNotBlank(country)) {
                        satelliteModel.setCountry(country);
                    }
                }
                if (StringUtils.isBlank(satelliteModel.getSatelliteNameCn())) {
                    isFailure = true;
                    msgList.add("卫星型号,第" + row + "行," + " 卫星中文名称为空");
                }
                if (StringUtils.isBlank(satelliteModel.getSatelliteModelNameCn())) {
                    isFailure = true;
                    msgList.add("卫星型号,第" + row + "行," + " 卫星型号中文名称为空");
                }

                if (StringUtils.isBlank(satelliteModel.getSource())) {
                    isFailure = true;
                    msgList.add("卫星型号,第" + row + "行," + " 数据来源为空");
                }
                //判断卫星型号分类名称是否为空
                if (StringUtils.isNotBlank(satelliteModel.getClassificationName())) {
                    WeaponryClassification weaponryClassification = weaponryClassificationMapper.selectByName(satelliteModel.getClassificationName().trim());
                    if (StringUtils.isNotNull(weaponryClassification)) {
                        //校验装备分类是否为底层分类
                        int count = weaponryClassificationMapper.selectChildrenCountByClassificationId(weaponryClassification.getClassificationId());
                        if (count > 0) {
                            //分类不为底层节点
                            isFailure = true;
                            msgList.add("卫星型号,第" + row + "行," + " 请选择底层分类");
                        } else {
                            //分类名称存在 赋值分类编码
                            satelliteModel.setClassificationCode(weaponryClassification.getClassificationCode());
                        }
                    } else {
                        //分类名称不存在
                        isFailure = true;
                        msgList.add("卫星型号,第" + row + "行," + " 分类名称不存在");
                    }
                }

                //校验图片
                String avatars = satelliteModel.getPicture();
                List<String> pictureNameList = new ArrayList<>();
                List<String> picturePathList = new ArrayList<>();
                List<String> pictureMd5List = new ArrayList<>();
                if (StringUtils.isNotBlank(avatars)) {
                    String[] avatarArray = avatars.split(";|；|，|,|、|\n|\n\r");
                    for (String avatar : avatarArray) {
                        if (avatar.contains(Constants.RESOURCE_PREFIX)) {
                            String avatarFilePath = avatar.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getWeaponryPath());
                            if (!new File(avatarFilePath).exists()) {
                                isFailure = true;
                                msgList.add("卫星型号,第" + row + "行," + " 图片（" + avatar + "）不存在");
                            }
                        } else {
                            //替换文件名称中的特殊字符
                            avatar = FileUploadUtils.replaceFileNameSpecialCharacter(avatar).toLowerCase();
                            if (StringUtils.isNotNull(filePathIndexMap) && filePathIndexMap.size() > 0) {
                                Integer index = filePathIndexMap.get(avatar);
                                //不存在 匹配文件名称
                                if (StringUtils.isNull(index)) {
                                    String excelFileName = StringUtils.getFileName(avatar);
                                    //使用文件名称匹配
                                    for (String key : filePathIndexMap.keySet()) {
                                        String fileName = StringUtils.getFileName(key);
                                        if (excelFileName.equals(fileName)) {
                                            index = filePathIndexMap.get(key);
                                            break;
                                        }
                                    }
                                }
                                if (StringUtils.isNotNull(index)) {
                                    //当数据存在但是支持更新或数据不存在时处理图片
                                    if ((StringUtils.isNotNull(oldSatelliteModel) && updateSupport) || StringUtils.isNull(oldSatelliteModel)) {
                                        //获取文件名称
                                        String temporaryFilePath = filePathList.get(index);
                                        String[] filePathArr = temporaryFilePath.split("/");
                                        //根据文件名称与纬度路径生成新的文件路径
                                        String newFilePath = FileUploadUtils.generateFilePath(WebdpConfig.getWeaponryPath(), filePathArr[filePathArr.length - 1]);
                                        //保存文件路径
                                        temporaryFilePathList.add(temporaryFilePath);
                                        //保存新的文件路径
                                        newFilePathList.add(newFilePath);
                                        //赋值新图片路径
                                        pictureNameList.add(avatar);
                                        picturePathList.add(newFilePath);
                                        String md5 = FileUtils.getMd5(temporaryFilePath);
                                        pictureMd5List.add(md5);
                                    }
                                } else {
                                    isFailure = true;
                                    msgList.add("卫星型号,第" + row + "行," + " 图片（" + avatar + "）不存在");
                                }
                            } else {
                                isFailure = true;
                                msgList.add("卫星型号,第" + row + "行," + " 图片不为空且压缩包文件为空");
                            }
                        }
                    }
                }
                if (pictureNameList.size() > 0) {
                    satelliteModel.setPictureNameList(pictureNameList);
                    satelliteModel.setPicturePathList(picturePathList);
                    satelliteModel.setPictureMd5List(pictureMd5List);
                }

                treatingAfterSatelliteModelList.add(satelliteModel);
                weaponryCodeList.add(satelliteModel.getWeaponryCode());
            }
        }

        CacheUtils.put("weaponryImportTreatingAfterWeaponryCodeList-" + userName, weaponryCodeList);
        if (isFailure) {
            return msgList;
        } else {
            CacheUtils.put("weaponryImportTreatingTemporaryFilePathList-" + userName, temporaryFilePathList);
            CacheUtils.put("weaponryImportTreatingNewFilePathList-" + userName, newFilePathList);
            CacheUtils.put("weaponryImportTreatingAfterSatelliteModelList-" + userName, treatingAfterSatelliteModelList);
        }
        return null;
    }

    /**
     * 导入卫星型号
     *
     * @param updateSupport 是否支持更新, 如果已存在, 则进行更新
     * @param operName      操作用户
     * @return 结果
     */
    @Override
    public String importSatelliteModel(boolean updateSupport, String operName) {
        Date nowDate = DateUtils.getNowDate();
        long insertCount = 0;
        long updateCount = 0;
        List<SatelliteModel> satelliteModelList = (List<SatelliteModel>) CacheUtils.get("weaponryImportTreatingAfterSatelliteModelList-" + operName);
        if (StringUtils.isNull(satelliteModelList) || satelliteModelList.size() < 1) {
            return null;
        }
        Integer satelliteModelMaxOrderNum = satelliteModelMapper.selectSatelliteModelMaxOrderNum();
        for (SatelliteModel satelliteModel : satelliteModelList) {
            if (StringUtils.isNotNull(satelliteModel)) {
                //赋值图片
                if (StringUtils.isNotNull(satelliteModel.getPicturePathList()) && satelliteModel.getPicturePathList().size() > 0) {
                    satelliteModel.setPicture(satelliteModel.getPicturePathList().get(0));
                }
                if (StringUtils.isNotBlank(satelliteModel.getWeaponryCode())) {
                    //新增
                    insertCount++;
                    //排序号
                    if (StringUtils.isNull(satelliteModel.getOrderNum())) {
                        satelliteModel.setOrderNum(satelliteModelMaxOrderNum++);
                    }
                    satelliteModel.setCreateBy(operName);
                    satelliteModel.setCreateTime(nowDate);
                    satelliteModelMapper.insertSatelliteModel(satelliteModel);
                } else if (updateSupport) {
                    //修改
                    updateCount++;
                    satelliteModel.setUpdateBy(operName);
                    satelliteModel.setUpdateTime(nowDate);
                    satelliteModelMapper.updateSatelliteModel(satelliteModel);
                }
                String weaponryCode = satelliteModelMapper.selectSatelliteModelById(satelliteModel.getModelId()).getWeaponryCode();
                //图片视频
                List<String> pictureNameList = satelliteModel.getPictureNameList();
                List<String> picturePathList = satelliteModel.getPicturePathList();
                List<String> pictureMd5List = satelliteModel.getPictureMd5List();
                if (StringUtils.isNotNull(pictureNameList) && pictureNameList.size() > 0) {
                    //删除原有数据
                    multimediaMapper.deleteByBusinessTypeAndAssociationIds("weaponry", Convert.toStrArray(weaponryCode), operName, nowDate.toString());
                    for (int i = 0; i < pictureNameList.size(); i++) {
                        String fileName = pictureNameList.get(i);
                        String filePath = picturePathList.get(i);
                        String fileMd5 = pictureMd5List.get(i);
                        //获取后缀
                        String extension = filePath.substring(filePath.lastIndexOf(".") + 1);
                        IdwMultimedia multimedia = new IdwMultimedia();
                        multimedia.setMediaType(extension);
                        multimedia.setBusinessType("weaponry");
                        multimedia.setAssociationId(weaponryCode);
                        multimedia.setTitle(fileName);
                        multimedia.setThumbnail(filePath);
                        multimedia.setMd5(fileMd5);
                        multimedia.setStoragePath(filePath);
                        multimedia.setSource(satelliteModel.getSource());
                        multimedia.setCreateBy(operName);
                        multimedia.setCreateTime(nowDate);
                        multimediaMapper.insertIdwMultimedia(multimedia);
                    }
                }
                //建造商数据维护
                String developmentStage = DictUtils.getDictValue("sys_development_stage", "建造");
                String contractor = satelliteModel.getBuilder();
                if (StringUtils.isNotBlank(contractor)) {
                    //删除原有数据
                    weaponryContractorMapper.deleteByWeaponryCodeAndDevelopmentStage(weaponryCode, developmentStage, operName);
                    String[] contractorArr = contractor.split("[;；]");
                    for (String name : contractorArr) {
                        if (StringUtils.isNotBlank(name)) {
                            name = name.trim();
                            IdwWeaponryContractor weaponryContractor = new IdwWeaponryContractor();
                            weaponryContractor.setWeaponryCode(weaponryCode);
                            weaponryContractor.setStructureCode("0");
                            weaponryContractor.setOrgNameCn(name);
                            weaponryContractor.setDevelopmentStage(developmentStage);
                            weaponryContractor.setSource(satelliteModel.getSource());
                            weaponryContractor.setCreateBy(operName);
                            weaponryContractor.setCreateTime(nowDate);
                            List<IdwOrg> orgList = idwOrgMapper.selectOrgByOrgName(name, null);
                            if (StringUtils.isNotNull(orgList) && orgList.size() == 1) {
                                IdwOrg org = orgList.get(0);
                                weaponryContractor.setOrgCode(org.getOrgCode());
                                weaponryContractor.setOrgNameCn(org.getOrgNameCn());
                                weaponryContractor.setOrgNameEn(org.getOrgNameEn());
                                weaponryContractor.setOrgProfile(StringUtils.isNotBlank(org.getProfileCn()) ? org.getProfileCn() : org.getProfileEn());
                            }
                            weaponryContractorMapper.insertIdwWeaponryContractor(weaponryContractor);
                        }
                    }
                }
            }
        }
        //清除卫星型号数据列表缓存
        CacheUtils.remove("weaponryImportTreatingAfterSatelliteModelList-" + operName);
        return "卫星型号共：" + satelliteModelList.size() + "条" + ",新增：" + insertCount + "条" + ",修改：" + updateCount + "条";
    }

    /**
     * 根据卫星型号武器装备编码查询
     *
     * @param weaponryCodes 卫星型号武器装备编码
     * @return 结果
     */
    @Override
    public List<SatelliteModel> selectSatelliteModelByWeaponryCodes(String[] weaponryCodes) {
        return satelliteModelMapper.selectSatelliteModelByWeaponryCodes(weaponryCodes);
    }
}
