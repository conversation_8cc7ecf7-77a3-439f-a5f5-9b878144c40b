package com.lirong.weaponry.model.domain;

import com.lirong.common.annotation.Excel;
import com.lirong.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.List;

/**
 * 卫星型号对象 satellite_model
 *
 * <AUTHOR>
 * @date 2023-09-18
 */
public class SatelliteModel extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 卫星型号ID
     */
    private Long modelId;

    /**
     * 卫星型号编码（model_id）
     */
    @Excel(name = "卫星型号编码")
    private String weaponryCode;

    /**
     * 卫星分类编码
     */
    @Excel(name = "装备分类编码")
    private String classificationCode;

    /**
     * 装备分类名称
     */
    @Excel(name = "装备分类名称")
    private String classificationName;

    /**
     * 国家/地区
     */
    @Excel(name = "国家/地区")
    private String country;

    /**
     * 卫星编码
     */
    @Excel(name = "卫星编码")
    private String satelliteCode;

    /**
     * 卫星中文名称
     */
    @Excel(name = "卫星中文名称")
    private String satelliteNameCn;

    /**
     * 卫星英文名称
     */
    @Excel(name = "卫星英文名称")
    private String satelliteNameEn;

    /**
     * 型号中文名称
     */
    @Excel(name = "型号中文名称")
    private String satelliteModelNameCn;

    /**
     * 型号英文名称
     */
    @Excel(name = "型号英文名称")
    private String satelliteModelNameEn;

    /**
     * 建造商
     */
    @Excel(name = "承包商")
    private String builder;

    /**
     * 图片
     */
    @Excel(name = "图片")
    private String picture;

    /**
     * 中文简介
     */
    @Excel(name = "中文简介")
    private String profileCn;

    /**
     * 英文简介
     */
    @Excel(name = "英文简介")
    private String profileEn;

    /**
     * 研制背景
     */
    @Excel(name = "研制背景")
    private String developmentBackground;

    /**
     * 使用状态
     */
    @Excel(name = "使用状态|状态")
    private String status;

    /**
     * 发射
     */
    @Excel(name = "发射")
    private String launch;

    /**
     * 轨道
     */
    @Excel(name = "轨道")
    private String orbit;

    /**
     * 设计寿命
     */
    @Excel(name = "设计寿命")
    private String designLife;

    /**
     * 作用
     */
    @Excel(name = "作用")
    private String applications;

    /**
     * 配置
     */
    @Excel(name = "配置")
    private String configuration;

    /**
     * 有效载荷
     */
    @Excel(name = "有效载荷")
    private String payload;

    /**
     * 质量
     */
    @Excel(name = "质量")
    private String mass;

    /**
     * 功率
     */
    @Excel(name = "功率")
    private String power;

    /**
     * 通信
     */
    @Excel(name = "通信")
    private String communications;

    /**
     * 传感器
     */
    @Excel(name = "传感器")
    private String sensors;

    /**
     * 花费
     */
    @Excel(name = "花费")
    private String cost;

    /**
     * 航天器
     */
    @Excel(name = "航天器")
    private String spacecraft;

    /**
     * 姿态控制
     */
    @Excel(name = "姿态控制")
    private String attitudeControl;

    /**
     * 指向经度
     */
    @Excel(name = "指向经度")
    private String propulsion;

    /**
     * 衰变日期
     */
    @Excel(name = "衰变日期")
    private String decayDate;

    /**
     * 所属军兵种
     */
    @Excel(name = "所属军兵种")
    private String troopsCategory;

    /**
     * 辅以装备
     */
    @Excel(name = "服役状态")
    private String serviceStatus;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String notes;

    /**
     * 是否显示前台
     */
    private Integer showHome;

    /**
     * 排序
     */
    @Excel(name = "排序")
    private Integer orderNum;

    /**
     * 数据来源
     */
    @Excel(name = "数据来源")
    private String source;

    /**
     * 是否删除，0-未删除，1-已删除
     */
    private Integer isDelete;

    /**
     * 承包商ID
     */
    private Long[] contractorId;

    /**
     * 承包商编码
     */
    private String[] contractorCode;

    /**
     * 承包商中文名称
     */
    private String[] contractorNameCn;

    /**
     * 承包商英文名称
     */
    private String[] contractorNameEn;

    /**
     * 删除的承包商ID
     */
    private long[] deleteContractorId;

    /**
     * 是否更新
     */
    private Boolean[] isUpdateContractor;

    private List<String> pictureNameList;

    private List<String> picturePathList;

    private List<String> pictureMd5List;

    public void setModelId(Long modelId) {
        this.modelId = modelId;
    }

    public Long getModelId() {
        return modelId;
    }

    public void setWeaponryCode(String weaponryCode) {
        this.weaponryCode = weaponryCode;
    }

    public String getWeaponryCode() {
        return weaponryCode;
    }

    public void setClassificationCode(String classificationCode) {
        this.classificationCode = classificationCode;
    }

    public String getClassificationCode() {
        return classificationCode;
    }

    public String getClassificationName() {
        return classificationName;
    }

    public void setClassificationName(String classificationName) {
        this.classificationName = classificationName;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCountry() {
        return country;
    }

    public void setSatelliteCode(String satelliteCode) {
        this.satelliteCode = satelliteCode;
    }

    public String getSatelliteCode() {
        return satelliteCode;
    }

    public void setSatelliteNameCn(String satelliteNameCn) {
        this.satelliteNameCn = satelliteNameCn;
    }

    public String getSatelliteNameCn() {
        return satelliteNameCn;
    }

    public void setSatelliteNameEn(String satelliteNameEn) {
        this.satelliteNameEn = satelliteNameEn;
    }

    public String getSatelliteNameEn() {
        return satelliteNameEn;
    }

    public void setSatelliteModelNameCn(String satelliteModelNameCn) {
        this.satelliteModelNameCn = satelliteModelNameCn;
    }

    public String getSatelliteModelNameCn() {
        return satelliteModelNameCn;
    }

    public void setSatelliteModelNameEn(String satelliteModelNameEn) {
        this.satelliteModelNameEn = satelliteModelNameEn;
    }

    public String getSatelliteModelNameEn() {
        return satelliteModelNameEn;
    }

    public String getBuilder() {
        return builder;
    }

    public void setBuilder(String builder) {
        this.builder = builder;
    }

    public void setPicture(String picture) {
        this.picture = picture;
    }

    public String getPicture() {
        return picture;
    }

    public void setProfileCn(String profileCn) {
        this.profileCn = profileCn;
    }

    public String getProfileCn() {
        return profileCn;
    }

    public void setProfileEn(String profileEn) {
        this.profileEn = profileEn;
    }

    public String getProfileEn() {
        return profileEn;
    }

    public void setDevelopmentBackground(String developmentBackground) {
        this.developmentBackground = developmentBackground;
    }

    public String getDevelopmentBackground() {
        return developmentBackground;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    public void setLaunch(String launch) {
        this.launch = launch;
    }

    public String getLaunch() {
        return launch;
    }

    public void setOrbit(String orbit) {
        this.orbit = orbit;
    }

    public String getOrbit() {
        return orbit;
    }

    public void setDesignLife(String designLife) {
        this.designLife = designLife;
    }

    public String getDesignLife() {
        return designLife;
    }

    public void setApplications(String applications) {
        this.applications = applications;
    }

    public String getApplications() {
        return applications;
    }

    public void setConfiguration(String configuration) {
        this.configuration = configuration;
    }

    public String getConfiguration() {
        return configuration;
    }

    public void setPayload(String payload) {
        this.payload = payload;
    }

    public String getPayload() {
        return payload;
    }

    public void setMass(String mass) {
        this.mass = mass;
    }

    public String getMass() {
        return mass;
    }

    public void setPower(String power) {
        this.power = power;
    }

    public String getPower() {
        return power;
    }

    public void setCommunications(String communications) {
        this.communications = communications;
    }

    public String getCommunications() {
        return communications;
    }

    public void setSensors(String sensors) {
        this.sensors = sensors;
    }

    public String getSensors() {
        return sensors;
    }

    public void setCost(String cost) {
        this.cost = cost;
    }

    public String getCost() {
        return cost;
    }

    public void setSpacecraft(String spacecraft) {
        this.spacecraft = spacecraft;
    }

    public String getSpacecraft() {
        return spacecraft;
    }

    public void setAttitudeControl(String attitudeControl) {
        this.attitudeControl = attitudeControl;
    }

    public String getAttitudeControl() {
        return attitudeControl;
    }

    public void setPropulsion(String propulsion) {
        this.propulsion = propulsion;
    }

    public String getPropulsion() {
        return propulsion;
    }

    public void setDecayDate(String decayDate) {
        this.decayDate = decayDate;
    }

    public String getDecayDate() {
        return decayDate;
    }

    public String getTroopsCategory() {
        return troopsCategory;
    }

    public void setTroopsCategory(String troopsCategory) {
        this.troopsCategory = troopsCategory;
    }

    public String getServiceStatus() {
        return serviceStatus;
    }

    public void setServiceStatus(String serviceStatus) {
        this.serviceStatus = serviceStatus;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public String getNotes() {
        return notes;
    }

    public Integer getShowHome() {
        return showHome;
    }

    public void setShowHome(Integer showHome) {
        this.showHome = showHome;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSource() {
        return source;
    }

    public Long[] getContractorId() {
        return contractorId;
    }

    public void setContractorId(Long[] contractorId) {
        this.contractorId = contractorId;
    }

    public String[] getContractorCode() {
        return contractorCode;
    }

    public void setContractorCode(String[] contractorCode) {
        this.contractorCode = contractorCode;
    }

    public String[] getContractorNameCn() {
        return contractorNameCn;
    }

    public void setContractorNameCn(String[] contractorNameCn) {
        this.contractorNameCn = contractorNameCn;
    }

    public String[] getContractorNameEn() {
        return contractorNameEn;
    }

    public void setContractorNameEn(String[] contractorNameEn) {
        this.contractorNameEn = contractorNameEn;
    }

    public long[] getDeleteContractorId() {
        return deleteContractorId;
    }

    public void setDeleteContractorId(long[] deleteContractorId) {
        this.deleteContractorId = deleteContractorId;
    }

    public Boolean[] getIsUpdateContractor() {
        return isUpdateContractor;
    }

    public void setIsUpdateContractor(Boolean[] isUpdateContractor) {
        this.isUpdateContractor = isUpdateContractor;
    }

    public List<String> getPictureNameList() {
        return pictureNameList;
    }

    public void setPictureNameList(List<String> pictureNameList) {
        this.pictureNameList = pictureNameList;
    }

    public List<String> getPicturePathList() {
        return picturePathList;
    }

    public void setPicturePathList(List<String> picturePathList) {
        this.picturePathList = picturePathList;
    }

    public List<String> getPictureMd5List() {
        return pictureMd5List;
    }

    public void setPictureMd5List(List<String> pictureMd5List) {
        this.pictureMd5List = pictureMd5List;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("modelId", getModelId())
                .append("weaponryCode", getWeaponryCode())
                .append("classificationCode", getClassificationCode())
                .append("classificationName", getClassificationName())
                .append("country", getCountry())
                .append("satelliteCode", getSatelliteCode())
                .append("satelliteNameCn", getSatelliteNameCn())
                .append("satelliteNameEn", getSatelliteNameEn())
                .append("satelliteModelNameCn", getSatelliteModelNameCn())
                .append("satelliteModelNameEn", getSatelliteModelNameEn())
                .append("builder", getBuilder())
                .append("picture", getPicture())
                .append("profileCn", getProfileCn())
                .append("profileEn", getProfileEn())
                .append("developmentBackground", getDevelopmentBackground())
                .append("status", getStatus())
                .append("launch", getLaunch())
                .append("orbit", getOrbit())
                .append("designLife", getDesignLife())
                .append("applications", getApplications())
                .append("configuration", getConfiguration())
                .append("payload", getPayload())
                .append("mass", getMass())
                .append("power", getPower())
                .append("communications", getCommunications())
                .append("sensors", getSensors())
                .append("cost", getCost())
                .append("spacecraft", getSpacecraft())
                .append("attitudeControl", getAttitudeControl())
                .append("propulsion", getPropulsion())
                .append("decayDate", getDecayDate())
                .append("troopsCategory", getTroopsCategory())
                .append("serviceStatus", getServiceStatus())
                .append("notes", getNotes())
                .append("showHome", getShowHome())
                .append("orderNum", getOrderNum())
                .append("source", getSource())
                .append("contractorId", getContractorId())
                .append("contractorCode", getContractorCode())
                .append("contractorNameCn", getContractorNameCn())
                .append("contractorNameEn", getContractorNameEn())
                .append("deleteContractorId", getDeleteContractorId())
                .append("isUpdateContractor", getIsUpdateContractor())
                .append("pictureNameList", getPictureNameList())
                .append("picturePathList", getPicturePathList())
                .append("pictureMd5List", getPictureMd5List())
                .append("isDelete", getIsDelete())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
