package com.lirong.project.member.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.lirong.common.config.WebdpConfig;
import com.lirong.common.constant.Constants;
import com.lirong.common.core.domain.StructureChart;
import com.lirong.common.service.FileDownloadService;
import com.lirong.common.utils.*;
import com.lirong.common.utils.file.FileUploadUtils;
import com.lirong.common.utils.file.FileUtils;
import com.lirong.common.utils.poi.ExcelUtil;
import com.lirong.multimedia.domain.IdwMultimedia;
import com.lirong.multimedia.mapper.IdwMultimediaMapper;
import com.lirong.organization.system.domain.IdwOrgArchitecture;
import com.lirong.organization.system.mapper.IdwOrgArchitectureMapper;
import com.lirong.organization.common.domain.IdwOrg;
import com.lirong.organization.common.mapper.IdwOrgMapper;
import com.lirong.organization.document.domain.IdwOrgDocument;
import com.lirong.organization.document.mapper.IdwOrgDocumentMapper;
import com.lirong.organization.financial.mapper.IdwOrgFinancialMapper;
import com.lirong.organization.news.domain.IdwOrgNews;
import com.lirong.organization.news.mapper.IdwOrgNewsMapper;
import com.lirong.organization.product.mapper.IdwOrgProductMapper;
import com.lirong.organization.project.domain.IdwOrgProject;
import com.lirong.organization.project.mapper.IdwOrgProjectMapper;
import com.lirong.organization.staff.domain.IdwOrgStaff;
import com.lirong.organization.staff.mapper.IdwOrgStaffMapper;
import com.lirong.personnel.common.domain.IdwPeopleMain;
import com.lirong.personnel.common.mapper.IdwPeopleMainMapper;
import com.lirong.personnel.domain.IdwPeopleAchievement;
import com.lirong.personnel.education.domain.IdwPeopleEducation;
import com.lirong.personnel.education.mapper.IdwPeopleEducationMapper;
import com.lirong.personnel.honor.domain.IdwPeopleHonor;
import com.lirong.personnel.honor.mapper.IdwPeopleHonorMapper;
import com.lirong.personnel.mapper.IdwPeopleAchievementMapper;
import com.lirong.personnel.media.domain.IdwPeopleSocialAccount;
import com.lirong.personnel.media.domain.IdwPeopleSocialMedia;
import com.lirong.personnel.media.mapper.IdwPeopleSocialAccountMapper;
import com.lirong.personnel.media.mapper.IdwPeopleSocialMediaMapper;
import com.lirong.personnel.news.domain.IdwPeopleNews;
import com.lirong.personnel.news.mapper.IdwPeopleNewsMapper;
import com.lirong.personnel.patent.domain.IdwPeoplePatent;
import com.lirong.personnel.patent.domain.IdwPeoplePatentInventor;
import com.lirong.personnel.patent.mapper.IdwPeoplePatentInventorMapper;
import com.lirong.personnel.patent.mapper.IdwPeoplePatentMapper;
import com.lirong.personnel.publishcation.domain.IdwPeoplePublishedWorks;
import com.lirong.personnel.publishcation.domain.IdwPeoplePublishedWorksAuthor;
import com.lirong.personnel.publishcation.mapper.IdwPeoplePublishedWorksAuthorMapper;
import com.lirong.personnel.publishcation.mapper.IdwPeoplePublishedWorksMapper;
import com.lirong.personnel.relationship.domain.IdwPeopleRelationship;
import com.lirong.personnel.relationship.mapper.IdwPeopleRelationshipMapper;
import com.lirong.personnel.work.domain.IdwPeopleWorkExperience;
import com.lirong.personnel.work.mapper.IdwPeopleWorkExperienceMapper;
import com.lirong.project.member.domain.*;
import com.lirong.project.member.mapper.AllPeopleMapper;
import com.lirong.project.member.mapper.TestMapper;
import com.lirong.project.member.service.TestService;
import com.lirong.project.member.utils.CSTranslateUtils;
import com.lirong.project.member.utils.BaiDuTranslateUtils;
import com.lirong.resource.domain.IdwResource;
import com.lirong.resource.domain.IdwResourceCategory;
import com.lirong.resource.domain.IdwResourceDictCategory;
import com.lirong.resource.mapper.IdwResourceCategoryMapper;
import com.lirong.resource.mapper.IdwResourceDictCategoryMapper;
import com.lirong.resource.mapper.IdwResourceMapper;
import com.lirong.system.architecture.domain.IdwOrgSystemArchitecture;
import com.lirong.system.architecture.mapper.IdwOrgSystemArchitectureMapper;
import com.lirong.technosphere.domain.IdwTechnosphere;
import com.lirong.weaponry.basic.domain.IdwWeaponryBasic;
import com.lirong.weaponry.basic.mapper.IdwWeaponryAssociationMapper;
import com.lirong.weaponry.basic.mapper.IdwWeaponryBasicMapper;
import com.lirong.weaponry.classification.mapper.WeaponryClassificationMapper;
import com.lirong.weaponry.ship.domain.Ship;
import com.lirong.weaponry.ship.mapper.ShipMapper;
import com.lirong.weaponry.specifications.domain.IdwWeaponrySpecifications;
import com.lirong.weaponry.specifications.mapper.IdwWeaponrySpecificationsMapper;
import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.text.PDFTextStripperByArea;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.ss.usermodel.*;
import org.icepdf.core.exceptions.PDFException;
import org.icepdf.core.exceptions.PDFSecurityException;
import org.icepdf.core.pobjects.Document;
import org.icepdf.core.util.GraphicsRenderingHints;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.InputStream;
import java.io.*;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * TestService业务层处理
 *
 * <AUTHOR>
 * @date 2021-06-25
 */
@Service
public class TestServiceImpl implements TestService {
    @Autowired
    private TestMapper testMapper;
    @Autowired
    private ShipMapper shipMapper;
    @Autowired
    private IdwMultimediaMapper idwMultimediaMapper;
    @Autowired
    private IdwWeaponryBasicMapper weaponryBasicMapper;
    @Autowired
    private IdwOrgSystemArchitectureMapper idwOrgSystemArchitectureMapper;
    @Autowired
    private WeaponryClassificationMapper weaponryClassificationMapper;
    @Autowired
    private IdwWeaponrySpecificationsMapper weaponrySpecificationsMapper;
    @Autowired
    private IdwWeaponryAssociationMapper weaponryAssociationMapper;
    @Autowired
    private IdwOrgArchitectureMapper idwOrgArchitectureMapper;
    @Autowired
    private AllPeopleMapper allPeopleMapper;
    @Autowired
    private List<FileDownloadService> fileDownloadServiceList;
    @Autowired
    private IdwPeopleEducationMapper peopleEducationMapper;
    @Autowired
    private IdwPeopleSocialAccountMapper peopleSocialAccountMapper;
    @Autowired
    private IdwPeopleSocialMediaMapper peopleSocialMediaMapper;
    @Autowired
    private IdwPeopleNewsMapper peopleNewsMapper;
    @Autowired
    private IdwPeopleWorkExperienceMapper peopleWorkExperienceMapper;
    @Autowired
    private IdwPeopleHonorMapper peopleHonorMapper;
    @Autowired
    private IdwPeoplePublishedWorksMapper peoplePublishedWorksMapper;
    @Autowired
    private IdwPeoplePublishedWorksAuthorMapper peoplePublishedWorksAuthorMapper;
    @Autowired
    private IdwPeoplePatentInventorMapper peoplePatentInventorMapper;
    @Autowired
    private IdwPeopleRelationshipMapper peopleRelationshipMapper;
    @Autowired
    private IdwPeopleAchievementMapper peopleAchievementMapper;
    @Autowired
    private IdwPeoplePatentMapper peoplePatentMapper;
    @Autowired
    private IdwResourceMapper idwResourceMapper;
    @Autowired
    private IdwOrgFinancialMapper idwOrgFinancialMapper;
    @Autowired
    private IdwOrgProductMapper idwOrgProductMapper;
    @Autowired
    private IdwResourceCategoryMapper idwResourceCategoryMapper;
    @Autowired
    private IdwMultimediaMapper multimediaMapper;
    @Autowired
    private IdwResourceDictCategoryMapper idwResourceDictCategoryMapper;

    /**
     * 根据机构类型修改机构排序号
     *
     * @param orgType       机构类型
     * @param startOrderNum 起始排序号数
     * @return 结果
     */
    @Override
    public String accordingOrgTypeUpdateOrderNum(String orgType, Integer startOrderNum) {
        if (StringUtils.isEmpty(orgType)) {
            orgType = "国防单位,政府机构,军事基地,国防企业,国防科研";
            String[] orgTypeArray = orgType.split(",");
            for (int i = 0; i < orgTypeArray.length; i++) {
                Integer maxLevel = testMapper.selectMaxLevelByOrgType(orgTypeArray[i]);
                orgSort(orgTypeArray[i], maxLevel, i + 1);
            }
        } else {
            if (orgType.equals("craftCompany")) {//startOrderNum = 1
                orgType = "国防单位";
            } else if (orgType.equals("administration")) {//startOrderNum = 2
                orgType = "政府机构";
            } else if (orgType.equals("base")) {//startOrderNum = 3
                orgType = "军事基地";
            } else if (orgType.equals("research")) {//startOrderNum = 4
                orgType = "国防科研";
            } else if (orgType.equals("enterprises")) {// startOrderNum = 5
                orgType = "国防企业";
            }
            Integer maxLevel = testMapper.selectMaxLevelByOrgType(orgType);
            orgSort(orgType, maxLevel, startOrderNum);
        }
        return "排序成功";
    }

    /**
     * 机构排序
     *
     * @param orgType       机构类型
     * @param maxLevel      最大层级
     * @param startOrderNum 起始排序号数
     */
    public void orgSort(String orgType, Integer maxLevel, Integer startOrderNum) {
        for (int i = 0; i < maxLevel; i++) {
            List<String> orgCodeList = testMapper.selectOrgCodeByLevelAndOrgType(orgType, i + 1);
            for (String orgCode : orgCodeList) {
                testMapper.updateOrgOrderNumByOrgCode(orgCode, startOrderNum);
                startOrderNum++;
            }
        }
    }

    /**
     * 根据人员类型修改人员排序号
     *
     * @param category      人员类型
     * @param startOrderNum 起始排序号数
     * @return 结果
     */
    @Override
    public String accordingCategoryUpdateOrderNum(String category, Integer startOrderNum) {
        List<IdwPeopleNews> peopleNewsList = testMapper.selectPeopleNewsOrderCount();
        List<String> peopleCodes = peopleNewsList.stream().map(IdwPeopleNews::getPeopleCode).collect(Collectors.toList());
        Integer orderNum = 1;
        for (IdwPeopleNews news : peopleNewsList) {
            String peopleCode = news.getPeopleCode();
            testMapper.updatePeopleOrderNumByPeopleCode(peopleCode, orderNum);
            orderNum++;
        }
        List<String> peopleCodeList = testMapper.selectPeopleCodeByCategory("基层人员");
        List<String> list = peopleCodeList.stream().filter(peopleCode -> !peopleCodes.contains(peopleCode)).collect(Collectors.toList());
        for (String code : list) {
            testMapper.updatePeopleOrderNumByPeopleCode(code, orderNum);
            orderNum++;
        }
        return "排序成功";
    }

    /**
     * 军事基地重新排序
     *
     * @return 结果
     */
    @Override
    public String baseUpdateOrderNum(Integer startOrderNum) {
        List<String> baseOrgCodeList = testMapper.selectImportBaseOrgCode();
        if (startOrderNum == null) {
            startOrderNum = 3;
        }
        for (String orgCode : baseOrgCodeList) {
            startOrderNum++;
            testMapper.updateOrgOrderNumByOrgCode(orgCode, startOrderNum);
        }
        List<String> migrationBaseOrgCodeList = testMapper.selectMigrationBaseOrgCode();
        for (String orgCode : migrationBaseOrgCodeList) {
            startOrderNum++;
            testMapper.updateOrgOrderNumByOrgCode(orgCode, startOrderNum);
        }
        return "排序成功";
    }

    /**
     * 拆分人员发表作品作者字段数据到人员发表作品作者表中
     *
     * @return 结果
     */
    @Override
    public String coauthorSplit() {
        List<PeoplePublishedWorks> peoplePublishedWorksList = testMapper.selectPeoplePublishedWorks();
        if (peoplePublishedWorksList != null && peoplePublishedWorksList.size() > 0) {
            for (PeoplePublishedWorks peoplePublishedWorks : peoplePublishedWorksList) {
                List<PeoplePublishedWorksAuthor> oldAuthorList = testMapper.selectAuthorByWorksId(peoplePublishedWorks.getWorksId());
                //获取的作者
                if (StringUtils.isNotBlank(peoplePublishedWorks.getAuthor())) {
                    String[] authorArr = peoplePublishedWorks.getAuthor().replaceAll("\\n", ",").replace("and", ",").replace("；", ",").replace(";", ",").replace("，", ",").split(",");
                    //判断需要新增的作者
                    for (String author : authorArr) {
                        if (StringUtils.isNotBlank(author) && (oldAuthorList == null || oldAuthorList.size() < 1 || !oldAuthorList.contains(author.trim()))) {
                            PeoplePublishedWorksAuthor peoplePublishedWorksAuthor = new PeoplePublishedWorksAuthor();
                            peoplePublishedWorksAuthor.setWorksId(peoplePublishedWorks.getWorksId());
                            peoplePublishedWorksAuthor.setPeopleName(author.trim());
                            testMapper.insertWorksAuthor(peoplePublishedWorksAuthor);
                        }
                    }
                    //判断需要删除的作者
                    if (oldAuthorList != null && oldAuthorList.size() > 0) {
                        List<String> newAuthorList = Arrays.asList(authorArr);
                        for (PeoplePublishedWorksAuthor author : oldAuthorList) {
                            if (!newAuthorList.contains(author.getPeopleName())) {
                                //根据作者ID删除
                                testMapper.deleteWorksAuthorByAuthorId(author.getAuthorId());
                            }
                        }
                    }
                }
            }
        }
        return "拆分成功";
    }

    /**
     * 更新solr
     *
     * @return 结果
     */
    @Override
    public String updateSolr() {
       /* //查询库中与solr 关联的数据weaponryChart
        List<IdwMultimedia> solrFileCorrelationData = testMapper.selectSolrCorrelation();
        for (IdwMultimedia multimedia : solrFileCorrelationData) {
            String url = "http://localhost:8983/solr/file";
            SolrClient client = new HttpSolrClient.Builder(url).build();
            DocumentObjectBinder binder = new DocumentObjectBinder();
            Article article = new Article();
            article.setId(multimedia.getStoragePath().replace("\\", "\\\\"));
            String[] title = new String[1];
            title[0] = multimedia.getTitle();
            article.setTitle(title);
            // 传入一个文章实体，里面包括solr索引的所有字段
            SolrInputDocument doc = binder.toSolrInputDocument(article);
            doc.addField("_version_", 1);
            try {
                client.add(doc);
                System.out.println(client.commit());
            } catch (Exception e) {
                e.printStackTrace();
            }
            System.out.println("--------------------------------------------------------------------------------------更新中--------------------------------------------------------------------------------------");
        }
        System.out.println("--------------------------------------------------------------------------------------更新成功--------------------------------------------------------------------------------------");*/
        return "更新成功";
    }

    /**
     * 维护机构层级字段
     *
     * @return 结果
     */
    @Override
    public String vindicateOrgLevel() {
        List<IdwOrgRelationship> orgRelationshipList = testMapper.selectAllOrgRelationship();
        if (StringUtils.isNotNull(orgRelationshipList) && orgRelationshipList.size() > 0) {
            for (IdwOrgRelationship orgRelationship : orgRelationshipList) {
                testMapper.updateOrgRelationshipLevelById(orgRelationship.getRelationshipId(), (long) orgRelationship.getAncestors().split(",").length);
            }
        }
        return "更新成功";
    }

    /**
     * 计算多媒体表中与机构/人员/武器装备关联文档md5值
     *
     * @return 结果
     */
    @Override
    public String calculateMultimediaMd5() throws FileNotFoundException {
        List<IdwMultimedia> multimediaList = testMapper.selectAllMultimedia();
        if (StringUtils.isNotNull(multimediaList) && multimediaList.size() > 0) {
            for (IdwMultimedia multimedia : multimediaList) {
                String filePath = multimedia.getStoragePath().replace(Constants.RESOURCE_PREFIX, WebdpConfig.getPath());
                File file = new File(filePath);
                if (file.exists()) {
                    InputStream inputStream = new FileInputStream(file);
                    String md5 = DigestUtils.md5Hex(String.valueOf(inputStream));
                    //System.err.println("md5：" + md5 + "长度：" + md5.length());
                    testMapper.updateMultimediaMd5ById(multimedia.getMediaId(), md5);
                } else {
                    System.err.println(multimedia.getTitle() + "：不存在！");
                }
            }
        }
        return "计算成功";
    }

    /**
     * 修改原始数据检索文档名称
     *
     * @return 结果
     */
    @Override
    public String updateSearchFileName() {
        List<IdwMultimedia> multimediaList = testMapper.selectAllSearchFile();
        if (StringUtils.isNotNull(multimediaList) && multimediaList.size() > 0) {
            int count = 0;
            for (IdwMultimedia multimedia : multimediaList) {
                String storagePath = multimedia.getStoragePath();
                if (StringUtils.isNotBlank(storagePath)) {
                    String filePath = storagePath.replace(Constants.RESOURCE_PREFIX, "E:/webdp/uploadPath");
                    File file = new File(filePath);
                    if (file.exists()) {
                        String[] filePathArr = filePath.split("/");
                        String newFile = "";
                        for (int i = 0; i < filePathArr.length; i++) {
                            if (StringUtils.isBlank(newFile)) {
                                newFile = filePathArr[i];
                            } else {
                                if (i < filePathArr.length - 1) {
                                    newFile += "/" + filePathArr[i];
                                } else {
                                    newFile += "/" + multimedia.getTitle() + filePath.substring(filePath.lastIndexOf("."));
                                }
                            }
                        }
                        if (!filePath.equals(newFile)) {
                            if (file.renameTo(new File(newFile))) {
                                //System.out.println("修改文件名成功");
                                testMapper.updateStoragePathById(multimedia.getMediaId(), newFile);
                            } else {
                                count++;
                                System.out.println(count + "、修改文件名失败：" + filePath);
                            }
                        }
                    }
                }
            }
        }
        return "修改成功";
    }

    /**
     * 维护机构关系表中parent_id字段数据
     *
     * @return 结果
     */
    @Override
    public String updateOrgRelationshipParentId() {
       /* List<IdwOrgRelationship> orgRelationshipList = testMapper.selectAllOrgRelationship();
        for (IdwOrgRelationship orgRelationship : orgRelationshipList) {
            if ("0".equals(orgRelationship.getParentCode())) {
                testMapper.updateOrgRelationshipParentIdById(orgRelationship.getRelationshipId(), (long) -1);
            } else {
                List<IdwOrgRelationship> parentOrgRelationshipList = testMapper.selectOrgRelationshipByOrgCode(orgRelationship.getParentCode());
                if (parentOrgRelationshipList.size() == 1) {
                    IdwOrgRelationship parentOrgRelationship = parentOrgRelationshipList.get(0);
                    orgRelationship.setParentId(parentOrgRelationship.getRelationshipId());
                    orgRelationship.setAncestors(parentOrgRelationship.getAncestors() + "," + parentOrgRelationship.getOrgCode());
                    orgRelationship.setLevel((long) orgRelationship.getAncestors().split(",").length);
                    idwOrgRelationshipMapper.updateById(orgRelationship);
                } else {
                    return orgRelationship.getOrgCode() + "不存在或存在多条关系数据";
                }
            }
        }*/

        return "维护成功";
    }

    /**
     * 根据Excel更新外台军人员职位/单位/军衔 导入people表sql语句更新
     * people static/sql
     *
     * @return 结果
     */
    @Override
    public String updatePersonnelByExcel() {
        MultipartFile multipartFile = null;
        try {
            File file = new File("C:/Users/<USER>/Desktop/政府军队官员.xlsx");
            FileInputStream input = new FileInputStream(file);
            multipartFile = new MockMultipartFile("file", file.getName(), "text/plain", IOUtils.toByteArray(input));
            ExcelUtil<People> util = new ExcelUtil<People>(People.class);
            List<People> peopleList = util.importExcel("政府军队官员", multipartFile.getInputStream());
            if (StringUtils.isNotNull(peopleList) && peopleList.size() > 0) {
                for (People people : peopleList) {
                    testMapper.insertPeople(people);
                }
            }
            input.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "更新成功";
    }

    /**
     * 修改机构表中数据来源为空的数据(赋值顶层节点数据来源)
     *
     * @return 结果
     */
    @Override
    public String updateOrgNullSource() {
        //查询所有数据来源为空的数据
      /*  List<IdwOrg> orgLIst = testMapper.selectAllNullSourceOrg();
        if (StringUtils.isNotNull(orgLIst) && orgLIst.size() > 0) {
            for (IdwOrg org : orgLIst) {
                //查询顶层节点数据来源并赋值
                String ancestors = org.getAncestors();
                if (StringUtils.isNotBlank(ancestors)) {
                    String[] ancestorsArr = ancestors.split(",");
                    if (ancestorsArr.length > 1) {
                        String source = testMapper.selectSourceByOrgCode(ancestorsArr[1]);
                        if (StringUtils.isNotBlank(source)) {
                            testMapper.updateOrgSourceByOrgId(source, org.getOrgId());
                        } else {
                            String parentSource = testMapper.selectSourceByOrgCode(org.getParentCode());
                            testMapper.updateOrgSourceByOrgId(parentSource, org.getOrgId());
                        }
                    }
                }
            }
        }*/
        return "更新成功";
    }

    /**
     * 根据删除Excel中存在的人员
     * all_people中为所有人员
     * 校验Excel中英文名称是否存在 如果已存在删除行
     * 删除所有重复数据后将新的Excel导出
     *
     * @return 结果
     */
    @Override
    public String deleteRepetitionPeople() {
        String name = "";
        List<Integer> deleteIndexList = new ArrayList<>();
        //获取最大人员编码
        String maxPeopleCode = testMapper.selectMaxPeopleCode();
        try {
            Workbook wb = WorkbookFactory.create(new FileInputStream("C:/Users/<USER>/Desktop/美印太以外人物/2021-11-24 政府军队官员 - 副本.xlsx"));
            Sheet sheet = wb.getSheet("政府军队官员");
            Workbook wb2 = new HSSFWorkbook();
            wb2 = wb;
            Row row;
            int lastIndex = sheet.getLastRowNum();
            Row titleRow = sheet.getRow(0);
            int lastCellNum = titleRow.getLastCellNum();
            int nameIndex = 0;
            for (int i = 0; i < lastCellNum; i++) {
                //拿到英文名称单元格索引
                if ("英文姓名".equals(titleRow.getCell(i).getStringCellValue())) {
                    nameIndex = i;
                    break;
                }
            }
            for (int i = 1; i <= lastIndex; i++) {
                //跳过第一行
                row = sheet.getRow(i);
                if (StringUtils.isNull(row)) {
                    //跳过空行
                    break;
                }
                Cell cell = row.getCell(nameIndex);
                name = cell.getStringCellValue();
                //根据名称查询人员是否存在
                boolean isExist = testMapper.selectPeopleIsExistByNameEnInAllPeople(name);
                if (isExist) {
                    deleteIndexList.add(i);
                } else {
                    //人员编码
                    int peopleCodeIndex = Integer.parseInt(maxPeopleCode.replace("P", ""));
                    peopleCodeIndex++;
                    //人员编码数字为七位
                    int fillLength = 7 - String.valueOf(peopleCodeIndex).length();
                    String fillString = "";
                    if (fillLength > 0) {
                        for (int j = 0; j < fillLength; j++) {
                            fillString += "0";
                        }
                    }
                    String newMaxPeopleCode = "P" + fillString + peopleCodeIndex;
                    maxPeopleCode = newMaxPeopleCode;
                    testMapper.insertAllPeopleTable(name, newMaxPeopleCode);
                }
            }
            //
            if (deleteIndexList.size() > 0) {
                for (Integer index : deleteIndexList) {
                    sheet.getRow(index);
                }
            }
            FileOutputStream fileOut = new FileOutputStream("C:/Users/<USER>/Desktop/美印太以外人物/2021-11-24 政府军队官员(已去重).xlsx");
            wb2.write(fileOut);
            fileOut.close();
        } catch (IOException | InvalidFormatException e) {
            e.printStackTrace();
        }
        System.out.println(name);
        return "成功";
    }

    /**
     * 装备规格排序
     *
     * @return 结果
     */
    @Override
    public String weaponrySpecificationsSort() {
        List<String> weaponryCodeList = testMapper.selectAllWeaponrySpecificationsRelevancy();
        if (StringUtils.isNull(weaponryCodeList) || weaponryCodeList.size() < 1) {
            return "装备规格为空";
        }
        for (String weaponryCode : weaponryCodeList) {
            List<IdwWeaponrySpecifications> weaponrySpecificationsList = testMapper.selectTopWeaponrySpecificationsByWeaponryCode(weaponryCode);
            int orderNum = 0;
            for (IdwWeaponrySpecifications weaponrySpecifications : weaponrySpecificationsList) {
                orderNum++;
                testMapper.updateWeaponrySpecificationsOrderNumBySpecificationsId(weaponrySpecifications.getSpecificationsId(), orderNum);
                accordingSpecificationsIdSort(weaponrySpecifications.getSpecificationsId());
            }
        }
        return "排序成功";
    }

    /**
     * 修改文件名称为UUID
     *
     * @return 结果
     */
    @Override
    public String updateFileNameBecomeUUID() {
        List<IdwResource> resourceList = testMapper.selectAllResource();
        /*String filePath = "";
        for (IdwResource resource : resourceList) {
            String storagePath = resource.getStoragePath();
            if (StringUtils.isBlank(storagePath)) {
                continue;
            }
            if (!new File(storagePath.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile())).exists()) {
                filePath += StringUtils.isBlank(filePath) ? storagePath : "," + storagePath;
            }
        }
        return filePath;*/
        if (resourceList.size() == 0) {
            return "资源库为空";
        }
        for (IdwResource resource : resourceList) {
            String thumbnail = resource.getThumbnail();
            if (StringUtils.isNotBlank(thumbnail) && isContainChinese(thumbnail)) {//缩略图包含中文
                String filePath = updateFileName(thumbnail);
                //修改数据库
                testMapper.updateResourceThumbnail(resource.getResourceId(), filePath);
            }
            String storagePath = resource.getStoragePath();
            if (StringUtils.isNotBlank(storagePath) && isContainChinese(storagePath)) {//文件路径包含中文
                String filePath = updateFileName(storagePath);
                //修改数据库
                testMapper.updateResourceStoragePath(resource.getResourceId(), filePath);
            }
        }
        return "修改成功";
    }

    /**
     * 根据体系结构ID更新顶层机构排序号
     *
     * @return 结果
     */
    @Override
    public String updateOrgOrderNumByOrgThemeId() {
        /*List<IdwOrgSystemArchitecture> orgThemeList = testMapper.selectOrgThemeOrderByThemeId();
        int i = 1;
        List<String> orgCodeList = new ArrayList<>();
        for (IdwOrgSystemArchitecture orgTheme : orgThemeList) {
            testMapper.updateOrgOrderNumByOrgCode(orgTheme.getOrgCode(), i);
            orgCodeList.add(orgTheme.getOrgCode());
            i++;
        }
        List<String> orgCodes = testMapper.selectOrgCodeExcludeOrgCodes(orgCodeList.toArray(new String[0]));
        for (String orgCode : orgCodes) {
            testMapper.updateOrgOrderNumByOrgCode(orgCode, i);
            i++;
        }*/
        return "排序成功";
    }

    /**
     * 根据体系结构更新机构类型别名
     *
     * @return 结果
     */
    @Override
    public String updateOrgTypeAliasByOrgThemeId() {
       /* List<IdwOrgSystemArchitecture> orgThemeList = testMapper.selectSecondLevelOrgTheme();
        for (IdwOrgSystemArchitecture orgTheme : orgThemeList) {
            List<String> orgCodeList = testMapper.selectOrgThemeOrgCodeBySecondLevelOrgThemeId(orgTheme.getId());
            testMapper.updateOrgTypeAlias(orgCodeList.toArray(new String[0]), orgTheme.getName());
        }*/
        return "更新成功";
    }

    /**
     * 格式化日期 机构文献/新闻/项目
     *
     * @return 结果
     */
    @Override
    public String formatMonthOrDay() {
        //机构新闻
        List<FormatMonthOrDay> orgNews = testMapper.selectAllOrgNews();
        for (FormatMonthOrDay news : orgNews) {
            String newDate = DateUtils.updateDateSeparator(news.getDate());
            if (StringUtils.isNotBlank(newDate)) {
                testMapper.updatePublishDateByNewsId(news.getId(), newDate);
            }
        }
        //机构采办项目
        List<FormatMonthOrDay> orgProjectList = testMapper.selectAllOrgProject();
        for (FormatMonthOrDay project : orgProjectList) {
            String newDate = DateUtils.updateDateSeparator(project.getDate());
            if (StringUtils.isNotBlank(newDate)) {
                testMapper.updatePublishDateByProjectId(project.getId(), newDate);
            }
        }
        //机构文献
        List<FormatMonthOrDay> orgDocumentList = testMapper.selectAllOrgDocument();
        for (FormatMonthOrDay document : orgDocumentList) {
            String newDate = DateUtils.updateDateSeparator(document.getDate());
            if (StringUtils.isNotBlank(newDate)) {
                testMapper.updatePublishDateByDocumentId(document.getId(), newDate);
            }
        }
        return "格式化成功";
    }

    /**
     * 解决部分顶层节点无架构数据但它处于其他架构里有架构数据问题
     *
     * @return 结果
     */
    @Override
    public String copyOrganizationalStructure() {
        List<IdwOrgRelationship> orgRelationshipList = testMapper.selectWithoutOrganizationalStructureTopOrgRelationship();
        for (IdwOrgRelationship orgRelationship : orgRelationshipList) {
            IdwOrgRelationship notTopNodeOrgRelationship = testMapper.selectNotTopNodeOrgRelationshipByOrgCode(orgRelationship.getOrgCode());
            if (StringUtils.isNotNull(notTopNodeOrgRelationship)) {
                List<IdwOrgRelationship> children = testMapper.selectChildrenByOrgCodeAndAncestors(notTopNodeOrgRelationship.getOrgCode(), notTopNodeOrgRelationship.getAncestors());
                copyOrganizationalStructure(orgRelationship, children);
            }
        }
        return "拷贝成功！";
    }

    /**
     * 导入人员 用于人员去重
     *
     * @return 结果
     */
    @Override
    public String importPeople() {
        /*int count = 0;
        try {
            ExcelUtil<AllPeople> util = new ExcelUtil<AllPeople>(AllPeople.class);
            InputStream is = new FileInputStream(new File("F:/HCZY/Excel导入完成备份/待处理/2022-04-15 外台军机构人员关系/美印太以外人物/people.xlsx"));
            List<AllPeople> list = util.importExcel("Sheet1", is);
            is.close();
            if (list.size() > 0) {
                for (AllPeople allPeople : list) {
                    count++;
                    allPeople.setType("印太以外");
                    allPeopleMapper.insertAllPeople(allPeople);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "导入[ " + count + " ]条！";*/
        return null;
    }

    @Autowired
    private IdwOrgMapper idwOrgMapper;
    @Autowired
    private IdwPeopleMainMapper idwPeopleMainMapper;
    @Autowired
    private IdwOrgStaffMapper idwOrgStaffMapper;

    //开始机构编码
    private static Long beginOrgCode;

    /**
     * 导入机构架构与机构人员
     *
     * @return 结果
     */
    @Override
    public String insertOrgStructureAndOrgStaff() {
        //O02000
        beginOrgCode = (long) 2000;
        //印太以内数据
        List<OriginalPeople> peopleList = testMapper.selectAllPeopleOriginalData().stream().filter(p -> !p.getType().equals("印太以外")).collect(Collectors.toList());
        //处理机构关系
        for (OriginalPeople people : peopleList) {
            insertOrgRelationship(people, peopleList);
        }
        return null;
    }

    /**
     * 机构架构迁移
     *
     * @return 结果
     */
    @Override
    public String orgStructureMigrate() {
        /*List<IdwOrgRelationship> orgRelationshipList = testMapper.selectAllOrgStructureMigrateByPatentOrgCode("0");
        for (IdwOrgRelationship orgRelationship : orgRelationshipList) {
            String oldOrgCode = orgRelationship.getOrgCode();
            long id = SnowIdUtils.uniqueLong();
            IdwOrg org = testMapper.selectOrgMigrateByOrgCode(oldOrgCode);
            //修改机构编码
            String orgCode = oldOrgCode + "TWTP";
            orgRelationship.setAuditPhase("not_submit");
            orgRelationship.setAssignUserId((long) 1);
            orgRelationship.setOrgCode(orgCode);
            orgRelationship.setRelationshipId(id);//重新赋值ID
            idwOrgRelationshipMapper.insert(orgRelationship);
            org.setOrgCode(orgCode);
            org.setUniqueCode(orgCode);
            if (StringUtils.isBlank(org.getSource())) {
                org.setSource("内部");
            }
            idwOrgMapper.insertIdwOrg(org);
            orgStructureMigrate(oldOrgCode, id);
        }*/
        return "迁移成功";
    }

    /**
     * 校验all_people表文件是否存在
     *
     * @return 结果
     */
    @Override
    public String verifyAllPeopleFileIsExist() {
        List<AllPeople> allPeopleList = testMapper.selectAllPeople();
        for (AllPeople people : allPeopleList) {
            boolean isExist = true;
            String suffix = "";
            File file = new File("D:/webdp/uploadPath/resumes/" + people.getPeopleId() + " " + people.getFullName() + "/" + people.getFullName() + ".pdf");
            if (file.exists()) {
                suffix = ".pdf";
            } else {
                file = new File("D:/webdp/uploadPath/resumes/" + people.getPeopleId() + " " + people.getFullName() + "/" + people.getFullName() + ".doc");
                if (file.exists()) {
                    suffix = ".doc";
                } else {
                    isExist = false;
                }
            }
            if (isExist) {
                String newPath = "D:/webdp/uploadPath/peopleFile/" + people.getFullName() + suffix;
                File newFile = new File(newPath);
                try {
                    int byteread = 0;
                    //判断新文件路径中包含的所有文件夹是否存在
                    if (!newFile.getParentFile().exists()) {
                        //如果文件夹不存在就新建
                        newFile.getParentFile().mkdirs();
                    }
                    InputStream is = new FileInputStream(file);
                    FileOutputStream fs = new FileOutputStream(newPath);
                    byte[] buffer = new byte[1024];
                    while ((byteread = is.read(buffer)) != -1) {
                        fs.write(buffer, 0, byteread);
                    }
                    is.close();
                } catch (IOException exception) {
                    exception.printStackTrace();
                }
            } else {
                //删除人员
                testMapper.deleteAllPeopleByPeopleId(people.getPeopleId());
            }
        }
        return "操作成功！";
    }

    /**
     * 重构外台军机构关系
     *
     * @return 结果
     */
    @Override
    public String reconsitutionWtjOrgRelationship() {
        deleteRepetitionRelationshipTopORgRelationship();
        //return reconsitutionOrgRelationshipByFile();
        //外台军架构补充
        /*List<IdwOrgStaff> orgStaffList = testMapper.selectPeopleOrgStaff();
        for (IdwOrgStaff orgStaff : orgStaffList) {
            IdwOrgStaff staff = idwOrgStaffMapper.selectByOrgCodeAndPeopleName(orgStaff.getOrgCode(), orgStaff.getPeopleNameCn(), orgStaff.getPeopleNameEn(), (long) 0);
            if (StringUtils.isNull(staff)) {
                idwOrgStaffMapper.insertIdwOrgStaff(orgStaff);
            }
        }*/
        //朱俊伟架构原始数据导入
        //insertNewPeople();
        //原始数据导入
        //newOrgRelReplenish();
        //架构导入
        //organizationChartreconsitution();
        //机构编码赋值
        //valuationNewOrgRelOrgCode();
        //架构调整
        //newOrgRelAdjustment();
        //验证架构
        /*List<OldOrgRel> oldOrgRelList = testMapper.selectAllOldOrgRel();
        for (int i = 0; i < oldOrgRelList.size(); i++) {
            if (i == 0) {
                continue;
            }
            OldOrgRel oldOrgRel = oldOrgRelList.get(i);
            int count = testMapper.selectNewOrgRelByNameEnAndParentName(oldOrgRel.getNameEn().trim(), testMapper.selectOldOrgRelNameByOrgCode(oldOrgRel.getParentCode()).trim());
            if (count < 1) {
                return oldOrgRel.getId() + "关系丢失";
            }
        }*/
        //架构修改
        //organizationChartUpdate();
        return "操作成功！";
    }

    //删除机构关系为多个中顶层关系
    public void deleteRepetitionRelationshipTopORgRelationship() {
      /*  List<OrgRelationship> orgRelationshipList = testMapper.selectTopOrgRel();
        testMapper.updateOrgRelUpdateByById(null, null);//修改用户置空
        for (OrgRelationship orgRelationship : orgRelationshipList) {
            if (!"T10709".equals(orgRelationship.getOrgCode())) {
                //查询原始数据中上级机构
                List<String> parentNameList = testMapper.selectNewOrgRelParentNameByOrgName(orgRelationship.getOrgName());
                List<String> newParentNameList = new ArrayList<>();
                //拆分上级机构名称
                for (String parentOrgName : parentNameList) {
                    String[] parentOrgNames = parentOrgName.split("[;；]");
                    for (String orgName : parentOrgNames) {
                        if (!newParentNameList.contains(orgName)) {
                            newParentNameList.add(orgName);
                        }
                    }
                }
                //查询当前机构现数据库中所有上级
                List<String> currentParentNameList = testMapper.selectOrgRelParentOrgNameByOrgCode(orgRelationship.getOrgCode());
                for (String parentName : parentNameList) {
                    if (!currentParentNameList.contains(parentName)) {
                        //不存在
                        List<IdwOrg> orgList = idwOrgMapper.selectOrgByOrgName(parentName, null);
                        if (orgList.size() > 0) {
                            if (orgList.size() == 1) {
                                String orgCode = orgList.get(0).getOrgCode();
                                if ("Z000611".equals(orgRelationship.getOrgCode())) {
                                    System.out.println();
                                }
                                //上级机构机构关系
                                List<IdwOrgRelationship> orgRelationships = idwOrgRelationshipMapper.selectByOrgCodeAndOrgType(orgCode, "国防单位");
                                IdwOrgRelationship relationship = orgRelationships.get(0);
                                String oldAncestors = orgRelationship.getAncestors();//原祖籍列表
                                orgRelationship.setParentCode(relationship.getOrgCode());
                                orgRelationship.setParentId(relationship.getRelationshipId());
                                orgRelationship.setAncestors(relationship.getAncestors() + "," + relationship.getOrgCode());
                                //修改子集祖籍列表
                                testMapper.updateChildrenParentIdByByOrgCodeAndAncestors(orgRelationship.getOrgCode(), oldAncestors, orgRelationship.getAncestors());
                                testMapper.updateChildrenAncestorsByOrgCodeAndAncestors(orgRelationship.getOrgCode(), oldAncestors, orgRelationship.getAncestors());
                                idwOrgRelationshipMapper.updateById(orgRelationship);
                            }
                        } else {
                            //将找不到上级机构的上级机构名称存放在当前机构关系数据修改用户中
                            //testMapper.updateOrgRelUpdateByById(orgRelationship.getRelationshipId(), parentName);
                        }
                    }
                }
            }
        }
        vindicateOrgLevel();*/
    }

    //修改组织架构
    public void organizationChartUpdate() {
        try {
            ExcelUtil<NewOrgRel> util = new ExcelUtil<NewOrgRel>(NewOrgRel.class);
            FileInputStream is = new FileInputStream(new File("F:\\HCZY\\Excel导入完成备份\\待处理\\2022-05-20 外台军架构核对完成(朱俊伟)\\架构0520.xlsx"));
            List<NewOrgRel> list = util.importExcel("Sheet1", is);
            List<NewOrgRel> updateList = list.stream().filter(p -> StringUtils.isNotBlank(p.getRemark())).collect(Collectors.toList());
            //删除的节点
            List<Long> deleteList = updateList.stream().filter(p -> p.getRemark().contains("删除")).map(NewOrgRel::getId).collect(Collectors.toList());
            deleteNewOrgRelById(deleteList);

            //新增的节点
            List<NewOrgRel> insertList = updateList.stream().filter(p -> p.getRemark().contains("新增")).collect(Collectors.toList());
            for (NewOrgRel newOrgRel : insertList) {
                testMapper.insertNewOrgRel(newOrgRel);
            }
            is.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据机构关系ID删除
     *
     * @param idList 关系ID
     */
    public void deleteNewOrgRelById(List<Long> idList) {
        Long[] ids = idList.toArray(new Long[0]);
        List<Long> childrenIdList = testMapper.selectNewOrgRelByParentIds(ids);
        if (childrenIdList.size() > 0) {
            deleteNewOrgRelById(childrenIdList);
        }
        testMapper.deleteNewOrgRelByIds(ids);
    }

    /**
     * 架构校对
     */
    public void newOrgRelAdjustment() {
        List<NewOrgRel> newOrgRelList = testMapper.selectNewOrgRelNotFoundParent();
        for (NewOrgRel newOrgRel : newOrgRelList) {
            String parentName = newOrgRel.getParentName().trim();
            List<NewOrgRel> newOrgRels = testMapper.selectNewOrgRelByName(parentName);
            if (newOrgRels.size() > 0) {
                testMapper.deleteNewOrgRelById(newOrgRel.getId());
                int count = testMapper.selectNewOrgRelByNameEnAndParentName(newOrgRel.getNameEn(), parentName);
                if (count < 1) {
                    for (NewOrgRel parentOrgRel : newOrgRels) {
                        NewOrgRel orgRel = new NewOrgRel();
                        orgRel.setParentId(parentOrgRel.getId());
                        orgRel.setParentName(parentName);
                        orgRel.setOrgCode(parentOrgRel.getOrgCode());
                        orgRel.setNameCn(parentOrgRel.getNameCn());
                        orgRel.setNameEn(parentOrgRel.getNameEn());
                        orgRel.setSource(parentOrgRel.getSource());
                        testMapper.insertNewOrgRel(orgRel);
                    }
                }
            }
        }
    }

    /**
     * 组织架构补充
     */
    public void newOrgRelReplenish() {
        try {
            ExcelUtil<Organization> util = new ExcelUtil<Organization>(Organization.class);
            InputStream is = new FileInputStream(new File("F:\\HCZY\\Excel导入完成备份\\外台军\\国防单位\\2022-04-27 外台军编制架构重构(朱俊伟)\\外台编制（校对）0426.xlsx"));
            List<Organization> list = util.importExcel("Sheet1", is);
            for (Organization organization : list) {
                OldOrgRel oldOrgRel = new OldOrgRel();
                oldOrgRel.setOrgCode(organization.getOrgCode());
                oldOrgRel.setNameEn(organization.getOrgNameEn());
                oldOrgRel.setNameCn(organization.getOrgNameCn());
                oldOrgRel.setParentCode(organization.getParentCodeOne());
                oldOrgRel.setSource(organization.getSource());
                testMapper.insertOldOrgRel(oldOrgRel);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 外台军组织架构重构
     */
    public void organizationChartreconsitution() {
        //原始架构
     /*List<OldOrgRel> oldOrgRelList = testMapper.selectAllOldOrgRel();
        for (OldOrgRel oldOrgRel : oldOrgRelList) {
            NewOrgRel newOrgRel = new NewOrgRel();
            newOrgRel.setOrgCode(oldOrgRel.getOrgCode());
            newOrgRel.setParentCode(oldOrgRel.getParentCode());
            newOrgRel.setNameCn(oldOrgRel.getNameCn());
            newOrgRel.setNameEn(oldOrgRel.getNameEn());
            newOrgRel.setSource(oldOrgRel.getSource());
            String parentName = testMapper.selectOldOrgRelNameByOrgCode(oldOrgRel.getParentCode());
            if (StringUtils.isBlank(parentName)) {
                //顶层
                newOrgRel.setParentId((long) 0);
                newOrgRel.setParentCode("0");
                testMapper.insertNewOrgRel(newOrgRel);
            } else {
                List<NewOrgRel> parentNewOrgRels = testMapper.selectNewOrgRelByName(parentName);
                if (parentNewOrgRels.size() > 0) {
                    for (NewOrgRel parentNewOrgRel : parentNewOrgRels) {
                        newOrgRel.setParentId(parentNewOrgRel.getId());
                        int count = testMapper.selectNewOrgRelByNameEnAndParentName(newOrgRel.getNameEn(), parentName);
                        if (count < 1) {
                            testMapper.insertNewOrgRel(newOrgRel);
                        }
                    }
                } else {
                    newOrgRel.setParentId((long) -0);
                    newOrgRel.setParentCode("0");
                    newOrgRel.setParentName(parentName);
                    testMapper.insertNewOrgRel(newOrgRel);
                }
            }
        }*/

        //朱俊伟架构
        List<NewPeople> peopleList = testMapper.selectAllNewPeople();
        List<NewOrgRel> list = new ArrayList<>();
        for (NewPeople newPeople : peopleList) {
            NewOrgRel newOrgRel = new NewOrgRel();
            newOrgRel.setNameCn(newPeople.getOrgNameCn());
            newOrgRel.setNameEn(newPeople.getOrgNameEn());
            newOrgRel.setParentName(newPeople.getParentName());
            newOrgRel.setSource(newPeople.getSource());
            list.add(newOrgRel);
        }
        for (NewOrgRel newOrgRel : list) {
            String parentName = newOrgRel.getParentName();
            if (StringUtils.isBlank(parentName)) {
                //顶层
                newOrgRel.setParentId((long) 0);
                newOrgRel.setParentCode("0");
                int count = testMapper.selectNewOrgRelByNameEnAndParentName(newOrgRel.getNameEn(), parentName);
                if (count < 1) {
                    testMapper.insertNewOrgRel(newOrgRel);
                }
            } else {
                String[] parentNames = parentName.split("[;；]");
                for (String pName : parentNames) {
                    List<NewOrgRel> parentNewOrgRels = testMapper.selectNewOrgRelByName(pName);
                    String[] nameCns = newOrgRel.getNameCn().split("[;；]");
                    String[] nameEns = newOrgRel.getNameEn().split("[;；]");
                    if (parentNewOrgRels.size() > 0) {
                        for (NewOrgRel parentNewOrgRel : parentNewOrgRels) {
                            for (int i = 0; i < nameCns.length; i++) {
                                newOrgRel.setParentId(parentNewOrgRel.getId());
                                newOrgRel.setNameCn(nameCns[i]);
                                newOrgRel.setNameEn(nameEns[i]);
                                newOrgRel.setParentName(pName);
                                int count = testMapper.selectNewOrgRelByNameEnAndParentName(newOrgRel.getNameEn(), pName);
                                if (count < 1) {
                                    testMapper.insertNewOrgRel(newOrgRel);
                                }
                            }
                        }
                    } else {
                        //上级不存在
                        for (int i = 0; i < nameCns.length; i++) {
                            newOrgRel.setParentId((long) -1);
                            newOrgRel.setParentCode("0");
                            newOrgRel.setNameCn(nameCns[i]);
                            newOrgRel.setNameEn(nameEns[i]);
                            newOrgRel.setParentName(pName);
                            int count = testMapper.selectNewOrgRelByNameEnAndParentName(newOrgRel.getNameEn(), pName);
                            if (count < 1) {
                                testMapper.insertNewOrgRel(newOrgRel);
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 机构编码赋值 T10709
     */
    public void valuationNewOrgRelOrgCode() {
        List<NewOrgRel> newOrgRelList = testMapper.selectNewOrgRelByOrgCodeIsNull();
        for (int i = 0; i < newOrgRelList.size(); i++) {
            String prefix = "Z";
            String code = "000000" + i;
            NewOrgRel newOrgRel = newOrgRelList.get(i);
            testMapper.updateNewOrgRelOrgCodeByNameEn(prefix + code.substring(code.length() - 6), newOrgRel.getNameEn());
        }
    }

    /**
     * 新增人员
     */
    public void insertNewPeople() {
        try {
            ExcelUtil<NewPeople> util = new ExcelUtil<NewPeople>(NewPeople.class);
            List<NewPeople> list = util.importExcel("司令部", new FileInputStream(new File("C:\\Users\\<USER>\\Desktop\\p1.xlsx")));
            list.addAll(util.importExcel("太平洋舰队", new FileInputStream(new File("C:\\Users\\<USER>\\Desktop\\p1.xlsx"))));
            list.addAll(util.importExcel("太平洋陆军", new FileInputStream(new File("C:\\Users\\<USER>\\Desktop\\p1.xlsx"))));
            list.addAll(util.importExcel("太平洋海军陆战队", new FileInputStream(new File("C:\\Users\\<USER>\\Desktop\\p1.xlsx"))));
            list.addAll(util.importExcel("太平洋空军", new FileInputStream(new File("C:\\Users\\<USER>\\Desktop\\p1.xlsx"))));
            list.addAll(util.importExcel("Sheet1", new FileInputStream(new File("C:\\Users\\<USER>\\Desktop\\p2.xlsx"))));
            for (NewPeople newPeople : list) {
                testMapper.insertNewPeople(newPeople);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 构建新的架构
     *
     * @return 结果
     */
    @Override
    public List<StructureChart> buildNewOrgchart() {
        return testMapper.buildNewOrgchart();
    }

    @Autowired
    private IdwOrgNewsMapper idwOrgNewsMapper;
    @Autowired
    private IdwOrgProjectMapper idwOrgProjectMapper;
    @Autowired
    private IdwOrgDocumentMapper idwOrgDocumentMapper;
    @Autowired
    private IdwPeopleNewsMapper idwPeopleNewsMapper;
    @Autowired
    private IdwWeaponrySpecificationsMapper idwWeaponrySpecificationsMapper;

    /**
     * 机构文献翻译
     *
     * @return 结果
     */
    @Override
    public String documentTranslate() {
        long documentTitleUpdateCount = 0;
        long documentSummaryUpdateCount = 0;
        try {
            Date nowDate = DateUtils.getNowDate();
            documentTitleUpdateCount = 0;
            documentSummaryUpdateCount = 0;
            List<IdwOrgDocument> orgDocumentList = testMapper.selectOrgDocument();
            for (IdwOrgDocument document : orgDocumentList) {
                boolean isUpdate = false;
                if (StringUtils.isBlank(document.getTitleCn()) && StringUtils.isNotBlank(document.getTitleEn())) {
                    //String text = CSTranslateUtils.englishTranslate(document.getTitleEn());
                    //String text = CSTranslateUtils.translate(document.getTitleEn());
                    String text = BaiDuTranslateUtils.translate(document.getTitleEn());
                    if (StringUtils.isNotBlank(text)) {
                        document.setTitleCn(text);
                        isUpdate = true;
                        documentTitleUpdateCount++;
                    }
                }
                if (StringUtils.isBlank(document.getSummaryCn()) && StringUtils.isNotBlank(document.getSummaryEn())) {
                    //String text = CSTranslateUtils.englishTranslate(document.getSummaryEn());
                    //String text = CSTranslateUtils.translate(document.getSummaryEn());
                    String text = BaiDuTranslateUtils.translate(document.getSummaryEn());
                    if (StringUtils.isNotBlank(text)) {
                        document.setSummaryCn(text);
                        isUpdate = true;
                        documentSummaryUpdateCount++;
                    }
                }
                document.setUpdateBy("传神翻译");
                document.setUpdateTime(nowDate);
                if (isUpdate) {
                    idwOrgDocumentMapper.updateIdwOrgDocument(document);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "机构文献标题翻译【" + documentTitleUpdateCount + "】条，" + "摘要翻译【" + documentSummaryUpdateCount + "】条。";
    }

    /**
     * 机构项目翻译
     *
     * @return 结果
     */
    @Override
    public String projectTranslate() {
        long projectTitleUpdateCount = 0;
        long projectDescriptionUpdateCount = 0;
        try {
            Date nowDate = DateUtils.getNowDate();
            projectTitleUpdateCount = 0;
            projectDescriptionUpdateCount = 0;
            List<IdwOrgProject> orgProjectList = testMapper.selectOrgProject();
            for (IdwOrgProject project : orgProjectList) {
                boolean isUpdate = false;
                if (StringUtils.isBlank(project.getTitleCn()) && StringUtils.isNotBlank(project.getTitleEn())) {
                    //String text = CSTranslateUtils.englishTranslate(project.getTitleEn());
                    String text = CSTranslateUtils.translate(project.getTitleEn());
                    if (StringUtils.isNotBlank(text)) {
                        project.setTitleCn(text);
                        isUpdate = true;
                        projectTitleUpdateCount++;
                    }
                }
                if (StringUtils.isBlank(project.getDescriptionCn()) && StringUtils.isNotBlank(project.getDescriptionEn())) {
                    //String text = CSTranslateUtils.englishTranslate(project.getDescriptionEn());
                    String text = CSTranslateUtils.translate(project.getDescriptionEn());
                    if (StringUtils.isNotBlank(text)) {
                        project.setDescriptionCn(text);
                        isUpdate = true;
                        projectDescriptionUpdateCount++;
                    }
                }
                if (isUpdate) {
                    project.setUpdateBy("传神翻译");
                    project.setUpdateTime(nowDate);
                    idwOrgProjectMapper.updateIdwOrgProject(project);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "机构项目标题翻译【" + projectTitleUpdateCount + "】条，" + "内容翻译【" + projectDescriptionUpdateCount + "】条。";
    }

    /**
     * 机构新闻翻译
     *
     * @return 结果
     */
    @Override
    public String newsTranslate() {
        long newsTitleUpdateCount = 0;
        long newsContentUpdateCount = 0;
        long newsSummaryUpdateCount = 0;
        try {
            Date nowDate = DateUtils.getNowDate();
            newsTitleUpdateCount = 0;
            newsContentUpdateCount = 0;
            newsSummaryUpdateCount = 0;
            List<IdwOrgNews> orgNewsList = testMapper.selectOrgNews();
            for (IdwOrgNews news : orgNewsList) {
                boolean isUpdate = false;
                /*if (StringUtils.isBlank(news.getTitleCn()) && StringUtils.isNotBlank(news.getTitleEn())) {
                    //String text = CSTranslateUtils.englishTranslate(news.getTitleEn());
                    String titleText = BaiDuTranslateUtils.translate(news.getTitleEn());
                    if (StringUtils.isNotBlank(titleText)) {
                        news.setTitleCn(titleText);
                        isUpdate = true;
                        newsTitleUpdateCount++;
                    }
                }*/
                if (StringUtils.isBlank(news.getContentCn()) && StringUtils.isNotBlank(news.getContentEn())) {
                    //String text = CSTranslateUtils.englishTranslate(news.getContentEn());
                    String contentText = BaiDuTranslateUtils.translate(news.getContentEn());
                    if (StringUtils.isNotBlank(contentText)) {
                        news.setContentCn(contentText);
                        isUpdate = true;
                        newsContentUpdateCount++;
                    }
                }
                if (StringUtils.isBlank(news.getSummaryCn()) && StringUtils.isNotBlank(news.getSummaryEn())) {
                    //String text = CSTranslateUtils.englishTranslate(news.getSummaryEn());
                    String summaryText = BaiDuTranslateUtils.translate(news.getSummaryEn());
                    if (StringUtils.isNotBlank(summaryText)) {
                        news.setSummaryCn(summaryText);
                        isUpdate = true;
                        newsSummaryUpdateCount++;
                    }
                }
                news.setUpdateBy("百度翻译");
                news.setUpdateTime(nowDate);
                testMapper.updateOrgNews(news);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "机构新闻标题翻译【" + newsTitleUpdateCount + "】条，" + "摘要翻译【" + newsSummaryUpdateCount + "】条，" + "内容翻译【" + newsContentUpdateCount + "】条。";
    }

    /**
     * 翻译
     *
     * @return 结果
     */
    @Override
    public String translate() {
        return documentTranslate() + projectTranslate() + newsTranslate();
    }

    /**
     * 获取机构人员简历并赋值
     *
     * @return 结果
     */
    @Override
    public String getOrgStaffProfile() {
        List<IdwOrgStaff> orgStaffList = testMapper.selectOrgStaffProfileIsPdfFile();
        for (IdwOrgStaff staff : orgStaffList) {
            String pdfFilePath = staff.getProfileEn();
            //获取后缀
            String extension = pdfFilePath.substring(pdfFilePath.lastIndexOf(".") + 1);
            if ("pdf".equals(extension)) {
                try (PDDocument document = PDDocument.load(new File("C:\\Users\\<USER>\\Desktop\\外台军美军印太以外人员\\简历文件\\" + pdfFilePath.replace("见", "")))) {
                    if (!document.isEncrypted()) {
                        PDFTextStripperByArea stripper = new PDFTextStripperByArea();
                        stripper.setSortByPosition(true);
                        PDFTextStripper textStripper = new PDFTextStripper();
                        String pdfFileText = textStripper.getText(document);
                        if (StringUtils.isNotBlank(pdfFileText)) {
                            try {
                                testMapper.updateOrgStaffProfileEn(staff.getStaffId(), pdfFileText);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        }
                    }
                } catch (IOException exception) {
                    exception.printStackTrace();
                }
            }
        }
        return null;
    }

    /**
     * 合并机构中文名称类似机构
     *
     * @return 结果
     */
    @Override
    public String mergeOrgNameCnLikenessOrg() {
        try {
            List<String> orgCodeList = new ArrayList<>();
            ExcelUtil<Organization> util = new ExcelUtil<Organization>(Organization.class);
            InputStream is = new FileInputStream(new File("C:\\Users\\<USER>\\Desktop\\已核对\\名称完全重合机构.xlsx"));
            List<Organization> list = util.importExcel("Sheet1", is);
            for (int i = 0; i < list.size(); i++) {
                Organization one = list.get(i);
                Organization two = list.get(++i);
                Organization source = new Organization();
                if (one.getOrgNameCn().equals(two.getOrgNameCn())) {
                    Organization target = new Organization();
                    if (!one.getOrgCode().contains("Z")) {
                        source = one;
                        target = two;
                    } else {
                        source = two;
                        target = one;
                    }
                    orgCodeList.add(source.getOrgCode());
                    mergeOrg(source, target);
                }
                if (i == list.size() - 1) {
                    continue;
                }
                int j = i;
                Organization three = list.get(j + 1);
                if (one.getOrgNameCn().equals(three.getOrgNameCn())) {
                    i++;
                    Organization target = new Organization();
                    if (!three.getOrgCode().contains("Z")) {
                        target = source;
                        source = three;
                    } else {
                        target = three;
                    }
                    orgCodeList.add(source.getOrgCode());
                    mergeOrg(source, target);
                }
            }
            String orgCodes = StringUtils.join(orgCodeList, ",");
            System.err.println(orgCodes);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "操作成功！";
    }

    /**
     * 合并机构
     *
     * @param source 合并到的机构 留存机构
     * @param target 需要合并的机构 删除的机构
     */
    public void mergeOrg(Organization source, Organization target) {
        String sourceOrgCode = source.getOrgCode();
        String targetOrgCode = target.getOrgCode();
        int relCount = testMapper.selectOrgRelCountByOrgCode(targetOrgCode);
        if (relCount == 1) {
            testMapper.updateOrgStaffOrgCode(sourceOrgCode, targetOrgCode);
        }
        testMapper.updateOrgRelByOrgCode(sourceOrgCode, targetOrgCode);
    }

    /**
     * 人员编码赋值
     *
     * @return 结果
     */
    @Override
    public String assignmentPeopleCode() {
        List<IdwPeopleMain> peopleList = testMapper.selectNeedAssignmentPeopleCode();
        int maxOrderNum = idwPeopleMainMapper.selectMaxOrderNum("政府军队官员");
        String maxPeopleCode = "P0028999";
        for (IdwPeopleMain people : peopleList) {
            String newPeopleCodeNumber = "0000000" + String.valueOf(Long.valueOf(maxPeopleCode.substring(1)) + 1);
            String newPeopleCode = maxPeopleCode.charAt(0) + newPeopleCodeNumber.substring(newPeopleCodeNumber.length() - 7);
            maxPeopleCode = newPeopleCode;
            testMapper.updatePeopleCodeAndOrderNumByPeopleId(people.getPeopleId(), newPeopleCode, maxOrderNum++);
        }
        return null;
    }

    /**
     * 军事力量数据导入
     *
     * @return 结果
     */
    @Override
    public String jsllImport() {
        try {
            Date nowDate = DateUtils.getNowDate();
            ExcelUtil<IdwResource> resourceUtil = new ExcelUtil<IdwResource>(IdwResource.class);
            InputStream resourceIs = new FileInputStream(new File("C:\\Users\\<USER>\\Desktop\\Desktop\\资源库.xlsx"));
            List<IdwResource> resourceList = resourceUtil.importExcel("文献", resourceIs);
            resourceIs.close();
            for (IdwResource resource : resourceList) {
                resource.setCreateBy("wangchao");
                resource.setCreateTime(nowDate);
                if (StringUtils.isBlank(resource.getSource())) {
                    resource.setSource("暂无");
                }
                idwResourceMapper.insertIdwResource(resource);
            }
            ExcelUtil<IdwResourceCategory> resourceCategoryUtil = new ExcelUtil<IdwResourceCategory>(IdwResourceCategory.class);
            InputStream resourceCategoryIs = new FileInputStream(new File("C:\\Users\\<USER>\\Desktop\\Desktop\\资源库.xlsx"));
            List<IdwResourceCategory> resourceCategorylist = resourceCategoryUtil.importExcel("文献关联文献分类", resourceCategoryIs);
            resourceCategoryIs.close();
            for (IdwResourceCategory resourceCategory : resourceCategorylist) {
                resourceCategory.setCreateBy("wangchao");
                resourceCategory.setCreateTime(nowDate);
                idwResourceCategoryMapper.insertIdwResourceCategory(resourceCategory);
            }
            /*ExcelUtil<IdwResourceDictCategory> resourceDictCategoryUtil = new ExcelUtil<IdwResourceDictCategory>(IdwResourceDictCategory.class);
            InputStream resourceDictCategoryIs = new FileInputStream(new File("C:\\Users\\<USER>\\Desktop\\Desktop\\资源库.xlsx"));
            List<IdwResourceDictCategory> resourceDictCategorylisL = resourceDictCategoryUtil.importExcel("文献分类", resourceDictCategoryIs);
            resourceDictCategoryIs.close();
            for (IdwResourceDictCategory resourceDictCategory : resourceDictCategorylisL) {

            }*/
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * 军事力量机构关系相关
     *
     * @return 结果
     */
    @Override
    public String jsllOrgRel() {
        /*Date nowDate = DateUtils.getNowDate();
        List<IdwOrg> orgList = testMapper.selectAllOrg();
        for (IdwOrg org : orgList) {
            List<IdwOrgRelationship> orgRelationshipList = testMapper.selectOrgRelationshipByOrgCode(org.getOrgCode());
            if (orgRelationshipList.size() > 0) {
                IdwOrgRelationship orgRelationship = orgRelationshipList.get(0);
                if (StringUtils.isNotNull(orgRelationship)) {
                    orgRelationship.setOrgType(org.getOrgType());
                    IdwOrgRelationship parentOrgRelationship = testMapper.selectOrgRelationshipByOrgCode(orgRelationship.getParentCode()).get(0);
                    orgRelationship.setParentId(parentOrgRelationship.getRelationshipId());
                    orgRelationship.setAncestors(parentOrgRelationship.getAncestors() + "," + orgRelationship.getParentCode());
                    orgRelationship.setLevel((long) orgRelationship.getAncestors().split(",").length);
                    orgRelationship.setAuditPhase("not_submit");
                    orgRelationship.setCreateBy("admin");
                    orgRelationship.setCreateTime(nowDate);
                    idwOrgRelationshipMapper.updateById(orgRelationship);

                } else {
                    IdwOrgRelationship relationship = new IdwOrgRelationship();
                    relationship.setOrgCode(org.getOrgCode());
                    relationship.setParentCode("0");
                    relationship.setParentId((long) -1);
                    relationship.setAncestors("0");
                    relationship.setOrgType(org.getOrgType());
                    relationship.setLevel((long) 1);
                    relationship.setAuditPhase("not_submit");
                    relationship.setCreateBy("admin");
                    relationship.setCreateTime(nowDate);
                    idwOrgRelationshipMapper.insert(relationship);
                }
            } else {
                IdwOrgRelationship relationship = new IdwOrgRelationship();
                relationship.setOrgCode(org.getOrgCode());
                relationship.setParentCode("0");
                relationship.setParentId((long) -1);
                relationship.setAncestors("0");
                relationship.setOrgType(org.getOrgType());
                relationship.setLevel((long) 1);
                relationship.setAuditPhase("not_submit");
                relationship.setCreateBy("admin");
                relationship.setCreateTime(nowDate);
                idwOrgRelationshipMapper.insert(relationship);
            }
        }*/
        return null;
    }

    /**
     * 验证文件是否存在
     *
     * @return 结果
     */
    @Override
    public List<String> verifyFileIsExist() {
        //文献
        //List<String> filePathList = idwOrgDocumentMapper.selectAllFilePath();
        //机构
        //List<String> filePathList = idwOrgMapper.selectAllFilePath();
        //机构财务状况
        //List<String> filePathList = idwOrgFinancialMapper.selectAllFilePath();
        //机构产品
        //List<String> filePathList = idwOrgProductMapper.selectAllFilePath();
        //机构人员
        //List<String> filePathList = idwOrgStaffMapper.selectAllFilePath();

        //人员
        List<String> filePathList = idwPeopleMainMapper.selectAllFilePath();

        List<String> notIsExistFilePath = new ArrayList<>();
        Long count = (long) 0;
        for (String filePath : filePathList) {
            count++;
            File file = new File(filePath.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile()));
            if (!file.exists()) {
                notIsExistFilePath.add(filePath);
            }
        }
        notIsExistFilePath.add(String.valueOf(count));
        return notIsExistFilePath;
    }

    /**
     * 机构文献名称为中文时将文件名称修改为md5
     *
     * @return 结果
     */
    @Override
    public String orgDocumentCnFileNameReplaceMd5() {
        List<String> inexistenceFilePath = new ArrayList<>();
        List<IdwOrgDocument> orgDocumentList = idwOrgDocumentMapper.selectIdwOrgDocumentList(new IdwOrgDocument());
        for (IdwOrgDocument document : orgDocumentList) {
            boolean isUpdate = false;
            String storagePath = document.getStoragePath();
            if ((StringUtils.isNotBlank(storagePath) && isContainChinese(storagePath)) || storagePath.contains(" ")) {
                storagePath = storagePath.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile());
                String filePath = storagePath;
                File file = new File(filePath);
                if (file.exists()) {
                    String md5 = FileUtils.getMd5(filePath);
                    String newFilePath = file.getParent() + "/" + md5 + filePath.substring(filePath.lastIndexOf("."));
                    file.renameTo(new File(newFilePath));
                    document.setStoragePath(newFilePath.replace("\\", "/").replace(WebdpConfig.getProfile(), Constants.RESOURCE_PREFIX));
                    isUpdate = true;
                } else {
                    inexistenceFilePath.add(document.getDocumentId().toString() + "filePath:" + storagePath);
                }
            }
            String thumbnail = document.getThumbnail();
            if ((StringUtils.isNotBlank(thumbnail) && isContainChinese(thumbnail)) || thumbnail.contains(" ")) {
                thumbnail = thumbnail.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile());
                String filePath = thumbnail;
                File file = new File(filePath);
                if (file.exists()) {
                    String md5 = FileUtils.getMd5(filePath);
                    String newFilePath = file.getParent() + "/" + md5 + filePath.substring(filePath.lastIndexOf("."));
                    File newFile = new File(newFilePath);
                    if (!newFile.exists()) {
                        file.renameTo(newFile);
                    }
                    document.setThumbnail(newFilePath.replace("\\", "/").replace(WebdpConfig.getProfile(), Constants.RESOURCE_PREFIX));
                    isUpdate = true;
                    if (thumbnail.contains("thumbnail-")) {
                        String originalThumbnail = thumbnail.replace("thumbnail-", "");
                        File originalFile = new File(originalThumbnail);
                        if (originalFile.exists()) {
                            String newOriginalFilePath = originalFile.getParent() + "/" + md5 + originalThumbnail.substring(filePath.lastIndexOf("."));
                            File newOriginalFile = new File(newOriginalFilePath);
                            if (!newOriginalFile.exists()) {
                                originalFile.renameTo(newOriginalFile);
                            }
                        }
                    }
                } else if (storagePath.contains(".pdf")) {
                    try {
                        //生成缩略图
                        String pdfToImage = pdfToImage(storagePath);
                        String imgPath = pdfToImage.replace(WebdpConfig.getProfile(), Constants.RESOURCE_PREFIX);
                        document.setThumbnail(imgPath);
                    } catch (PDFSecurityException e) {
                        e.printStackTrace();
                        document.setThumbnail("");
                    } catch (PDFException e) {
                        e.printStackTrace();
                        document.setThumbnail("");
                    } catch (IOException exception) {
                        exception.printStackTrace();
                        document.setThumbnail("");
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                        document.setThumbnail("");
                    }
                    isUpdate = true;
                } else {
                    document.setThumbnail("");
                }
            }
            if (isUpdate) {
                idwOrgDocumentMapper.updateIdwOrgDocument(document);
            }
        }
        return JSONObject.toJSONString(inexistenceFilePath);
    }

    /**
     * 修改装备规格
     *
     * @return 结果
     */
    @Override
    public String updateSpecifications() {
        //formattingSpecifications();
        reconstructionId();
        return "操作成功！";
    }

    /**
     * 重新构建装备战技指标ID-修改为雪花ID
     */
    public void reconstructionId() {
        List<IdwWeaponrySpecifications> idwWeaponrySpecificationsList = idwWeaponrySpecificationsMapper.selectIdwWeaponrySpecificationsList(new IdwWeaponrySpecifications());
        for (IdwWeaponrySpecifications weaponrySpecifications : idwWeaponrySpecificationsList) {
            long id = SnowIdUtils.uniqueLong();// 获取雪花id
            Long specificationsId = weaponrySpecifications.getSpecificationsId();
            testMapper.updateWeaponrySpecificationsId(specificationsId, id);
            testMapper.updateWeaponrySpecificationsParentId(specificationsId, id);
        }
    }


    /**
     * 人员新闻新增作者
     *
     * @return 结果
     */
    @Override
    public String insertPeopleNewsAuthor() {
        List<IdwPeopleNews> newsList = idwPeopleNewsMapper.selectIdwPeopleNewsList(new IdwPeopleNews());
        newsList.stream().filter(news -> StringUtils.isBlank(news.getAuthor())).collect(Collectors.toList()).forEach(news -> {
            String source = news.getUrl();
            try {
                URI uri = new URI(source);
                String domain = uri.getHost();
                news.setAuthor(domain);
                idwPeopleNewsMapper.updateIdwPeopleNews(news);
            } catch (URISyntaxException e) {
                e.printStackTrace();
            }
        });
        return null;
    }

    /**
     * 格式化日期
     *
     * @return 结果
     */
    @Override
    public String formatDate(FormatDate formatDate) {
        String tableNames = "idw_org,idw_people_main,idw_resource";
        String primaryKeys = "org_id,people_id,resource_id";
        String pendingFields = "establish_time,appointment_date,publish_date";
        String[] tableNameArr = tableNames.split(",");
        String[] primaryKeyArr = primaryKeys.split(",");
        String[] pendingFieldsArr = pendingFields.split(",");
        for (int i = 0; i < tableNameArr.length; i++) {
            formatDate.setTableName(tableNameArr[i]);
            formatDate.setPrimaryKey(primaryKeyArr[i]);
            formatDate.setPendingField(pendingFieldsArr[i]);
            List<FormatDate> dateList = testMapper.selectDate(formatDate);
            dateList.forEach(date -> {
                //格式化日期
                String d = DateUtils.updateDateSeparator(date.getValue());
                if (StringUtils.isNotBlank(d)) {
                    if (!d.equals(date.getValue())) {
                        testMapper.updateDate(formatDate.getTableName(), formatDate.getPrimaryKey(), date.getKeyValue(), formatDate.getPendingField(), d);
                    }
                } else {

                    System.out.println();
                }
            });
        }
        return null;
    }

    /**
     * 查询机构关系
     *
     * @return 结果
     */
    @Override
    public List<IdwOrgRelationship> selectOrgRel() {
        return testMapper.selectOrgRel();
    }

    /**
     * 根据机构表中org_code parent_code 将关系迁移到机构关系表中
     *
     * @return 结果
     */
    @Override
    public String insertOrgRel() {
        /*Date nowDate = DateUtils.getNowDate();
        List<IdwOrg> orgList = testMapper.selectAllOrg();
        for (IdwOrg org : orgList) {
            IdwOrgRelationship orgRelationship = new IdwOrgRelationship();
            orgRelationship.setOrgCode(org.getOrgCode());
            orgRelationship.setParentCode(org.getParentCode());
            orgRelationship.setAncestors("0");
            orgRelationship.setOrgType(org.getOrgType());
            orgRelationship.setAuditPhase("not_submit");
            orgRelationship.setAssignUserId((long) 1);
            orgRelationship.setLevel((long) 0);
            orgRelationship.setCreateBy("后勤");
            orgRelationship.setCreateTime(nowDate);
            idwOrgRelationshipMapper.insert(orgRelationship);
        }
        List<OrgRelationship> orgRelationshipList = testMapper.selectTopOrgRel();
        for (OrgRelationship orgRelationship : orgRelationshipList) {
            orgRelationship.setParentId((long) -1);
            orgRelationship.setAncestors("0");
            orgRelationship.setLevel((long) 1);
            idwOrgRelationshipMapper.updateById(orgRelationship);
            vindicateOrgAncestors(orgRelationship.getRelationshipId(), orgRelationship.getOrgCode(), orgRelationship.getAncestors());
        }*/
        return "操作成功！";
    }


    private static String contents;
    private static long index;

    /**
     * 提取文件内容
     *
     * @return 结果
     */
    @Override
    public String extractFileContent() {
        index = 0;
        contents = "";
        /*File file = new File("F:/HCZY/Excel导入完成备份/其他/有无人和文献数据清单/202104无人系统文献素材");
        extractFileContent(file);*/
        List<OriginalDocument> originalDocumentList = testMapper.selectOriginalDocument();
        originalDocumentList.forEach(d -> {
            index++;
            if (StringUtils.isBlank(contents)) {
                contents = d.getId() + "\t" + d.getContent().trim().replaceAll("[\\t\\n\\r]", "");
            } else {
                contents += "\r\n" + d.getId() + "\t" + d.getContent().trim().replaceAll("[\\t\\n\\r]", "");
            }
            System.out.println(index);
        });
        write2File("F:/HCZY/Excel导入完成备份/其他/有无人和文献数据清单/有无人作战文件文本.txt", contents);
        return index + "个文件提取成功";
    }

    /**
     * 迁移机构关系
     *
     * @return 结果
     */
    @Override
    public String migrationOrgRelationship() {
        List<Long> processedOrgRelationshipId = new ArrayList<>();
        List<IdwOrgRelationship> orgRelationshipList = testMapper.selectAllOrgRelationship();
        for (IdwOrgRelationship orgRelationship : orgRelationshipList) {

        }
        return null;
    }

    /**
     * 资源库生成缩略图
     *
     * @return 结果
     */
    @Override
    public String resourcePdfToImage() {
        List<IdwResource> resourceList = idwResourceMapper.selectIdwResourceList(new IdwResource());
        resourceList.forEach(resource -> {
            if (StringUtils.isBlank(resource.getThumbnail()) && StringUtils.isNotBlank(resource.getStoragePath())) {
                try {
                    //生成缩略图
                    String pdfToImage = pdfToImage(resource.getStoragePath());
                    String imgPath = pdfToImage.replace(WebdpConfig.getProfile(), Constants.RESOURCE_PREFIX);
                    resource.setThumbnail(imgPath);
                    idwResourceMapper.updateIdwResource(resource);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
        return "生成成功！";
    }

    /**
     * 拆分专利与发布作品机构名称
     *
     * @return 结果
     */
    @Override
    public String splitPatentAndAwardOrgName() {
        return splitPatentOrgName();
        //return splitAwardOrgName();
    }

    /**
     * 技术领域表赋值祖籍列表与层级
     *
     * @return 结果
     */
    @Override
    public String assignmentTechnosphere() {
        List<Technosphere> technosphereList = testMapper.selectTechnosphereList();
        for (Technosphere technosphere : technosphereList) {
            if (technosphere.getParentId() == 0) {
                technosphere.setAncestors("0");
                technosphere.setLevel(1);
            } else {
                Technosphere parentTechnosphere = testMapper.selectTechnosphereById(technosphere.getParentId());
                if (StringUtils.isNotBlank(parentTechnosphere.getAncestors())) {
                    technosphere.setAncestors(parentTechnosphere.getAncestors() + "," + parentTechnosphere.getId());
                    technosphere.setLevel(parentTechnosphere.getLevel() + 1);
                }
            }
            testMapper.updateTechnosphere(technosphere);
        }
        if (testMapper.selectTechnosphereList().size() > 0) {
            assignmentTechnosphere();
        }
        return "赋值成功！";
    }

    /**
     * 导入数据
     *
     * @return 结果
     */
    @Override
    public Object importData() {
        //importExcel();
       /*for (int i = 0; i < 190; i++) {
            System.err.println(SnowIdUtils.uniqueLong());
        }*/
        //return importFile();

        /*try {
            return importAircraftAndWarship();
        } catch (Exception e) {
            e.printStackTrace();
        }*/
        //return "";
        return importWeaponry();
    }

    /**
     * 导入武器装备
     *
     * @return
     */
    public String importWeaponry() {
        List<IdwWeaponryBasic> weaponryList = new ArrayList<>();
        try {
            ExcelUtil<IdwWeaponryBasic> util = new ExcelUtil<IdwWeaponryBasic>(IdwWeaponryBasic.class);
            File file = new File("F:\\HCZY\\Excel导入完成备份\\外台军\\2023-11-28 装备更新（燕）\\空军武器装备-wll.xlsx");
            FileInputStream inputStream = new FileInputStream(file);
            weaponryList = util.importExcel("武器装备", inputStream);
            inputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        for (IdwWeaponryBasic idwWeaponryBasic : weaponryList) {
            weaponryBasicMapper.updateIdwWeaponry(idwWeaponryBasic);
        }
        return "";
    }

    /**
     * 导入军机与军舰数据
     *
     * @return 结果
     */
    public String importAircraftAndWarship() throws IOException {
        //军机
        Path aircraftPath = Paths.get("F:/HCZY/Excel导入完成备份/待处理/2023-01-17 军科评估中心军舰军机数据/重点关注目标-飞机.txt");
        String aircraftString = Files.readAllLines(aircraftPath).get(0);
        List<IdwMilitaryAircraft> aircraftList = JSONObject.parseArray(aircraftString, IdwMilitaryAircraft.class);
        for (IdwMilitaryAircraft militaryAircraft : aircraftList) {
            testMapper.insertMilitaryAircraft(militaryAircraft);
        }
        //军舰
        Path warshipPath = Paths.get("F:/HCZY/Excel导入完成备份/待处理/2023-01-17 军科评估中心军舰军机数据/重点关注目标-舰船.txt");
        String warshipString = Files.readAllLines(warshipPath).get(0);
        List<IdwWarship> warshipList = JSONObject.parseArray(warshipString, IdwWarship.class);
        for (IdwWarship warship : warshipList) {
            testMapper.insertWarship(warship);
        }

        return "";
    }

    /**
     * 上传文件
     *
     * @return 结果
     */
    public Object importFile() {
        List<FileVo> fileVoList = testMapper.selectAllAvatarPath();
        List<FileVo> inexistenceFilePathList = new ArrayList<>();
        for (FileVo file : fileVoList) {
            String filePath = file.getPath();
            if (!new File(filePath.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile())).exists()) {
                inexistenceFilePathList.add(file);
            }
        }
        ExcelUtil<FileVo> excelUtil = new ExcelUtil<FileVo>(FileVo.class);
        String fileName = (String) excelUtil.exportExcel(inexistenceFilePathList, "文件路径").get("msg");
        return fileName;
    }

    /**
     * 导入Excel
     *
     * @return 结果
     */
    public String importExcel() {
        try {
            ExcelUtil<CraftCompany> util = new ExcelUtil<CraftCompany>(CraftCompany.class);
            File file = new File("F:\\HCZY\\Excel导入完成备份\\待处理\\2023-02-01 企业最后一批（朱俊伟）\\剩余108企业-导入.xlsx");
            FileInputStream inputStream = new FileInputStream(file);
            List<CraftCompany> craftCompanyList = util.importExcel("基本信息", inputStream);
            inputStream.close();
            List<Long> errCraftCompanyIdList = new ArrayList<>();
            for (CraftCompany craftCompany : craftCompanyList) {
                //格式化经纬度
                String latitude = craftCompany.getLatitude();
                //纬度范围 -90~90
                if (StringUtils.isNotBlank(latitude)) {
                    if (latitude.contains("°")) {
                        latitude = CoordUtils.formatLAL(latitude);
                    }
                    int parseIntLatitude = 0;
                    try {
                        String[] latitudes = latitude.split("\\.");
                        //格式化
                        String latitudeInteger = latitudes[0];//纬度整数
                        String latitudeDecimals = latitudes[1];//纬度小数
                        if (latitudeDecimals.length() > 6) {
                            latitude = latitudeInteger + "." + latitudeDecimals.substring(0, 6);
                        } else if (latitudeDecimals.length() < 6) {
                            latitude = latitudeInteger + "." + (latitudeDecimals + "000000").substring(0, 6);
                        }
                        //校验格式是否规范
                        boolean latitudeIsNumeric = StringUtils.validateNumber(latitude);
                        parseIntLatitude = Integer.parseInt(latitudeInteger);
                        if (!latitudeIsNumeric || !(90 > parseIntLatitude && parseIntLatitude > -90)) {
                            System.err.println();
                        } else {
                            craftCompany.setLatitude(latitude);
                        }
                    } catch (NumberFormatException e) {
                        errCraftCompanyIdList.add(craftCompany.getId());
                    }
                }
                String longitude = craftCompany.getLongitude();
                //经度范围 -180~180
                if (StringUtils.isNotBlank(longitude)) {
                    if (longitude.contains("°")) {
                        longitude = CoordUtils.formatLAL(longitude);
                    }
                    int parseIntLongitude = 0;
                    try {
                        String[] longitudes = longitude.split("\\.");
                        //格式化
                        String longitudeInteger = longitudes[0];//纬度整数
                        String longitudeDecimals = longitudes[1];//纬度小数
                        if (longitudeDecimals.length() > 6) {
                            longitude = longitudeInteger + "." + longitudeDecimals.substring(0, 6);
                        } else if (longitudeDecimals.length() < 6) {
                            longitude = longitudeInteger + "." + (longitudeDecimals + "000000").substring(0, 6);
                        }
                        //校验格式是否规范
                        boolean longitudeIsNumeric = StringUtils.validateNumber(longitude);
                        parseIntLongitude = Integer.parseInt(longitudes[0]);
                        if (!longitudeIsNumeric || !(180 > parseIntLongitude && parseIntLongitude > -180)) {
                            errCraftCompanyIdList.add(craftCompany.getId());
                        } else {
                            craftCompany.setLongitude(longitude);
                        }
                    } catch (NumberFormatException e) {
                        errCraftCompanyIdList.add(craftCompany.getId());
                    }
                }
            }
            if (errCraftCompanyIdList.size() > 0) {
                System.out.println();
            } else {
                for (CraftCompany craftCompany : craftCompanyList) {
                    CraftCompany oldCraftCompany = testMapper.selectCraftCompanyById(craftCompany.getId());
                    if (StringUtils.isNull(oldCraftCompany)) {
                        testMapper.insertCraftCompany(craftCompany);
                    } else {
                        craftCompany.setId(oldCraftCompany.getId());
                        testMapper.updateCraftCompany(craftCompany);
                    }
                }
            }

            ExcelUtil<CraftCompanyPeople> craftCompanyPeopleUtil = new ExcelUtil<CraftCompanyPeople>(CraftCompanyPeople.class);
            FileInputStream fileInputStream = new FileInputStream(file);
            List<CraftCompanyPeople> craftCompanyPeopleList = craftCompanyPeopleUtil.importExcel("企业人员", fileInputStream);
            fileInputStream.close();
            for (CraftCompanyPeople craftCompanyPeople : craftCompanyPeopleList) {
                CraftCompanyPeople oldCraftCompanyPeople = testMapper.selectCraftCompanyPeople(craftCompanyPeople.getCompanyId(), craftCompanyPeople.getName());
                if (StringUtils.isNull(oldCraftCompanyPeople)) {
                    craftCompanyPeople.setId(SnowIdUtils.uniqueLong());
                    testMapper.insertCraftCompanyPeople(craftCompanyPeople);
                } else {
                    craftCompanyPeople.setId(oldCraftCompanyPeople.getId());
                    testMapper.updateCraftCompanyPeople(craftCompanyPeople);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

    /**
     * 统计空值率
     *
     * @param tableName 表名
     * @return 结果
     */
    @Override
    public String statisticsVacancyRate(String tableName) {
        List<String> vacancyRateList = new ArrayList<>();
        List<DBCloumn> dbCloumnList = testMapper.selectColumnNameByTableName("wjmbsj", tableName);
        //目标部队机构
        //String query = "WHERE is_delete = 0 AND org_type_alias = '部队'";
        //目标部队机构人员
        //String query = "WHERE is_delete = 0 AND org_code IN ( SELECT org_code FROM idw_org WHERE is_delete = 0 AND org_type_alias = '部队' )";
        //目标部队体系
        //String query = "WHERE is_delete = 0 AND org_type_alias = '部队体系'";
        //目标部队体系人员
        //String query = "WHERE is_delete = 0 AND org_code IN ( SELECT org_code FROM idw_org WHERE is_delete = 0 AND org_type_alias = '部队体系' )";
        //目标军事科研机构
        //String query = "WHERE is_delete = 0 AND org_type_alias = '军事科研' AND org_code IN ( SELECT org_code FROM idw_org_relationship WHERE is_delete = 0 AND parent_id = - 1 AND org_type = '国防科研' )";
        //标军事科研机构人员/发展历程/产品/新闻/采办项目/文献
        //String query = "WHERE is_delete = 0 AND org_code IN ( SELECT org_code FROM idw_org_relationship WHERE is_delete = 0 AND parent_id = - 1 AND org_type = '国防科研' )";
        //目标军工企业
        //String query = "WHERE is_delete = 0 AND org_type = '国防企业' ";
        //目标军工企业人员
        //String query = "WHERE is_delete = 0 AND org_code IN ( SELECT org_code FROM idw_org WHERE is_delete = 0 AND org_type = '国防企业' )";
        //目标人物
        String query = "WHERE is_delete = 0";
        Integer total = testMapper.selectDataTotalByTableNameAndQuery(tableName, query);
        for (DBCloumn dbCloumn : dbCloumnList) {
            String dataType = dbCloumn.getDataType();
            String columnName = dbCloumn.getColumnName();
            boolean isString = false;
            if ("char".equals(dataType) || "varchar".equals(dataType) || "text".equals(dataType)) {
                isString = true;
            }
            if ("is_delete".equals(columnName) || "create_by".equals(columnName) || "create_time".equals(columnName) || "update_by".equals(columnName) || "update_time".equals(columnName)) {
                continue;
            }
            String vacancyRate = testMapper.selectVacancyRateByTableNameAndColumnName(tableName, columnName, total, isString, query);
            vacancyRateList.add(total + "                             " + dbCloumn.getColumnComment() + "                                            " + vacancyRate);
        }
        for (String vacancyRate : vacancyRateList) {
            System.err.println(vacancyRate);
        }
        return null;
    }

    /**
     * 根据表名与属性名格式化时间
     *
     * @param tableName 表名
     * @param property  属性名
     * @return 结果
     */
    @Override
    public String dateFormat(String tableName, String property) {
        List<String> dateList = testMapper.selectByTableNameAndProperty(tableName, property);
        SimpleDateFormat usSimpleDateFormat = new SimpleDateFormat("MMMM dd, yyyy", Locale.US);
        SimpleDateFormat enSimpleDateFormat = new SimpleDateFormat("EEE MMM dd HH:mm:ss z yyyy", Locale.ENGLISH);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        for (String date : dateList) {
            try {
                String d = dateFormat.format(enSimpleDateFormat.parse(enSimpleDateFormat.parse(usSimpleDateFormat.parse(date).toString()).toString()));
                if (StringUtils.isNotBlank(d)) {
                    testMapper.updateTaableByTableNameAndProperty(tableName, property, d, date);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return null;
    }

    /**
     * 舰船数据迁移到ship表
     *
     * @return 结果
     */
    @Override
    public String shipTransfer() {
        String userName = ShiroUtils.getUserName();
        Date nowDate = DateUtils.getNowDate();
        //List<IdwWeaponryShip> weaponryShipList = testMapper.selectWeaponryShipList();
        //for (IdwWeaponryShip weaponryShip : weaponryShipList) {
        for (String weaponryCode : "435492238986842813,435492238986842814,435492238986842815,435492238986842818,435492238991037115,435492238991037116,435492238991037118,435492238991037120,435492238991037121,435493560473621180,435493560473621182,435496627877188283,435496627877188284,435496627877188285,435496627877188286,435496627877188287,435496627877188288,435496627877188289,435496627877188290,435496627881382587,435496627881382588,435496627881382589,435498717169062589,435498717169062594,435498717378777791,435500877562778301,435500877562778302,435500877562778303,435500877562778304,435500877562778305,435500877562778306,435500877562778307,435500877566972607,435500877566972609,435500877566972610,435500877566972611,435500877571166909,435500877571166911,435747087947141460,435747087955530068,435747088010056019,435747088064581973,435747088077164884,435747088119107925,435747088119107926,435747088123302229,435747088123302230,435747088123302231,435747088123302232,435747088127496531,435747088131690835,435747088131690836,435747088131690837,435747088131690838,435747088140079443,435747088140079444,435747088144273747,435747088223965525,435747088223965528,435747088228159827,435747088228159828,435747088228159829,435747088228159830,435747088228159831,435747088228159832,435747088236548435,435747088236548436,435747088236548437,435747088236548438,435747088236548439,435747088240742739,435747088240742740,435747088240742741,435747088240742742,435747088244937043,435747088244937044,435747088244937045,435747088244937046,435747088244937047,435747088244937048,435747088244937049,435747088244937050,435747088249131347,435747088249131348,435747088249131349,435747088249131350,435747088249131351,435747088249131352,435747088249131353,435747088249131354,435747088249131355,435747088249131356,435747088249131357,435747088253325651,435747088253325652,435747088253325654,435747088253325655,435747088253325656,435747088257519955,435747088257519956,435747088257519957,435747088257519958,435747088257519959,435747088257519961,435747088265908566,435747088265908567,435747088270102867,435747088270102869,435747088270102870,435747088270102871,435747088270102872,435747088274297171,435747088274297172,435747088274297173,435747088274297176,435747088299463003,435747088303657299,435747088303657300,435747088303657301,435747088307851606,435747088312045907,435747088312045908,435747088312045909,435747088312045910,435747088316240211,435747088316240216,435747088328823126,435747088328823128,435747088341406041,435747088345600339,435747088362377556,435747088362377557,435747088362377558,435747088362377559,435747088362377560,435747088362377562,435747088362377563,435747088366571861,435747088366571862,435747088366571863,435747088366571864,435747088366571865,435747088366571866,435747088366571867,435747088366571868,435747088370766163,435747088370766165,435747088370766166,435747088370766167,435747088370766168,435747088370766169,435747088370766171,435747088370766172,435747088374960467,435747088374960468,435747088374960471,435747088374960472,435747088374960473,435747088374960474,435747088374960476,435747088379154771,435747088379154772,435747088379154773,435747088379154774,435747088383349076,435747088383349078,435747088383349079,435747088383349080,435747088383349081,435747088387543379,435747088387543380,435747088387543383,435747088387543386,435747088391737683,435747088391737684,435747088391737685,435747088391737686,435747088391737687,435747088391737688,435747088395931987,435747088395931992,435747088395931993,435747088395931994,435747088395931995,435747088400126292,435747088400126293,435747088400126294,435747088400126297,435747088404320595,435747088404320596,435747088404320597,435747088404320598,435747088404320599,435747088404320600,435747088404320601,435747088408514899,435747088408514900,435747088408514901,435747088408514902,435747088408514905,435747088408514906,435747088408514907,435747088412709205,435747088412709206,435747088412709207,435747088412709209,435747088412709210,435747088412709211,435747088412709212,435747088412709214,435747088416903507,435747088416903508,435747088416903509,435747088425292116,435747088425292117,435747088425292118,435747088425292119,435747088429486420,435747088433680724,435747088433680725,435747088433680726,435747088433680727,435747088433680728,435747088433680729,435747088433680732,435747088442069331,435747088446263637,435747088446263638,435747088446263639,435747088446263640,435747088446263641,435747088446263642,435747088446263644,435747088446263645,435747088450457939,435747088450457940,435747088450457941,435747088450457942,435747088450457943,435747088450457944,435747088450457945,435747088450457946,435747088454652244,435747088454652245,435747088454652246,435747088454652247,435747088454652249,435747088463040851,435747088467235155,435747088467235156,435747088467235157,435747088467235158,435747088467235160,435747088467235161,435747088471429459,435747088471429460,435747088471429461,435747088471429462,435747088471429463,435747088475623764,435747088475623765,435747088475623766,435747088475623767,435747088475623768,435747088475623769,435747088475623771,435747088475623772,435747088475623773,435747088475623774,435747088475623775,435747088479818067,435747088479818070,435747088479818071,435747088479818072,435747088479818074,435747088479818075,435747088479818076,435747088484012371,435747088484012372,435747088484012376,435747088484012377,435747088488206675,435747088496595283,435747088496595284,435747088500789587,435747088500789588,435747088500789589,435747088500789590,435747088500789591,435747088500789592,435747088504983891,435747088504983892,435747088504983893,435747088504983894,435747088509178195,435747088509178197,435747088509178199,435747088509178200,435747088509178201,435747088509178202,435747088509178203,435747088509178204,435747088509178205,435747088513372499,435747088513372500,435747088513372502,435747088513372503,435747088513372504,435747088513372505,435747088517566803,435747088517566804,435747088517566807,435747088517566808,435747088517566809,435747088517566810,435747088517566811,435747088517566812,435747088517566813,435747088517566814,435747088517566815,435747088517566816,435747088521761107,435747088521761108,435747088521761111,435747088521761112,435747088525955411,435747088525955412,435747088525955413,435747088530149715,435747088530149716,435747088530149717,435747088530149718,435747088530149719,435747088534344020,435940909528389081,W000116,W001836,W001843,W001845,W001844,W001957,W0030175,W005003,W005004,W005005,W005014,W005015,W005016,W005017,W005018,W005020".split(",")) {
            IdwWeaponryBasic weaponry = weaponryBasicMapper.selectByWeaponryCode(weaponryCode);
            //不存在舰船 所有数据迁入ship表
            long id = SnowIdUtils.uniqueLong();// 获取雪花id
            Ship ship = new Ship();
            ship.setId(id);
            ship.setWeaponryCode("ship_" + id);
            ship.setNationalOrigin(weaponry.getCountry());
            ship.setClassificationCode(weaponry.getClassificationCode());
            ship.setShipTranslateName(weaponry.getNameCn());
            ship.setShipName(weaponry.getNameEn());
            ship.setPhoto(weaponry.getAvatar());
            ship.setHullNo(weaponry.getWeaponryCode());
            ship.setAwarded(weaponry.getFirstDeliveryTime());
            ship.setLaunched(weaponry.getFirstTime());
            ship.setCommissioned(weaponry.getSeviceTime());
            ship.setServiceStatus(weaponry.getServiceStatus());
            ship.setHomeport(weaponry.getHomeport());
            ship.setBuilder(weaponry.getContractor());
            ship.setSource(weaponry.getSource());
            ship.setHistory(weaponry.getServiceHistory());
            ship.setCreateBy(userName);
            ship.setCreateTime(nowDate);
            shipMapper.insertShip(ship);
            //将大文本赋值到舰船简介中
            String introductionCn = weaponry.getIntroductionCn();
            String introductionEn = weaponry.getIntroductionEn();
            if (StringUtils.isNotBlank(weaponry.getDevelopmentBackground())) {
                boolean isContainChinese = isContainChinese(weaponry.getDevelopmentBackground());
                if (isContainChinese) {
                    introductionCn += "<br><br><b>研制背景</b></b><br><br>" + weaponry.getDevelopmentBackground();
                } else {
                    introductionEn += "<br><br><b>development background</b><br><br>" + weaponry.getDevelopmentBackground();
                }
            }
            if (StringUtils.isNotBlank(weaponry.getConstructionHistory())) {
                boolean isContainChinese = isContainChinese(weaponry.getConstructionHistory());
                if (isContainChinese) {
                    introductionCn += "<br><br><b>建造沿革</b><br><br>" + weaponry.getConstructionHistory();
                } else {
                    introductionEn += "<br><br><b>construction history</b><br><br>" + weaponry.getConstructionHistory();
                }
            }
            if (StringUtils.isNotBlank(weaponry.getServiceHistory())) {
                boolean isContainChinese = isContainChinese(weaponry.getServiceHistory());
                if (isContainChinese) {
                    introductionCn += "<br><br><b>服役历程</b><br><br>" + weaponry.getServiceHistory();
                } else {
                    introductionEn += "<br><br><b>service history</b><br><br>" + weaponry.getServiceHistory();
                }
            }
            if (StringUtils.isNotBlank(weaponry.getStructure())) {
                boolean isContainChinese = isContainChinese(weaponry.getStructure());
                if (isContainChinese) {
                    introductionCn += "<br><br><b>结构特征</b><br><br>" + weaponry.getStructure();
                } else {
                    introductionEn += "<br><br><b>structure</b><br><br>" + weaponry.getStructure();
                }
            }
            if (StringUtils.isNotBlank(weaponry.getArmament())) {
                boolean isContainChinese = isContainChinese(weaponry.getArmament());
                if (isContainChinese) {
                    introductionCn += "<br><br><b>武器装备</b><br><br>" + weaponry.getArmament();
                } else {
                    introductionEn += "<br><br><b>armament</b><br><br>" + weaponry.getArmament();
                }
            }
            if (StringUtils.isNotBlank(weaponry.getPowerSystem())) {
                boolean isContainChinese = isContainChinese(weaponry.getPowerSystem());
                if (isContainChinese) {
                    introductionCn += "<br><br><b>动力系统</b><br><br>" + weaponry.getPowerSystem();
                } else {
                    introductionEn += "<br><br><b>power system</b><br><br>" + weaponry.getPowerSystem();
                }
            }
            if (StringUtils.isNotBlank(weaponry.getAvionicsSystem())) {
                boolean isContainChinese = isContainChinese(weaponry.getAvionicsSystem());
                if (isContainChinese) {
                    introductionCn += "<br><br><b>航电系统</b><br><br>" + weaponry.getAvionicsSystem();
                } else {
                    introductionEn += "<br><br><b>avionics system</b><br><br>" + weaponry.getAvionicsSystem();
                }
            }
            //String newWeaponryCode = "ship_" + weaponryShip.getShipId();
            testMapper.updateShipIntroductionByWeaponryCode(weaponryCode, introductionCn, introductionEn);
            //testMapper.updateWeaponrySpecificationsWeaponryCode(weaponryCode, newWeaponryCode);
            //testMapper.updateWeaponryFleetlistWeaponryCode(weaponryCode, newWeaponryCode);
            //testMapper.updateWeaponryFleetlistRelatedWeaponryCode(weaponryCode, newWeaponryCode);
            //testMapper.updateWeaponryOperatorWeaponryCode(weaponryCode, newWeaponryCode);
            //testMapper.updateWeaponryStructureWeaponryCode(weaponryCode, newWeaponryCode);
            //testMapper.updateWeaponryAffiliationThemeWeaponryCode(weaponryCode, newWeaponryCode);
            //testMapper.updateWeaponryContractorWeaponryCode(weaponryCode, newWeaponryCode);
        }
        return "迁移完成！";
    }

    /**
     * 校验文件是否存在
     *
     * @return 结果
     */
    @Override
    public String fileExist() {
        //return shipFileExist();
        //return resourceFileExost();
        return documentFileExost();
    }

    /**
     * 校验资源库文件
     *
     * @return 结果
     */
    public String documentFileExost() {
        List<IdwOrgDocument> documentList = idwOrgDocumentMapper.selectIdwOrgDocumentList(new IdwOrgDocument());
        String ids = "";
        List<IdwOrgDocument> list = documentList.stream().filter(document -> StringUtils.isNotBlank(document.getThumbnail()) || StringUtils.isNotBlank(document.getStoragePath())).collect(Collectors.toList());
        for (IdwOrgDocument document : list) {
            if (StringUtils.isNotBlank(document.getStoragePath())) {
                String storagePath = document.getStoragePath().replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile());
                if (!new File(storagePath).exists()) {
                    ids += StringUtils.isBlank(ids) ? document.getDocumentId() : "," + document.getDocumentId();
                }
            }
            if (StringUtils.isNotBlank(document.getThumbnail())) {
                String thumbnail = document.getThumbnail().replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile());
                if (!new File(thumbnail).exists()) {
                    ids += StringUtils.isBlank(ids) ? document.getDocumentId() : "," + document.getDocumentId();
                }
            }
        }
        return ids;
    }

    /**
     * 校验资源库文件
     *
     * @return 结果
     */
    public String resourceFileExost() {
        List<IdwResource> resourceList = idwResourceMapper.selectIdwResourceList(new IdwResource());
        String ids = "";
        for (IdwResource resource : resourceList.stream().filter(resource -> StringUtils.isNotBlank(resource.getStoragePath()) || StringUtils.isNotBlank(resource.getThumbnail())).collect(Collectors.toList())) {
            if (StringUtils.isNotBlank(resource.getStoragePath())) {
                String storagePath = resource.getStoragePath().replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile());
                if (!new File(storagePath).exists()) {
                    ids += StringUtils.isBlank(ids) ? resource.getResourceId() : "," + resource.getResourceId();
                }
            }
            if (StringUtils.isNotBlank(resource.getThumbnail())) {
                String thumbnail = resource.getThumbnail().replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile());
                if (!new File(thumbnail).exists()) {
                    ids += StringUtils.isBlank(ids) ? resource.getResourceId() : "," + resource.getResourceId();
                }
            }
        }
        return ids;
    }

    /**
     * 校验舰船文件
     *
     * @return 结果
     */
    public String shipFileExist() {
         /*String path = "";
        List<String> filePathList = testMapper.selectFilePathList("ship", "photo");
        for (String filePath : filePathList) {
            String fileAbsolutePath = filePath.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile());
            if (!filePath.contains(Constants.RESOURCE_PREFIX) || !new File(fileAbsolutePath).exists()) {
                path += StringUtils.isBlank(path) ? filePath : "," + filePath;
            }
        }
        return path;*/
        String ids = "";
        List<Ship> shipList = testMapper.selectShipList();
        String weaponryPath = WebdpConfig.getWeaponryPath() + "/" + DateUtils.datePath();
        for (Ship ship : shipList) {
            Long id = ship.getId();
            String badge = ship.getBadge();
            if (StringUtils.isNotBlank(badge)) {
                if (badge.contains(Constants.RESOURCE_PREFIX) && new File(badge.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile())).exists()) {
                    //文件md5 作为文件新的名称
                    String md5 = FileUtils.getMd5(badge);
                    //重新生成文件路径并设置小写
                    String newFilePath = weaponryPath + "/" + md5 + badge.substring(badge.lastIndexOf(".")).toLowerCase();
                    if (!new File(newFilePath).exists()) {
                        //拷贝文件并将文件名称修改为文件md5
                        FileUploadUtils.copyFile(badge.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile()), newFilePath);
                    }
                    //更新数据库
                    testMapper.updateShipBadgeById(id, newFilePath.replace(WebdpConfig.getProfile(), Constants.RESOURCE_PREFIX));
                } else {
                    ids += StringUtils.isBlank(ids) ? id : "," + id;
                }
            }
            String photo = ship.getPhoto();
            if (StringUtils.isNotBlank(photo)) {
                if (photo.contains(Constants.RESOURCE_PREFIX) && new File(photo.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile())).exists()) {
                    //文件md5 作为文件新的名称
                    String md5 = FileUtils.getMd5(photo);
                    //重新生成文件路径并设置小写
                    String newFilePath = weaponryPath + "/" + md5 + photo.substring(photo.lastIndexOf(".")).toLowerCase();
                    if (!new File(newFilePath).exists()) {
                        //拷贝文件并将文件名称修改为文件md5
                        FileUploadUtils.copyFile(photo.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile()), newFilePath);
                    }
                    //更新数据库
                    testMapper.updateShipPhotoById(id, newFilePath.replace(WebdpConfig.getProfile(), Constants.RESOURCE_PREFIX));
                } else {
                    ids += StringUtils.isBlank(ids) ? id : "," + id;
                }
            }
        }
        return ids;
    }

    /**
     * 排序
     *
     * @return 结果
     */
    @Override
    public String sort() {
        //装备
        List<IdwWeaponryBasic> weaponryList = testMapper.selectAllWeaponry();
        Integer weaponryMaxOrderNum = weaponryBasicMapper.selectWeaponryMaxOrderNum();
        for (IdwWeaponryBasic weaponry : weaponryList) {
            weaponryMaxOrderNum++;
            testMapper.updateWeaponryOrderNumByWeaponryCode(weaponry.getWeaponryCode(), weaponryMaxOrderNum);
        }
        //舰船
        List<Ship> shipList = testMapper.selectAllShip();
        Integer shipMaxOrderNum = shipMapper.selectShipMaxOrderNum();
        for (Ship ship : shipList) {
            shipMaxOrderNum++;
            testMapper.updateShipOrderNumById(ship.getId(), shipMaxOrderNum);
        }
        return "排序完成！";
    }

    /**
     * 数据迁移
     *
     * @return 结果
     */
    @Override
    public String dataMigration() {
        //人物画像、外台军、装备谱系、科研机构知识库、军科评估中心
        //新建数据库->军科评估中心-人物画像->科研机构知识库
        /*String targetDatabase = "";//目标数据库-当前数据库
        String migrationDatabase = "`rwhx`.";//需要迁移的数据库
        //机构
        //人员
        peopleMigration(migrationDatabase);
        //装备
        //合同
        //资源库
        //菜单
        return null;*/
        //采集两千装备战技指标迁移
        return weaponrySpecificationsMigration();
    }

    /**
     * 采集两千装备战技指标迁移
     *
     * @return 结果
     */
    public String weaponrySpecificationsMigration() {
        String userName = ShiroUtils.getUserName();
        Date nowDate = DateUtils.getNowDate();
        List<EquipmentCharacteristic> characteristicList = testMapper.selectCollectWeaponrySpecifications();
        for (EquipmentCharacteristic characteristic : characteristicList) {
            String weaponryCode = characteristic.getEquipmentId().toString();
            String type = StringUtils.isNull(characteristic.getType()) ? "" : characteristic.getType().trim();
            String indexName = StringUtils.isNull(characteristic.getIndexName()) ? "" : characteristic.getIndexName().trim();
            String indexValue = StringUtils.isNull(characteristic.getIndexValue()) ? "" : characteristic.getIndexValue().trim();
            if (StringUtils.isNotBlank(type) && !type.equals(indexName)) {
                //分类
                IdwWeaponrySpecifications specifications = testMapper.selectWeaponrySpecificationsByWeaponryCodeAndNameEnAndParentId(weaponryCode, type, (long) 0);
                if (StringUtils.isNull(specifications)) {//去重
                    int maxOrderNum = testMapper.selectWeaponrySpecificationsMaxOrderNumByWeaponryCodeAndParentId(weaponryCode, (long) 0);
                    IdwWeaponrySpecifications weaponrySpecifications = new IdwWeaponrySpecifications();
                    weaponrySpecifications.setWeaponryCode(weaponryCode);
                    weaponrySpecifications.setParentId((long) 0);
                    weaponrySpecifications.setNameCn(type);
                    weaponrySpecifications.setNameEn(type);
                    weaponrySpecifications.setType("分类");
                    weaponrySpecifications.setLevel(1);
                    weaponrySpecifications.setOrderNum(maxOrderNum + 1);
                    weaponrySpecifications.setSource("采集装备");
                    weaponrySpecifications.setCreateBy(userName);
                    weaponrySpecifications.setCreateTime(nowDate);
                    idwWeaponrySpecificationsMapper.insertIdwWeaponrySpecifications(weaponrySpecifications);
                }
            }
            //指标
            IdwWeaponrySpecifications cspecifications = new IdwWeaponrySpecifications();
            cspecifications.setWeaponryCode(weaponryCode);
            //根据机构编码与上级名称（type作为分类的名称）查询装备战技指标ID
            long parentId = 0;
            if (StringUtils.isNotBlank(type) && !type.equals(indexName)) {
                IdwWeaponrySpecifications parent = testMapper.selectWeaponrySpecificationsByWeaponryCodeAndNameEnAndParentId(weaponryCode, type, (long) 0);
                parentId = parent.getSpecificationsId();
                cspecifications.setLevel(2);
            } else {
                cspecifications.setLevel(1);
            }
            cspecifications.setParentId(parentId);
            cspecifications.setNameCn(indexName);
            cspecifications.setNameEn(indexName);
            cspecifications.setValue(indexValue);
            cspecifications.setType("指标");
            int cmaxOrderNum = testMapper.selectWeaponrySpecificationsMaxOrderNumByWeaponryCodeAndParentId(weaponryCode, parentId);
            cspecifications.setOrderNum(cmaxOrderNum + 1);
            cspecifications.setSource("采集两千装备");
            cspecifications.setCreateBy(userName);
            cspecifications.setCreateTime(nowDate);
            //去重
            IdwWeaponrySpecifications specifications = testMapper.selectWeaponrySpecificationsByWeaponryCodeAndNameEnAndParentId(cspecifications.getWeaponryCode(), cspecifications.getNameEn(), cspecifications.getParentId());
            if (StringUtils.isNull(specifications)) {
                idwWeaponrySpecificationsMapper.insertIdwWeaponrySpecifications(cspecifications);
            }
        }
        return "采集两千装备战技指标迁移完成";
    }

    /**
     * 获取文件页码
     *
     * @return 结果
     */
    @Override
    public String getFilePage() {
        String path = "";
        List<DocDocument> documentList = testMapper.selectDocDocument();
        for (DocDocument document : documentList) {
            String filePath = document.getFilePath().replace("D:", "E:");
            String fileSuffix = filePath.substring(filePath.lastIndexOf(".") + 1, filePath.length());
            int pages = 0;
            long length = 0;
            if (fileSuffix.equalsIgnoreCase("pdf")) {
                File file = new File(filePath);
                if (file.exists()) {
                    length = file.length();
                    try {
                        PDDocument pdfReader = PDDocument.load(file);
                        pages = pdfReader.getNumberOfPages();
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else {
                    path = StringUtils.isBlank(path) ? filePath : "," + filePath;
                }
            } else {
                path = StringUtils.isBlank(path) ? filePath : "," + filePath;
            }
            if (pages != 0) {
                //System.err.println(pages);
                testMapper.updateDocDocumentPageById(document.getId(), pages, length);
            }
        }
        return path;
    }

    /**
     * 维护机构祖籍列表
     *
     * @return 结果
     */
    @Override
    public String vindicateOrgAncestors() {
        /*List<IdwOrgRelationship> relationshipList = testMapper.selectOrgRelationshipOrderByLevel();
        for (IdwOrgRelationship relationship : relationshipList) {
            if ("-1".equals(relationship.getParentId().toString())) {
                testMapper.updateOrgRelationshipAncestorsByRelationshipId(relationship.getRelationshipId(), "0");
            } else {
                IdwOrgRelationship parentRelationship = idwOrgRelationshipMapper.selectByRelationshipId(relationship.getParentId(), false);
                testMapper.updateOrgRelationshipAncestorsByRelationshipId(relationship.getRelationshipId(), parentRelationship.getAncestors() + "," + parentRelationship.getOrgCode());
            }
        }*/
        return null;
    }

    /**
     * 导入采集数据
     *
     * @return 结果
     */
    @Override
    public String importCollection() {
        String userName = ShiroUtils.getUserName();
        Date nowDate = DateUtils.getNowDate();
        String equipmentIds = "435500877566972611, 434753603362623807, 444494662871290208, 444494662779015520, 443020883833917516, 443020902813143116, 443020883938775116, 435940920467133908, 435940920462939618, 434753602523763007, 443020832675991628, 444494692248195424";
        List<EquipmentCharacteristic> equipmentCharacteristicList = testMapper.selectEquipmentCharacteristicByEquipmentIds(equipmentIds.split(","));
        for (EquipmentCharacteristic equipmentCharacteristic : equipmentCharacteristicList) {
            IdwWeaponrySpecifications specifications = idwWeaponrySpecificationsMapper.selectByWeaponryCodeAndName(equipmentCharacteristic.getEquipmentId().toString(), null, equipmentCharacteristic.getIndexName(), null);
            if (StringUtils.isNull(specifications)) {
                IdwWeaponrySpecifications weaponrySpecifications = new IdwWeaponrySpecifications();
                weaponrySpecifications.setWeaponryCode(equipmentCharacteristic.getEquipmentId().toString());
                if (StringUtils.isNotBlank(equipmentCharacteristic.getType())) {
                    IdwWeaponrySpecifications parentWeaponrySpecifications = idwWeaponrySpecificationsMapper.selectByWeaponryCodeAndName(equipmentCharacteristic.getEquipmentId().toString(), null, equipmentCharacteristic.getType(), null);
                    if (StringUtils.isNull(parentWeaponrySpecifications)) {
                        IdwWeaponrySpecifications s = new IdwWeaponrySpecifications();
                        s.setWeaponryCode(equipmentCharacteristic.getEquipmentId().toString());
                        s.setParentId((long) 0);
                        s.setNameCn(equipmentCharacteristic.getType());
                        s.setNameEn(equipmentCharacteristic.getType());
                        s.setType("分类");
                        s.setLevel(1);
                        s.setSource("采集");
                        s.setCreateBy(userName);
                        s.setCreateTime(nowDate);
                        idwWeaponrySpecificationsMapper.insertIdwWeaponrySpecifications(s);
                        parentWeaponrySpecifications = idwWeaponrySpecificationsMapper.selectByWeaponryCodeAndName(equipmentCharacteristic.getEquipmentId().toString(), null, equipmentCharacteristic.getType(), null);
                    }
                    weaponrySpecifications.setParentId(parentWeaponrySpecifications.getSpecificationsId());
                    weaponrySpecifications.setLevel(parentWeaponrySpecifications.getLevel() + 1);
                } else {
                    weaponrySpecifications.setParentId((long) -1);
                    weaponrySpecifications.setLevel(1);
                }
                weaponrySpecifications.setType("指标");
                weaponrySpecifications.setNameCn(equipmentCharacteristic.getIndexName());
                weaponrySpecifications.setNameEn(equipmentCharacteristic.getIndexName());
                weaponrySpecifications.setValue(equipmentCharacteristic.getIndexValue());
                weaponrySpecifications.setSource("采集");
                weaponrySpecifications.setCreateBy(userName);
                weaponrySpecifications.setCreateTime(nowDate);
                idwWeaponrySpecificationsMapper.insertIdwWeaponrySpecifications(weaponrySpecifications);
            }
        }

        return null;
    }

    /**
     * 导出文件
     *
     * @return 结果
     */
    @Override
    public String exportFile() {
        /*List<IdwOrgDocument> documentList = testMapper.selectOrgDocumentByCollectTime("2022-05-13 00:00:00");
        String notExistFiles = "";
        List<String> inexistenceFilePath = new ArrayList<>();
        for (IdwOrgDocument document : documentList) {
            String thumbnail = document.getThumbnail();
            if (StringUtils.isNotBlank(thumbnail)) {
                inexistenceFilePath.add(thumbnail);
                if (thumbnail.contains(Constants.THUMBNAIL_PREFIX)) {
                    inexistenceFilePath.add(thumbnail.replace(Constants.THUMBNAIL_PREFIX, ""));
                }
                if (!new File(thumbnail.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile())).exists()) {
                    notExistFiles = StringUtils.isNotBlank(notExistFiles) ? notExistFiles + "," + thumbnail : thumbnail;
                }
            }
            String storagePath = document.getStoragePath();
            if (StringUtils.isNotBlank(storagePath)) {
                inexistenceFilePath.add(storagePath);
                if (!new File(storagePath.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile())).exists()) {
                    notExistFiles = StringUtils.isNotBlank(notExistFiles) ? notExistFiles + "," + storagePath : storagePath;
                }
            }
        }
        if (StringUtils.isNotBlank(notExistFiles)) {
            return notExistFiles;
        }
        for (String filePath : inexistenceFilePath) {
            copyFile(filePath.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile()), filePath.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getDownloadPath()));
        }*/
        /*List<String> type = new ArrayList<>();
        type.add("checkbox");
        type.add("resource");
        type.add("project");
        type.add("personnel");
        type.add("organization");
        type.add("weaponry");
        String date = "1999-01-01";
        List<String> inexistenceFilePath = new ArrayList<>();
        for (FileDownloadService fileDownloadService : fileDownloadServiceList) {
            List<String> path = fileDownloadService.getFilePath(type);
            if (StringUtils.isNotNull(path) && path.size() > 0) {
                for (String filePath : path) {
                    if (!filePath.contains(Constants.RESOURCE_PREFIX)) {
                        continue;
                    }
                    String fileAbsolutePath = filePath.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile());
                    File file = new File(fileAbsolutePath);
                    if (!file.exists() && !inexistenceFilePath.contains(filePath)) {
                        inexistenceFilePath.add(filePath);
                        System.err.println("文件：【" + filePath + "】不存在");
                    }
                    if (fileAbsolutePath.contains(Constants.THUMBNAIL_PREFIX)) {
                        String replace = fileAbsolutePath.replace(Constants.THUMBNAIL_PREFIX, "");
                        file = new File(replace);
                        if (!file.exists() && !inexistenceFilePath.contains(replace)) {
                            inexistenceFilePath.add(filePath);
                            System.err.println("原文件：【" + filePath + "】不存在");
                        }
                    }
                }
            }
        }
        if (type.contains("firepower")) {
            File flagsDir = new File(WebdpConfig.getProfile() + "/img/flags");
            if (!flagsDir.exists()) {
                AjaxResult.error("军力指数国旗不存在");
            }
            File mapsDir = new File(WebdpConfig.getProfile() + "/img/maps");
            if (!mapsDir.exists()) {
                AjaxResult.error("军力指数地图不存在");
            }
        }
        return "不存在：" + JSONObject.toJSONString(inexistenceFilePath);*/
        //return technosphere();
        String[] peopleIds = "2032,2033,2034,2055,2056,2057,2069,2084,2085,2086,2088,2089,2090,2097,2112,2113,2114,2160,2161,2162,2172,2175,2176,2177,2187,2207,2213,2214,2215,2216,2217,2219,2220,2221,2222,2223,2224,2225,2226,2227,2228,2229,2230,2233,2238,2255,2263,2330,2331,2332,2348,2357,2389,2390,2391,2392,2418,2437,2438,2439,2446,2497,2538,2548,2549,2550,2582,2591,2619,2669,2670,2671,2672,2673,2682,2683,2685,2686,2687,2691,2692,2693,2730,2732,2734,2743,2766,3554,3555,3562,3610,3611,3612,3649,3737,3738,3739,3740,3756,3757,3758,3759,3760,3761,3762,3763,3764,3765,3773,3774,3775,3776,3784,3785,3786,3799,3805,3807,3808,3809,3820,3865,3866,3868,3869,3870,4326,4327,4328,4342,4343,4344,4345,4346,4361,4362,4363,4364,4394,4426,4436,4437,4438,4439,4440,4453,4475,4476,4477,4478,4479,4540,4546,4552,4599,4600,4601,4602,4603,4604,4605,4606,4607,4608,4609,4610,4611,4612,4639,4640,4641,4660,4713,4714,4715,4730,4731,4732,4733,4738,5803,5804,5805,5806,5807,5808,5812,5813,5818,5819,5820,5825,5830,5831,5832,5843,5844,5845,5846,5847,5848,5849,5861,5864,5870,5871,5875,5877,5878,5879,5880,5919,5920,5921,5922,5924,5925,5926,5930,5931,5932,5933,5934,5935,5937,5938,5939,5940,5941,5942,5943,5944,5945,5951,5952,5953,5954,5955,5956,5957,5958,5959,5960,6017,6018,6019,6020,6021,6022,6092,6093,6094,6102,6103,6104,6105,6106,6107,6108,6109,6110,6111,6112,6113,6114,6127,6128,6129,6130,6134,6135,6136,6143,6146,6147,6148,6149,6156,6157,6158,6159,6160,6161,6339,6343,6346,6363,6375,6464,6500,6503,6515,6711,14998,15115,15205,15255,15580,15627,16001,16352,16474,16565,16781,16943,16947,16983,17380,17474,17537,17538,18101,18307,18485,18682,18685,18751,19062,19182,19187,19255,19641,19667,19717,20002,20003,20004,20005,20006,20007,20008,20009,20010,20011,20013,20014,20015,20016,20017,20018,20019,20020,20021,20022,20023,20025,20026,20027,20028,20029,20031,20032,20033,20034,20035,20036,20037,20038,20040,20041,20042,20043,20044,20045,20046,20047,20048,20049,20050,20051,20052,20055,20056,20057,20058,20062,20064,20067,20069,20070,20074,20076,20079,20080,20083,20084,20085,20086,20087,20088,20089,20090,20091,20092,20093,20094,20095,20096,20097,20098,20099,20100,20101,20102,20103,20104,20105,20107,20108,20109,20110,20111,20112,20113,20114,20115,20116,20117,20118,20119,20120,20121,20122,20123,20124,20125,20126,20127,20128,20129,20130,20131,20132,20133,20134,20136,20137,20138,20139,20140,20144,20145,20147,20149,20152,20154,20155,20158,20159,20161,20163,20165,20167,20169,20171,20173,20175,20177,20179,20185,20187,20188,20190,20192,20193,20196,20197,20200,20202,20204,20205,20207,20210,20212,20215,20217,20221,20222,20224,20226,20228,20229,20233,20234,20236,20238,20241,20243,20245,20246,20249,20251,20252,20254,20256,20258,20260,20261,20262,20263,20266,20268,20270,20272,20274,20275,20305,20306,20307,20308,20309,20310,20311,20312,20313,20314,20315,20316,20317,20318,20319,20320,20321,20322,20323,20324,20325,20326,20327,20328,20329,20331,20332,20333,20334,20335,20336,20337,20338,20339,20340,20341,20343,20344,20345,20346,20347,20348,20349,20350,20351,20354,20356,20359,20360,20361,20362,20363,20364,20365,20366,20367,20368,20369,20370,20372,20373,20374,20375,20376,20377,20378,20379,20380,20381,20382,20383,20384,20385,20386,20387,20388,20389,20390,20391,20392,20448,20508,20529,20535,20548,20558,20562,20594,20611,20616,20627,20634,20638,20643,20659,20685,20686,20695,20716,20726,20737,20739,20781,20782,20787,20797,20801,20814,20829,20853,20875,20898,20916,20930,21010,21049,21121,21122,21171,21179,21237,21240,21243,21318,21482,21572,21589,21743,21744,21745,21754,21759,21760,21761,24224,24225,24241,24242,24243,24611,24934,25038,25054,25057,25059,25080,25106,25110,25199,25203,25235,25318,25388,25406,25453,25454,25455,25456,25457,25492,25493,25494,25495,25496,25497,25500,25590,25650,25681,26100,26101,26102,26109,26114,26115,26116,26125,26137,26303,26304,26330,26331,26332,26345,26346,26349,26350,26351,26352,26353,26354,26357,26361,26362,26363,26364,26365,26366,26444,26445,26448,26449,26450,26451,26452,26456,26457,26458,26459,26460,26461,26463,26464,26484,26485,26486,26487,26488,26489,26490,26491,26492,26493,26494,26495,26496,26497,26498,26510,26511,26512,26513,26514,26515,26516,26525,26526,26527,26539,26540,26541,26542,26543,26547,26548,26554,26555,26556,26557,26558,26559,26560,26561,26562,26563,26564,26565,26571,26572,26573,26578,26579,26587,26588,26589,26594,26595,26596,26597,26598,26599,29367,29374,29375,29376,29377,29378,29379,29380,29381,29382,29383,29441,29445,29449,29450,29452,29453,29617,29621,29633,29634,29635,29637,29638,29639,29640,29641,29642,29742,31214,31242,31246,31259,31290,31299,31326,31339,31347,31356,31373,31381,31421,31432,31477,31482,31486,31501,31521,31523,31549,31551,31557,31581,31590,31604,31606,31612,31637,31642,31651,31659,31661,31663,31701,31723,31733,31734,31745,31747,31748,31765,31771,31775,31777,31778,31779,31781,31785,31788,31793,31795,31822,31824,31847,31848,31863,31865,31874,31894,31900,31912,31913,31914,31921,31936,31938,31953,31956,31960,31961,31968,31969,31971,31972,31982,31996,32003,32010,32019,32020,32025,32029,32030,32036,32053,32080,32085,32091,32094,32096,32097,32105,32109,32116,32123,32133,32140,32145,32147,32153,32157,32160,32167,32170,32172,32183,32190,32191,32208,32211,32222,32228,32229,32230,32231,32232,32233,32234,32235,32236,32249,32263,32269,32270,32275,32299,32301,32307,32313,32334,32340,32366,32371,32388,32401,32417,32425,32430,32437,32463,32469,32475,32478,32491,32500,32501,32510,32534,32544,32549,32565,32572,32576,32577,32579,32612,32613,32630,32641,32652,32671,32679,32699,32702,32710,32711,32713,32724,32747,32762,32767,32773,32779,32800,32827,32847,32865,32866,32867,32873,32874,32875,32879,32880,32883,32915,32942,32947,32948,32964,32968,32975,32992,33006,33011,33014,33030,33032,33041,33058,33063,33078,33081,33083,33088,33094,33097,33099,33102,33109,33114,33146,33157,33158,33170,33173,33176,33204,33210,33227,33228,33245,33263,33301,33313,33314,33323,33329,33335,33339,33344,33362,33369,33385,33398,33413,33417,33420,33421,33443,33444,33447,33453,33456,33457,33458,33467,33478,33495,33499,33517,33518,33519,33530,33533,33539,33553,33561,33562,33563,33564,33570,33601,33603,33676,33685,33721,33726,33728,33736,33749,33753,33758,33763,33765,33771,33796,33807,33816,33834,33881,33882,33915,33917,33927,33945,33950,33956,33962,33969,33974,34029,34030,34031,34035,34048,34055,34059,34088,34099,34122,34124,34130,34131,34166,34172,34176,34180,34181,34193,34209,34227,34248,34259,34266,34271,34274,34287,34297,34313,34328,34334,34338,34362,34363,34367,34379,34380,34391,34396,34397,34399,34403,34404,34406,34413,34414,34415,34417,34424,34430,34435,34455,34462,34474,34481,34483,34485,34499,34512,34524,34535,34542,34550,34554,34555,34564,34567,34576,34583,34606,34608,34621,34625,34627,34638,34648,34664,34669,34685,34687,34693,34696,34706,34719,34725,34732,34733,34775,34792,34812,34815,34816,34823,34826,34829,34831,34852,34859,34864,34873,34881,34886,34893,34896,34900,34917,34918,34922,34930,34946,34967,34974,34981,35022,35029,35045,35049,35051,35061,35062,35098,35132,35141,35142,35147,35177,35196,35202,35203,35219,35232,35246,35262,35283,35284,35293,35321,35329,35351,35364,35366,35368,35370,35374,35381,35403,35405,35440,35458,35482,35484,35497,35500,35506,35525,35528,35535,35539,35586,35593,35596,35603,35611,35620,35633,35636,35648,35649,35651,35655,35665,35680,35683,35694,35722,35731,35743,35746,35757,35787,35804,35824,35833,35834,35835,35836,35837,35838,35840,35844,35846,35847,35860,35861,35862,35868,35897,35914,35920,35927,35929,35931,35932,35944,35955,35972,35977,35978,36000,36011,36012,36031,36040,36044,36055,36085,36100,36110,36117,36120,36143,36152,36155,36174,36177,36182,36188,36198,36204,36220,36247,36251,36255,36267,36271,36272,36309,36325,36327,36331,36336,36344,36348,36363,36366,36372,36383,36391,36397,36401,36408,36420,36425,36441,36457,36467,36473,36474,36475,36479,36480,36489,36501,36506,36510,36537,36539,36545,36547,36548,36549,39436,39437,39438,39439,39472,39473,39474,39475,39476,39477,39478,39479,39480,39481,39482,39483,39484,39485,39486,39487,39488,39489,39490,39491,39492,39493,39494,39495,39496,39497,39498,39499,39500,39501,39502,39503,39504,39505,39506,39507,39508,39509,39510,39511,39512,39513,39514,39515,39516,39517,39518,39519,39520,39521,39522,39523,39524,39525,39526,39527,39528,39529,39530,39531,39532,39533,39534,39535,39536,39537,39538,39539,39540,39541,39542,39543,39548,39549,39550,39551,39552,39553,39554,39555,39556,39557,39558,39559,39560,39580,39581,39582,39583,39584,39585,39586,39587,39588,39589,39590,39591,39592,39593,39594,39595,39596,39597,39598,39602,39603,39604,39605,39606,39607,39608,39609,39610,39624,39625,39626,39627,39628,39629,39631,39632,39633,39634,39635,39636,39637,39640".split(",");
        /*List<String> filePateList = testMapper.selectPeopleAvatarByPeopleIds(peopleIds);
        for (String avatar : filePateList) {
            String oldPath = avatar.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getPath());
            String[] filePathArr = oldPath.split("/");
            File file = new File(WebdpConfig.getPath() + "/" + filePathArr[filePathArr.length - 1]);
            if (file.exists()) {
                file.delete();
            }
            String newPath = WebdpConfig.getDownloadPath() + "/" + filePathArr[filePathArr.length - 1];
            copyFile(oldPath, newPath);
        }*/
        List<String> newsIds = new ArrayList<>();
        for (String id : peopleIds) {
            IdwPeopleMain people = testMapper.selectPeopleByPeopleId(id);
            newsIds.addAll(testMapper.selectNewsIdByKeyword(people.getNameCn(), people.getNameEn()));
        }
        if (!newsIds.isEmpty()) {
            return String.join(",", newsIds);
        }
        return "操作成功！";
    }

    /**
     * 机构关系迁移
     *
     * @return 结果
     */
    @Override
    public String orgRelationshipMigration() {
        //relationshipMigration();
        //wtjRelationshipMigration();
        //synchronizationRelationshipMigration();
        return "迁移成功！";
    }

    /*String userName;
    Date nowDate;*/

    /**
     * 同步架构
     *
     * @return 结果
     */
   /* public String synchronizationRelationshipMigration() {
        userName = ShiroUtils.getUserName();
        nowDate = DateUtils.getNowDate();
        List<IdwOrg> orgList = testMapper.selectOrgStructureNotData();
        for (IdwOrg org : orgList) {
            String topOrgCode = org.getOrgCode();
            List<IdwOrgRelationship> orgRelationshipList = testMapper.selectOrgRelationshipByParentCode(topOrgCode);
            for (IdwOrgRelationship orgRelationship : orgRelationshipList) {
                IdwOrg o = idwOrgMapper.selectOrgByOrgCode(orgRelationship.getOrgCode());
                IdwOrgStructure orgStructure = new IdwOrgStructure();
                orgStructure.setOrgCode(topOrgCode);
                orgStructure.setParentId((long) 0);
                orgStructure.setAncestors("0");
                orgStructure.setLevel(1);
                orgStructure.setStructureOrgCode(orgRelationship.getOrgCode());
                orgStructure.setNameCn(o.getOrgNameCn());
                orgStructure.setNameEn(o.getOrgNameEn());
                orgStructure.setAvatar(o.getAvatar());
                orgStructure.setType(o.getOrgType());
                orgStructure.setProfileCn(o.getProfileCn());
                orgStructure.setProfileEn(o.getProfileEn());
                orgStructure.setLongitude(o.getLongitude());
                orgStructure.setLatitude(o.getLatitude());
                orgStructure.setSource(StringUtils.isNotBlank(o.getSource()) ? o.getSource() : "");
                orgStructure.setCreateBy(userName);
                orgStructure.setCreateTime(nowDate);
                testMapper.insertOrgStructure(orgStructure);
                synchronizationRelationship(topOrgCode, orgStructure.getStructureId());
            }
        }
        return "同步完成！";
    }*/

    /**
     * 根据顶层机构编码与机构架构ID迁移当前节点子集
     *
     * @param topOrgCode  顶层节点机构编码
     * @param structureId 当前架构节点ID
     */
    /*public void synchronizationRelationship(String topOrgCode, Long structureId) {
        IdwOrgStructure structure = orgStructureMapper.selectIdwOrgStructureById(structureId);
        List<IdwOrgRelationship> orgRelationshipList = testMapper.selectOrgRelationshipByParentCode(structure.getStructureOrgCode());
        int level = structure.getLevel() + 1;
        String ancestors = structure.getAncestors() + "," + structureId;
        for (IdwOrgRelationship orgRelationship : orgRelationshipList) {
            IdwOrg o = idwOrgMapper.selectOrgByOrgCode(orgRelationship.getOrgCode());
            IdwOrgStructure orgStructure = new IdwOrgStructure();
            orgStructure.setOrgCode(topOrgCode);
            orgStructure.setParentId(structureId);
            orgStructure.setAncestors(ancestors);
            orgStructure.setLevel(level);
            orgStructure.setStructureOrgCode(orgRelationship.getOrgCode());
            orgStructure.setNameCn(o.getOrgNameCn());
            orgStructure.setNameEn(o.getOrgNameEn());
            orgStructure.setAvatar(o.getAvatar());
            orgStructure.setType(o.getOrgType());
            orgStructure.setProfileCn(o.getProfileCn());
            orgStructure.setProfileEn(o.getProfileEn());
            orgStructure.setLongitude(o.getLongitude());
            orgStructure.setLatitude(o.getLatitude());
            orgStructure.setSource(StringUtils.isNotBlank(o.getSource()) ? o.getSource() : "");
            orgStructure.setCreateBy(userName);
            orgStructure.setCreateTime(nowDate);
            testMapper.insertOrgStructure(orgStructure);
            synchronizationRelationship(topOrgCode, orgStructure.getStructureId());
        }
    }*/

    /**
     * 机构相关
     *
     * @return 结果
     */
    @Override
    public String updateOrg() {
        //return updateOrgCode();
        //return updateOrgRelationshipId();
        //return migrationBase();
        //return orgStaff();
        //return importOrgArchitecture();
        //return importOrgSystemArchitecture();
        //return insertOrgSystemArchitectureOrg();
        //return deleteRepetitionOrg();
        return migrationOrg();
        //return "操作成功！";
    }

    /**
     * 迁移大系统机构
     *
     * @return 结果
     */
    public String migrationOrg() {
        userName = ShiroUtils.getUserName();
        nowDate = DateUtils.getNowDate();
        //1 政府/军队机构 2 高校/实验室 3 公司/私营部门
        List<IdwOrg> orgList = testMapper.selectOrg();
        orgList.forEach(org -> {
            org.setShowHome(0);
            if (org.getOrgType().equals("1")) {
                org.setOrgType("政府机构");
                org.setOrgTypeAlias("政府机构");
                int maxOrderNum = idwOrgMapper.selectMaxOrderNum(org.getOrgType());
                org.setOrderNum(maxOrderNum + 1);
            } else if (org.getOrgType().equals("2")) {
                org.setOrgType("国防科研");
                org.setOrgTypeAlias("高校/实验室");
                int maxOrderNum = idwOrgMapper.selectMaxOrderNum(org.getOrgType());
                org.setOrderNum(maxOrderNum + 1);
            } else if (org.getOrgType().equals("3")) {
                org.setOrgType("国防企业");
                org.setOrgTypeAlias("公司/私营部门");
                int maxOrderNum = idwOrgMapper.selectMaxOrderNum(org.getOrgType());
                org.setOrderNum(maxOrderNum + 1);
            }
            org.setCountry("US");
            org.setCreateBy(userName);
            org.setCreateTime(nowDate);
            org.setUpdateBy("大系统美国国防部及其子节点以外机构迁移");
            idwOrgMapper.insertIdwOrg(org);
            //添加组织架构
            List<OrgArchitecture> architectureList = testMapper.selectOrgArchitectureByOrgCode(org.getOrgCode());
            if (!architectureList.isEmpty()) {
                List<OrgArchitecture> children = architectureList.stream().filter(architecture -> architecture.getParentCode().equals(org.getOrgCode())).collect(Collectors.toList());
                for (OrgArchitecture child : children) {
                    if (StringUtils.isBlank(child.getNameCn())) {
                        child.setNameCn(child.getNameEn());
                    }
                    long id = SnowIdUtils.uniqueLong();// 获取雪花id
                    child.setArchitectureId(id);
                    child.setParentId((long) 0);
                    child.setAncestors("0");
                    child.setLevel(1);
                    child.setCreateBy(userName);
                    child.setCreateTime(nowDate);
                    testMapper.insertOrgArchitecture(child);
                    //查询子集
                    List<OrgArchitecture> list = architectureList.stream().filter(architecture -> architecture.getParentCode().equals(child.getNodeOrgCode())).collect(Collectors.toList());
                    if (!list.isEmpty()) {
                        insertOrgArchitecture(architectureList, list, id);
                    }
                }
            }
        });
        return "操作成功！";
    }

    /**
     * 新增机构架构
     *
     * @param orgArchitectureList 机构架构对象
     * @param child               当前机构架构对象
     * @param parentId            上级ID
     */
    public void insertOrgArchitecture(List<OrgArchitecture> orgArchitectureList, List<OrgArchitecture> child, Long parentId) {
        for (OrgArchitecture orgArchitecture : child) {
            IdwOrgArchitecture parent = idwOrgArchitectureMapper.selectIdwOrgArchitectureById(parentId);
            if (StringUtils.isBlank(orgArchitecture.getNameCn())) {
                orgArchitecture.setNameCn(orgArchitecture.getNameEn());
            }
            long id = SnowIdUtils.uniqueLong();// 获取雪花id
            orgArchitecture.setArchitectureId(id);
            orgArchitecture.setParentId(parentId);
            orgArchitecture.setAncestors(parent.getAncestors() + "," + parentId);
            orgArchitecture.setLevel(parent.getLevel() + 1);
            orgArchitecture.setCreateBy(userName);
            orgArchitecture.setCreateTime(nowDate);
            testMapper.insertOrgArchitecture(orgArchitecture);
            //查询子集
            List<OrgArchitecture> list = orgArchitectureList.stream().filter(architecture -> architecture.getParentCode().equals(orgArchitecture.getNodeOrgCode())).collect(Collectors.toList());
            if (!list.isEmpty()) {
                insertOrgArchitecture(orgArchitectureList, list, id);
            }
        }
    }

    /**
     * 删除重复机构
     *
     * @return 结果
     */
    public String deleteRepetitionOrg() {
        //部队
        String orgCodes = "O00596,O00331,O00329,O00375,O00692,O00705,O00694,O00572,O00706,O00712,O00699,O00711,O00563,O00567,O00582,O00686,O00700,O00687,O00665,O00568,O00683,O00569,O00708,O00688,O00677,O00595,O00566,O00501,O00508,O00623,O00526,O00515,O00523,Z003777,O00138,O00123,Z000342,Z000343,O00134,Z001402,Z003599,Z003305,Z001166,Z003333,Z002855,O00230,O00189,Z001302,O00244,O00180,Z003060,O00223,Z000340,Z000341,Z001171,Z001175,Z001172,Z001176,O00217,Z000351,Z000355,Z000361,Z000356,Z000360,Z000352,Z000353,Z000354,O00150,O00158,O00157,O00222,Z002706,O00181,O00353,O00373,O00207,O00524,O00425,O00364,O00307,O00363,O00206,O00165,Z000349,Z000350,Z001980,O00323";
        String repetitionOrgCodes = "Z004109,Z001909,Z001917,Z000770,Z001446,Z000716,Z001427,Z000828,Z000701,Z000712,Z001450,Z000706,Z003362,Z000814,Z001361,Z001440,Z001491,Z001452,Z003806,Z000821,Z001473,Z000822,Z000715,Z001471,Z003523,Z003512,Z000813,Z003304,Z003363,Z003956,Z001350,Z003790,Z004367,Z003765,Z000238,Z000142,Z001174,Z001170,Z000259,Z001893,Z003622,Z003907,Z000334,Z003314,Z001469,Z001205,Z000008,Z004039,Z004409,Z000592,Z003062,Z004541,Z001169,Z001173,Z000345,Z000347,Z000348,Z000346,Z003090,Z001181,Z001185,Z001186,Z001177,Z001178,Z001182,Z001183,Z001184,Z000077,Z000081,Z000079,Z000803,Z004445,Z000594,Z001970,Z000731,Z000725,Z000685,Z002661,Z000903,Z001289,Z000684,Z000723,Z003301,Z001179,Z001180,Z001983,Z000838";
        String[] orgCodeArr = orgCodes.split(",");
        String[] repetitionOrgCodeArr = repetitionOrgCodes.split(",");
        for (int i = 0; i < orgCodeArr.length; i++) {
            String orgCode = orgCodeArr[i];
            String repetitionOrgCode = repetitionOrgCodeArr[i];
            System.err.println(orgCode + "          " + repetitionOrgCode);
            /*testMapper.updateOrgSystemArchitectureOrgCode(repetitionOrgCode, orgCode);
            testMapper.updateOrgStaffOrgCode(orgCode, repetitionOrgCode);
            testMapper.deleteRepetitionOrg(repetitionOrgCode, orgCode);*/
        }
        //企业
        //963737366-112716357,T01458,O03025,441905699899838689,boyinminyongfeijijituan(BCA)-boyinminyongfeijijituanzigongsi,SUBS-BPM,O03059,T00014,441905736440615137,O03040,O03052,O03014,O03013,O03012,O03036,441905689825120481,457171529801666729,O03015,O03048,W010007,469454696038273183,qitazigongsi-yingxitugongsi,W010005,O03022,O02223,W010008,O03011,DIV-PW,W010002
        //qitazigongsi-yingyongwulikexuegongsi(APS),457218634725463685,T00034,haishixitong-basigangtiegongsi(BIW),boyinfangyu、kongjianyuanquan(BDS)-boyinfangyu、kongjianyuanquanfenbudezigongsi,093983034,T00048-beierzhishengjideshilong,W010004,yewudanyuan-boyinfangyu、kongjianyuanquan(BDS),T00336,T00043-tongyongdianqihangkong,067638957,RC-GAAS,T00010,haishixitong-tongyongdonglidianchuangongsi,xinxijishu-tongyongdonglixinxijishugongsi(GDIT),tongyongdongliludixitonggongsi(GDLS)-tongyongdonglijiqirenxitonggongsi,T00043,T00041,T00011,T00011-yinggeersizaochuanchang,AS-INS,T00013,T00023,gongchengbu-jixiegongchengzu,T00011-niuboteniusizaochuanchang(NNS),T00012,boyinfangyu、kongjianyuanquan(BDS)-guiguaigongchang,T00048
        //haishixitong-tongyongdonglidianchuangongsi与001381284-963737366是同一个


        //基地
        //Z004016,Z002865,Z002864,Z002863,Z002611,Z001944,Z001814,Z001813,Z001812,Z002502,Z002460,Z002453,Z000676,Z002610,Z003941,Z003939,Z001426,Z003265,Z003201,Z000631,Z003266,Z002866,Z001464,Z002867,Z002868,Z002869,Z002870,Z002871,Z002872,Z002314,Z002239,Z002240,Z002245,Z000443,Z002052,Z002043,Z002145,Z002092,Z001946,Z002005,Z002873,Z000444,Z002246,Z002360,Z002316

        //单位合并
        //O00413,O00406 合并为国防单位  O00214,O03021 合并为国防单位 O00213,O03020合并为国防单位 O00216,O03019合并为国防单位 O03024,Z003041合并为国防单位 Z003030,457218634721269370 合并为国防单企业 *********,Z003053 合并为国防单位 T00003,Z003129 合并为国防单位 Z000380,O01020 合并为国防单位 O03017,T00992,RMS-xikesijifeijigongsi 合并 W010006,T00050 合并为国防科研 O03053,T20003253 合并为国防科研
        return "操作成功！";
    }

    public String insertOrgSystemArchitectureOrg() {
        userName = ShiroUtils.getUserName();
        nowDate = DateUtils.getNowDate();
        List<IdwOrg> orgList = testMapper.selectOrgByOrgTypes("国防科研,国防企业".split(","));
        //603579540858343424
        List<IdwOrg> enterprisesList = orgList.stream().filter(org -> "国防企业".equals(org.getOrgType())).collect(Collectors.toList());
        long enterprisesId = Long.parseLong("603579540858343424");
        Integer enterprisesMaxOrderNum = idwOrgSystemArchitectureMapper.selectMaxOrderNumByParentId(enterprisesId);
        for (IdwOrg org : enterprisesList) {
            long id = SnowIdUtils.uniqueLong();// 获取雪花id
            IdwOrgSystemArchitecture architecture = new IdwOrgSystemArchitecture();
            architecture.setId(id);
            architecture.setSystemId(enterprisesId);
            architecture.setType("organization");
            architecture.setOrgCode(org.getOrgCode());
            architecture.setName(StringUtils.isNotBlank(org.getOrgNameCn()) ? org.getOrgNameCn() : org.getOrgNameEn());
            architecture.setParentId((long) 0);
            architecture.setAncestors("0");
            architecture.setColor("#ffffff");
            architecture.setIsShow("1");
            architecture.setProfile(StringUtils.isNotBlank(org.getProfileCn()) ? org.getProfileCn() : org.getProfileEn());
            architecture.setLevel(1);
            architecture.setOrderNum(enterprisesMaxOrderNum++);
            architecture.setCreateBy(userName);
            architecture.setCreateTime(nowDate);
            idwOrgSystemArchitectureMapper.insertOrgArchitecture(architecture);
        }
        //602203079966593024
        List<IdwOrg> researchList = orgList.stream().filter(org -> "国防科研".equals(org.getOrgType())).collect(Collectors.toList());
        long researchId = Long.parseLong("602203079966593024");
        Integer researchMaxOrderNum = idwOrgSystemArchitectureMapper.selectMaxOrderNumByParentId(researchId);
        for (IdwOrg org : researchList) {
            long id = SnowIdUtils.uniqueLong();// 获取雪花id
            IdwOrgSystemArchitecture architecture = new IdwOrgSystemArchitecture();
            architecture.setId(id);
            architecture.setSystemId(researchId);
            architecture.setType("organization");
            architecture.setOrgCode(org.getOrgCode());
            architecture.setName(StringUtils.isNotBlank(org.getOrgNameCn()) ? org.getOrgNameCn() : org.getOrgNameEn());
            architecture.setParentId((long) 0);
            architecture.setAncestors("0");
            architecture.setColor("#ffffff");
            architecture.setIsShow("1");
            architecture.setProfile(StringUtils.isNotBlank(org.getProfileCn()) ? org.getProfileCn() : org.getProfileEn());
            architecture.setLevel(1);
            architecture.setOrderNum(researchMaxOrderNum++);
            architecture.setCreateBy(userName);
            architecture.setCreateTime(nowDate);
            idwOrgSystemArchitectureMapper.insertOrgArchitecture(architecture);
        }
        return "操作成功！";
    }

    public String importOrgSystemArchitecture() {
        userName = ShiroUtils.getUserName();
        nowDate = DateUtils.getNowDate();
        List<IdwOrgSystemArchitecture> orgSystemArchitectureList = testMapper.selectOrgStructure("T10709", (long) 0);
        Integer maxOrderNum = idwOrgSystemArchitectureMapper.selectMaxOrderNumByParentId(Long.parseLong("602580684985208832"));
        for (IdwOrgSystemArchitecture orgSystemArchitecture : orgSystemArchitectureList) {
            orgSystemArchitecture.setParentId(Long.parseLong("602580684985208832"));
            orgSystemArchitecture.setAncestors("0,602580684985208832");
            orgSystemArchitecture.setOrderNum(maxOrderNum++);
            orgSystemArchitecture.setCreateBy(userName);
            orgSystemArchitecture.setCreateTime(nowDate);
            orgSystemArchitecture.setLevel(orgSystemArchitecture.getAncestors().split(",").length);
            idwOrgSystemArchitectureMapper.insertOrgArchitecture(orgSystemArchitecture);
            insertOrgSystemArchitecture(orgSystemArchitecture.getId());
        }
        return "操作成功";
    }

    public void insertOrgSystemArchitecture(Long parentId) {
        List<IdwOrgSystemArchitecture> orgSystemArchitectureList = testMapper.selectOrgStructure("T10709", parentId);
        if (orgSystemArchitectureList.size() > 0) {
            IdwOrgSystemArchitecture parent = idwOrgSystemArchitectureMapper.selectOrgSystemArchitectureById(parentId);
            Integer maxOrderNum = idwOrgSystemArchitectureMapper.selectMaxOrderNumByParentId(parentId);
            for (IdwOrgSystemArchitecture orgSystemArchitecture : orgSystemArchitectureList) {
                orgSystemArchitecture.setParentId(parentId);
                orgSystemArchitecture.setAncestors(parent.getAncestors() + "," + parentId);
                orgSystemArchitecture.setOrderNum(maxOrderNum++);
                orgSystemArchitecture.setCreateBy(userName);
                orgSystemArchitecture.setCreateTime(nowDate);
                idwOrgSystemArchitectureMapper.insertOrgArchitecture(orgSystemArchitecture);
                insertOrgSystemArchitecture(orgSystemArchitecture.getId());
            }
        }
    }

    /**
     * 导入机构架构
     *
     * @return 结果
     */
    public String importOrgArchitecture() {
        userName = ShiroUtils.getUserName();
        nowDate = DateUtils.getNowDate();
        //查询所有机构架构 国防企业和国防科研
        List<IdwOrg> orgList = testMapper.selectOrgByOrgTypes("国防科研,国防企业".split(","));
        for (IdwOrg org : orgList) {
            topOrgCode = org.getOrgCode();
            migrationOrgRelationshipGoArchitecture(null, null, null);
        }
        //idwOrgArchitectureMapper
        return "操作成功";
    }

    String topOrgCode;
    String userName;
    Date nowDate;

    public void migrationOrgRelationshipGoArchitecture(String orgCode, Long parentId, String ancestors) {
        List<IdwOrgRelationship> orgRelationshipList = new ArrayList<>();
        if (StringUtils.isBlank(orgCode)) {
            orgRelationshipList = testMapper.selectOrgRelationshipByParentCode(topOrgCode);
        } else {
            orgRelationshipList = testMapper.selectOrgRelationshipByParentCode(orgCode);
        }
        for (IdwOrgRelationship orgRelationship : orgRelationshipList) {
            IdwOrg org = idwOrgMapper.selectOrgByOrgCode(orgRelationship.getOrgCode());
            long id = SnowIdUtils.uniqueLong();// 获取雪花id
            IdwOrgArchitecture orgArchitecture = new IdwOrgArchitecture();
            orgArchitecture.setArchitectureId(id);
            parentId = StringUtils.isNull(parentId) ? 0 : parentId;
            orgArchitecture.setParentId(parentId);
            ancestors = StringUtils.isBlank(ancestors) ? "0" : ancestors;
            orgArchitecture.setAncestors(ancestors);
            orgArchitecture.setLevel(ancestors.split(",").length);
            orgArchitecture.setOrgCode(topOrgCode);
            orgArchitecture.setNodeOrgCode(org.getOrgCode());
            orgArchitecture.setNameCn(org.getOrgNameCn());
            orgArchitecture.setNameEn(org.getOrgNameEn());
            orgArchitecture.setType("部门");
            orgArchitecture.setCreateBy(userName);
            orgArchitecture.setCreateTime(nowDate);
            idwOrgArchitectureMapper.insertIdwOrgArchitecture(orgArchitecture);
            migrationOrgRelationshipGoArchitecture(orgArchitecture.getNodeOrgCode(), id, orgArchitecture.getAncestors() + "," + id);
        }
    }

    /**
     * 机构人员相关
     *
     * @return 结果
     */
    public String orgStaff() {
        //String filePath = "F:\\HCZY\\Excel导入完成备份\\外台军\\2023-11-03 外台军人员更新\\机构人员导.xlsx";
        //String filePath = "F:\\HCZY\\Excel导入完成备份\\外台军\\2023-11-09 外台军机构人员（王玲玲）\\机构人员导入.xlsx";
        //String filePath = "F:\\HCZY\\Excel导入完成备份\\外台军\\2023-10-27 外台军机构人员更新（王玲玲）\\机构人员导.xlsx";
        String filePath = "F:\\HCZY\\Excel导入完成备份\\外台军\\2023-11-03 外台军人员更新（王玲玲）\\机构人员导入-1103.xlsx";
        String userName = ShiroUtils.getUserName();
        Date nowDate = DateUtils.getNowDate();
        List<OrgStaffVo> list = new ArrayList<>();
        try {
            ExcelUtil<OrgStaffVo> util = new ExcelUtil<OrgStaffVo>(OrgStaffVo.class);
            InputStream is = new FileInputStream(new File(filePath));
            list = util.importExcel("Sheet1", is);
            is.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        int row = 1;
        List<String> msgList = new ArrayList<>();
        String previouOrgCode = "";
        String previouOrgName = "";
        for (OrgStaffVo orgStaffVo : list) {
            row++;
            String orgCode = orgStaffVo.getOrgCode();
            String orgName = orgStaffVo.getOrgNameCn();
            if (orgStaffVo.getPeopleNameEn().equals(orgStaffVo.getInsertStaff())) {
                continue;
            }
            if (StringUtils.isBlank(orgCode)) {
                orgCode = previouOrgCode;
            } else {
                previouOrgCode = orgCode;
                previouOrgName = orgName;
            }
            if (StringUtils.isBlank(orgStaffVo.getPosition()) && StringUtils.isBlank(orgStaffVo.getInsertStaff())) {
                orgStaffVo.setPosition("现任");
            } else if (StringUtils.isBlank(orgStaffVo.getPosition()) && StringUtils.isNotBlank(orgStaffVo.getInsertStaff())) {
                orgStaffVo.setPosition("离任");
            }
            //更新机构人员职位
            testMapper.updateOrgStaffPosition(orgCode, orgStaffVo.getPeopleNameEn(), orgStaffVo.getPosition());
            //更新机构人员服役状态
            if (StringUtils.isNotBlank(orgCode) && StringUtils.isNotBlank(orgStaffVo.getPeopleNameEn()) && "离任".equals(orgStaffVo.getStatus())) {
                testMapper.updateOrgStaffStatus(orgCode, orgStaffVo.getPeopleNameEn(), orgStaffVo.getPosition(), orgStaffVo.getStatus());
            }
            String insertStaff = orgStaffVo.getInsertStaff();
            if (StringUtils.isNotBlank(insertStaff)) {
                List<IdwPeopleMain> peopleMainList = idwPeopleMainMapper.selectByPeopleName(insertStaff, null);
                if (peopleMainList.size() == 1) {
                    IdwPeopleMain people = peopleMainList.get(0);
                    IdwOrgStaff staff = new IdwOrgStaff();
                    staff.setOrgCode(orgCode);
                    staff.setPeopleNameCn(people.getNameCn());
                    staff.setPeopleNameEn(people.getNameEn());
                    staff.setAvatar(people.getAvatar());
                    staff.setProfileCn(people.getProfileCn());
                    staff.setProfileEn(people.getProfileEn());
                    staff.setPosition(orgStaffVo.getPosition());
                    staff.setStatus("现任");
                    staff.setSource(orgStaffVo.getSource());
                    staff.setCreateBy(userName);
                    staff.setCreateTime(nowDate);
                    IdwOrgStaff orgStaff = testMapper.selectOrgStaff(staff.getOrgCode(), staff.getPeopleNameCn(), staff.getPeopleNameEn(), staff.getPosition());
                    if (StringUtils.isNull(orgStaff)) {
                        idwOrgStaffMapper.insertIdwOrgStaff(staff);
                    }
                    people.setOrgCode(orgCode);
                    people.setOrgName(previouOrgName);
                    people.setPost(orgStaffVo.getPosition());
                    idwPeopleMainMapper.updatePeople(people);
                } else if (peopleMainList.size() > 1) {
                    msgList.add("第" + row + "行," + insertStaff + "在人员库中存在多条记录");
                } else {
                    msgList.add("第" + row + "行," + insertStaff + " 在人员库中不存在");
                }
            } else /*if ("新增".equals(orgStaffVo.getRemark()))*/ {
                List<IdwPeopleMain> peopleMainList = idwPeopleMainMapper.selectByPeopleName(orgStaffVo.getPeopleNameEn(), null);
                if (peopleMainList.size() == 1) {
                    IdwPeopleMain people = peopleMainList.get(0);
                    IdwOrgStaff staff = new IdwOrgStaff();
                    staff.setOrgCode(orgCode);
                    staff.setPeopleNameCn(people.getNameCn());
                    staff.setPeopleNameEn(people.getNameEn());
                    staff.setAvatar(people.getAvatar());
                    staff.setProfileCn(people.getProfileCn());
                    staff.setProfileEn(people.getProfileEn());
                    staff.setPosition(orgStaffVo.getPosition());
                    staff.setStatus("现任");
                    staff.setSource(orgStaffVo.getSource());
                    staff.setCreateBy(userName);
                    staff.setCreateTime(nowDate);
                    IdwOrgStaff orgStaff = testMapper.selectOrgStaff(staff.getOrgCode(), staff.getPeopleNameCn(), staff.getPeopleNameEn(), staff.getPosition());
                    if (StringUtils.isNull(orgStaff)) {
                        idwOrgStaffMapper.insertIdwOrgStaff(staff);
                    }
                    people.setOrgCode(orgCode);
                    people.setOrgName(previouOrgName);
                    people.setPost(orgStaffVo.getPosition());
                    idwPeopleMainMapper.updatePeople(people);
                }
            }
        }
        return msgList.size() > 0 ? String.join("；", msgList) : "操作成功";
    }

    /**
     * 迁移军事基地
     *
     * @return 结果
     */
    /*public String migrationBase() {
        List<InstallationVo> installationVoList = testMapper.selectBase();
       *//* for (Installation installation : installationList) {
            Installation collectInstallation = testMapper.selectInstallationByFullName(installation.getFullName());
            if (StringUtils.isNull(collectInstallation)) {
                //军事设施不存在，直接新增
                long id = SnowIdUtils.uniqueLong();// 获取雪花id
                installation.setId(id);
                testMapper.insertInstallation(installation);
            } else {
                //存在，更新数据
                if (StringUtils.isNull(installation.getLatitude()) || StringUtils.isNull(installation.getLongitude())) {
                    installation.setLatitude(null);
                    installation.setLongitude(null);
                }
                installation.setId(collectInstallation.getId());
                testMapper.updateInstallation(installation);
                testMapper.updateInstallationUnitInstallationCodeByInstallationId(collectInstallation.getId(), installation.getInstallationCode());
            }
        }*//*
        Date nowDate = DateUtils.getNowDate();
        //驻扎部队
        String orgCodes = installationVoList.stream().map(InstallationVo::getInstallationCode).collect(Collectors.joining(","));
        List<IdwOrgTroops> orgTroopsList = orgTroopsMapper.selectByOrgCodes(orgCodes.split(","), false);
        for (IdwOrgTroops troops : orgTroopsList) {
            InstallationUnitVo installationUnitVo = new InstallationUnitVo();
            long id = SnowIdUtils.uniqueLong();// 获取雪花id
            installationUnitVo.setId(id);
            installationUnitVo.setInstallationCode(troops.getOrgCode());
            installationUnitVo.setCountry(troops.getCountry());
            installationUnitVo.setUnitNameCn(troops.getTroopsNameCn());
            installationUnitVo.setUnitNameEn(troops.getTroopsNameEn());
            installationUnitVo.setUnitNameEn(troops.getTroopsNameEn());
            installationUnitVo.setStartDate(troops.getStartDate());
            installationUnitVo.setEndDate(troops.getEndDate());
            installationUnitVo.setMission(troops.getTask());
            installationUnitVo.setTroopsNum(troops.getTroopsNum());
            installationUnitVo.setSource(troops.getSource());
            installationUnitVo.setCreateBy("迁移");
            installationUnitVo.setCreateTime(nowDate);
            testMapper.insertInstallationUnit(installationUnitVo);
        }
        return "操作完成！";
    }*/

    /**
     * 资源库相关
     *
     * @return 结果
     */
    @Override
    public String updateResource() {
        //return updateResouceTags();
        //return integrationResouce();
        List<String> inexistenceFilePathList = new ArrayList<>();
        String paths = "28036,28068,29783,29613,27727,22440,26889,21586,22157,28771,27556,27496,22135,26734,27240,28944,22262,28688,29473,29662,23071,28803,28563,21916,29021,28596,22023,27780,23198,22049,26619,22928,29051,23163,27220,22202,23135,26744,28112,22188,22473,27634,22934,21871,22374,22137,22433,28804,22383,22029,29049,27541,29435,27657,22910,21972,22958,28081,28770,27741,27620,22449,26702,22153,29409,27639,26726,27203,21639,26491,22193,29593,21958,29575,22021,21839,29597,29121,22358,22268,27763,29586,26598,22506,28795,28374,27676,28349,27715,29527,23173,21968,28879,22305,29040,21973,28087,26926,21489,21506,21525,21526,21537,21538,21540,21544,21554,21579,21485,21491,21501,21510,21517,21524,21528,21549,21552,21578,21487,21509,21515,21539,21547,21577,21580,21585,21797,21811,24762,24831,23730,25843,24257,25752,23800,26120,24422,28426,21551,21583,21596,21604,21629,21646,21650,21664,21810,21819,21504,21516,21546,21559,21600,21609,21617,21625,21626,21655,21570,22131,22404,22854,22859,22884,27877,23138,26755,26919,21505,21627,28234,22198,22292,22866,22925,22961,22982,27879,21486,21555,21556,21589,21592,21602,21611,21643,21653,21798,21502,21575,21576,21594,21599,21619,21666,21681,21690,21801,21542,21566,21571,21613,21654,21660,21661,21662,21683,21796,29577,27534,21924,22250,23032,27603,28055,23223,29372,21691,28036,28068,29783,29613,27727,22440,26889,21586,22157,28771,29572,22343,27685,22013,21994,28244,28245,28246,28247,28248,22002,22159,21492,21514,27513,21548,21944,21557,26764,21567";
        List<String> list = testMapper.selectResourceFilePathByIds(paths.split(","));
        list.addAll(testMapper.selectCollectionDocumentFilePath());
        for (String path : list) {
            String[] p = path.split("/");
            String fileName = p[p.length - 1];
            String filePath = path.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile());
            if (!new File(filePath).exists()) {
                inexistenceFilePathList.add(fileName);
            } else {
                String newFilePath = WebdpConfig.getDownloadPath() + fileName;
                if (!new File(newFilePath).exists()) {
                    FileUploadUtils.copyFile(filePath, newFilePath);
                }
            }
        }
        return String.join(",", inexistenceFilePathList);
        //return inexistenceFilePathList.size() + "";
    }

    /**
     * elasticsearch相关
     *
     * @return 结果
     */
    @Override
    public String elasticsearch() {

        return null;
    }
    /*@Autowired
    private RestHighLevelClient restClientBuilder;

    public void wildcardQuery() throws IOException {
        String[] keywords = "迈克尔·法兰西，Michael France，克里斯托弗·胡利特，Christopher Hulitt，帕特里克·奥蒂斯，Patrick Otis，克里斯托弗·巴纳，Christopher Bahner，大卫·F·哈里斯，David F. Harris，托马斯·E·吉尔汉姆二世，Thomas E. Gilham II，谢恩·卡登，Shane Cardon，杜汶·切尼，Dewon Chaney，萨姆·布莱恩特，Samuel Bryant，蒂凡尼·马吉，Tiffany N. Magee，杰弗里·梅洛迪，Jeffrey A. Melody，布兰农·S·比克尔，Brannon S. Bickel，凯文·W·佩里曼，Kevin W. Perryman，肯尼斯·R·科尔曼，Kenneth R. Colman，肖恩·罗切劳，Sean Rocheleau，爱德华·韦勒，Edward Weiler，凯文·A·布恩，Kevin A. Boone，安东尼·巴恩斯，Anthony Barnes，塞德里克·L·杰瑟普，Cedrick L. Jessup，特洛伊·M·纽森，Troy M. Newsom，帕特里克·科里根，Patrick Corrigan，鲍比·马尔科维奇，Bobby Markovich，丹尼尔·奎因，Daniel Quinn，莫妮卡·J·库尔，Monica J. Kuhl，温斯顿·E·斯科特二世，Winston E. Scott II，马修.D.马特，Matthew D. Matter，罗伊·基奇纳，Roy Kitchener，乔伊·道金，Joey Dodgen，詹姆斯 奥斯本，James Osborne，威廉·戴利，William R. Daly，迈克尔·墨菲，Micah Murphy，科尔比·霍华德，Colby Howard，克里斯托弗S.英格兰，Christopher S. England，米奇·琼斯，Mickey Jones，艾伦L麦克斯韦，Allen L. Maxwell, Jr.，菲尔·里奥斯，Phil Rios，昆廷·T·纽森，Quentin T. Newsom，罗伯特B.查德威克，Robert B. Chadwick, II，卡尔森，克里斯 福林，Chris Follin，特伦斯·科尔曼，Terence Coleman，迈拉·普雷斯科，Myla Presco，拉里A.林奇，Larry A. Lynch，奥古斯丁·C·库珀，Augustine C. Cooper，贾里德·穆勒，Jared D. Mueller，迈克尔·雷，D. Michael Ray，布莱克·G·希梅尔，Blake G. Schimmel，马修·J·斯拉舍，Matthew J. Thrasher，汤米 洛克，Tommy Locke，迈克尔L.韦斯特盖特，Michael L. Westgate，阿曼达·L·戴维斯，Amanda L. Davis，布莱恩·哈普利，Brian Happli，尼古拉斯·古德，Nicholas Good，刘易斯·W·卡拉威，Lewis W. Callaway，克雷格·瓦夫鲁斯克，Kraig Vavrusk，T.J. 泽尔，T.J. Zerr，迈克尔 兰贝恩，Micheal \"SNAP\" Langbehn，安德鲁，Andrew \"GRAND\" Mariner，耶利米J. 霍勒，Jeremiah J. Holler，凯文·麦克纳特，Kevin McNatt，斯科特L.普利斯，Scott L. Pleus，马修C.托马斯，Matthew C. Thomas，托德F.西米卡塔，Todd F. Cimicata，克里斯托弗·赫斯特，Christopher Hurst，罗尼C.弗里曼，Ronnie C. Freeman，小富兰克林 E.多明格斯，Franklin E. Dominguez Jr.，齐姆贝 S.哈里斯，Tshombe S. Harris，马修·福安特，Matthew Fountain，威廉R.默兹，William R. Merz，罗伯特T.克拉克，Robert T. Clark，杰森M.哈卡，Jason M. Haka，蒂莫西A.雷克斯罗德，Timothy A. Rexrode，杰弗里S.海因曼，Jeffrey S. Hineman，布莱克L.匡威，Blake L. Converse，马丁J.穆基安，Martin J. Muckian，威尔·彭宁顿，Will Pennington，博 约翰斯，Bo Johns，柯蒂斯D.布朗特，Curtis D. Blunt，阿德里安T.考尔德，Adrian T. Calder，迈克尔“Pup”斯威尼，Michael \"Pup\" Sweeney，杰弗里·里德，Jeffrey Reeder，沃尔特·梅诺尔，Walter C. Mainor，杰里米·卡尔森，Jeremy Carlson，安德鲁·托马森，Andrew Thomasson，特洛伊A.波乔尔基奇，Troy A. Bojorquiz，贾瑞尔C.加德纳，Jarriel C. Gardner，杰夫·比利，Jeff Bierley，布雷特·杰克逊，Brett Jackson，布雷特·格拉贝，Bret Grabbe，道格拉斯·佩里，Douglas Perry，约翰W.范切尔，John W. Fancher，乔恩·米勒，Jon Miller，杰弗里A.巴恩斯，JEFFERY A. BARNES，德里克F.马伦霍，Derek F. Mullenhour，曼宁·蒙塔涅特，Manning Montagnet，大卫A.奥洛斯基，David A. Orlosky，丹尼尔J.欧文，Daniel J. Irwin，杰弗里·格里姆斯，Jeffrey Grimes，史蒂夫·安苏尼，Steve Ansuini，加里 怀斯，Gary Wise，达伦B.根瑟，Darren B. Guenther，马里奥 里弗斯，Mario Rivers，埃里克·斯皮策，Erik A. Spitzer，塔米L.哈里斯，Tammie L. Harris，安东尼M.佩科拉罗，Anthony M. Pecoraro，乔·洛夫蕾丝，Joe Lovelace，杰里米·耶茨，Jeremy D. Yates，布莱恩P.克鲁泽尔尼克，BRIAN P. KRUZELNICK，安德鲁J.坎贝尔，ANDREW J. CAMPBELL，杰森T.米尔斯，JASON T. MILLS，约翰E.佩恩，JOHN E. PAYNE，乔尔L.凯里，JOEL L. CAREY，乔治N.沃格尔，GEORGE N. VOGEL，杰西卡L.本德，JESSICA L. BENDER，杰西J.弗里德尔，JESSE J. FRIEDEL，大卫S.伊格林，DAVID S. EAGLIN，约翰F.冈萨雷斯，JOHN F. GONZALES，亨利R.杰弗斯三世，Henry R. Jeffress, III，亚当J.维兹，ADAM J. VIZI，克里斯B. 沃尔夫·哈蒙德，CHRIS B. “WOLF” HAMMOND，丹尼尔A.多贝尔，Daniel A. Dobbels，史蒂文O.科勒，Steven O. Koehler Jr.，杰森B.特里，JASON B. TERRY，查尔斯D.库利，CHARLES D. COOLEY，瑞奇B.史密斯，RICKY B. SMITH，爱德华J.巴兰科，Edward J. Ballanco，乔纳森J.布鲁，Jonathon J. Blue，雷蒙德S.迈尔斯，Raymond S. Myers，卡斯滕 赫克尔，Karsten \"Hazel\" Heckl，托马斯B.萨维奇，Thomas B. Savage，乔治 C.施雷夫勒三世，George C. Schreffler, III，詹姆斯 波特菲尔德，James Porterfield，托比A.鲁伊斯，Toby A. Ruiz，罗杰B.特纳,Jr，Roger B. Turner, Jr.，克里斯托弗D.吉迪恩斯，Christopher D. Gideons，特伦斯C.惠特科姆，Terrence C. Whitcomb，帕特里克保罗C.曼加兰，PatrickPaul C. Mangaran，马修T.古德，Matthew T. Good，詹姆斯L.霍尔，James L. Horr，罗萨莉娅 西福，Rosalia Scifo，罗伯塔L.谢伊，Roberta L. Shea，安德鲁J.伯根，Andrew J. Bergen，斯坦内特W.雷伊，Stennett W. Rey，洛伦·鲁克，Loren Rucker，罗达 柯尔比，Ronda R. Kirkby，克里斯托弗J.马奥尼，Christopher J. Mahoney，詹姆斯F.哈普，James F. Harp，大卫M.斯蒂尔，David M. Steele，大卫A.威尔逊，David A. Wilson，哈兰B.帕塔瓦兰，Harlan B. Patawaran，雷金纳德·丹尼尔斯，Reginald Daniels，布莱恩R.德雷克斯勒，Brian R. Drechsler，W. J.巴托洛梅亚，W. J. Bartolomea，埃琳娜M.罗德里格斯，Elena M. Rodriguez，吉姆W.莱弗利，Jim W. Lively，特拉维斯L.德巴尔，Travis L. DeBarr，勒 埃.诺兰，Le E. Nolan，安德鲁T.普里迪，Andrew T. Priddy，D. J. 希波尔，D. J. Hipol，斯图尔特D.格拉斯，Stuart D. Glass，克里斯托弗J.布朗齐，Christopher J. Bronzi，约瑟夫C.贝格利，Joseph C. Begley，丹尼尔E.曼格鲁姆，Daniel E. Mangrum，H 斯泰西·克拉迪三世，H Stacy Clardy, III，凯尔B.埃里森，KYLE B. ELLISON，迈克尔·P伍兹，MICHAEL P. WOODS，克里斯托弗W.摩尔，CHRISTOPHER W. MOORE，克里斯托弗A.麦克菲利普斯，Christopher A. McPhillips，斯蒂芬·格里芬，Stephen A Griffin，美狄亚A.达德利，Medea A. Dudley，贾里德B.戈姆，Jared B. Game，布莱恩N.沃尔福德，Brian N. Wolford，罗杰D.纽科姆，Rodger D. Newcomb，乔纳森M.卡特，Jonathon M. Carter，迈克尔R.纳科尼克兹尼，Michael R. Nakonieczny，斯宾塞E.斯科特，Spencer E. Scott，拉里M.詹金斯，LARRY M. JENKINS，阿丹F.莫雷诺，Adan F. Moreno，马修J.福斯，Matthew J. Fouss，兰德尔.里德，RANDALL REED，威廉.马歇尔，WILLIAM L. MARSHALL，兰迪.奎亚特科夫斯基，RANDY KWIATKOWSKI，特洛伊.帕纳农，S.TROY PANANON，布莱恩.L.贝克，BLAINE L. BAKER，凯西.W.格拉斯科克，KATHI W. GLASCOCK，杰森.E.布里格，JASON E. BAILEY，杰米.L.纽曼，JAMIE L. NEWMAN，约翰.B.克瑞尔，John B. Creel，雪莉.L.门迪塔，Shelly L. Mendieta，玛丽贝丝.O.费雷尔，Maribeth O. Ferrer，基因.B.詹姆逊三世，Gene B. Jameson III，斯蒂芬妮.A.凯特，Stephanie A. Cates，大卫.C.埃佩森，David C. Epperson，霍卡吉，Hokaj，霍普.L.斯基比茨基，Hope L. Skibitsky，约书亚.M.奥尔森，Joshua M. Olson，马修.S.胡斯曼，Matthew S. Husemann，斯科特.C.洛卡，Scott C. Lockard，丹尼尔.C.克莱顿，Daniel C. Clayton，史蒂文.J.詹茨，Steven J. Jantz，杰里米 L. 安特瑟赫，Jeremy L. Unterseher，迈克尔 E. 兰利，Michael E. Langley，亚伦 G. 麦克唐纳，Aaron G. McDonald，南希 S. 拉科雷，Nancy S. Lacore，迈克尔 W. 巴泽，Michael W. Baze，约翰尼斯 J. 冈萨雷斯，Johannes J. Gonzalez，小肯尼斯 F. 麦肯齐，Kenneth F. McKenzie, Jr，詹姆斯 马洛伊，James Malloy，帕特里克 D. 弗兰克，Patrick D. Frank，詹姆斯.赫尔德尔，James Herdel，柯特.A.伦肖，Curt A. Renshaw，罗德尼.P.德瓦尔特，Rodney P. DeWalt，迈克尔.T.斯宾塞，Michael T. Spencer，安德鲁.F.卡尔森，Andrew F. Carlson，富兰克林.P.卡尔，Franklin P. Call，梅丽莎.D.奥乔亚，Melissa D. Ochoa，卢卡斯.普伦，Lucas Pullen，约翰.G.斯托姆，John G. Storms，马修.C.克罗威尔，MATTHEW C. CROWELL，丹尼斯.W.菲西利耶，DENNIS W. FUSELIER，约瑟夫.D.昆克尔，JOSEPH D. KUNKEL，埃文.L.佩特斯，Evan L. Pettus，科里.J.克罗，COREY J. CROW，丹尼尔.H.图利，DANIEL H. TULLEY，J.克里斯托弗.麦克隆，J. CHRISTOPHER McCLUNG，达纳.L.卡帕尔迪，DANA L. CAPALDI，拉里.R.布罗德韦尔，LARRY R. BROADWELL，杰里米.R.里夫斯，JEREMY R. REEVES，约书亚.J.威纳，JOSHUA J. WIENER，亨利.H.特里佩特三世，HENRY H. TRIPLETT III，丹尼尔.J.科德斯，DANIEL J. CORDES，达娜.C.康斯尔，DANA C. COUNCIL，弗雷德里克“特雷”科尔曼，Frederick \"Trey\" Coleman，科里.克里斯托弗，Cory Christoffer，约翰.普罗内维奇，John Proniewicz，乔丹 格兰特，Jordan \"Gadget\" Grant，约翰.阿塔里.哈里斯，John “Atari” Harris，杰里米.斯图尔特，Jeremy Stewart，史蒂夫.科珀，Steve Koper，泰勒 斯塔克，Tyler \"Mask\" Stark，朱莉.A.克雷格，Julie A. Craig，格雷格.雷特勒，Greg Rettler，斯蒂芬.D.唐纳德，Stephen D. Donald，罗斯.迈尔斯，Ross Myers，迈克尔.J.史密斯，Michael J. Smith，斯科特.A.内格尔，Scott A. Nagle，马修.H.威尔士，Matthew H. Welsh，保罗.J.耶格，Paul J. Jaeger，安德鲁.G.理查森，Andrew G. Richardson，丹尼.L.诺尔斯，Danny L. Noles，托马斯.K.海登，Thomas K. Hayden，小肯尼思.M.布鲁斯，Kenneth  M. Bruce, JR，希瑟.A.福克斯，HEATHER A. FOX，斯宾塞.S.托马斯，SPENCER S. THOMAS，托里.M.琼斯，TORI M. JONES，托里.M.琼斯，Johnathan A. Eccles，杰弗里.S.福本，Geoffrey S. Fukumoto，斯科蒂.A.彭德利，SCOTTY A. PENDLEY，C.杰弗里“米克”卡梅隆，C. GEOFFREY “MICK” CAMERON，埃里克.O.韦尔科姆，ERICK O. WELCOME，安德鲁.M.鲍尔，ANDREW M. BAUER，加文.P.马克斯，GAVIN P. MARKS，乔恩.L.舒马特，JON L. SHUMATE，布莱恩.图曼，BRYAN TUMAN，杰弗里.A.菲利普斯，JEFFREY A. PHILLIPS，奥马尔.A.维拉斯科，Omar A. Velasco，特雷莎.A.哈克，Theresa A. Haak，罗伯特.L.霍普金斯，Robert L. Hopkins，卡梅隆.S.普林格尔，CAMERON S. PRINGLE，蒂莫西.J.柯里，TIMOTHY J. CURRY，瑞安.A.图恩斯，RYAN A. THUYNS，凯勒.史蒂文斯，KAYLE STEVENS，帕特里克.C.威廉姆斯，PATRICK C. WILLIAMS，史蒂文.E.维尔波斯，STEVEN E. VILPORS，克里斯托弗.T.芬尼格斯米尔，CHRISTOPHER T. FINNIGSMIER，小凯文.A.保罗，KEVIN A. PAUL JR.，史蒂文.J.安德森，Steven J. Anderson，杰弗里.A.布兰肯希普，Jefferey A. Blankenship，以色列.杰格，ISRAEL JAEGER，凯瑟琳.G.巴伯，Katharine G. Barber，拉尔夫.E.博尔德纳三世，Ralph E. Bordner III，艾米.朗，Amy Long，马修.E.琼斯，MATTHEW E. JONES，马修.H.斯沃茨，Matthew H. Swartz，胡本.L.菲利普斯，HUBEN L. PHILLIPS，杰森.T.雷诺兹，Jason T. Reynolds，约翰.C.鲍勒，John C. Bowler，迪恩.桑德伯格，Dean Sonnenberg，杰弗里.史密斯，Jeffrey Smith，罗伯特.E.佛罗伦萨，Robert E. Florentino，罗斯.德宁，Ross Drenning，约瑟夫.A.巴格特，JOSEPH A. BAGGETT，小威廉 W. 惠滕伯格，WILLIAM W. WHITTENBERGER JR.，提摩太 M. 阿普盖特，TIMOTHY M. APPLEGATE，莫林 G. 巴纳维奇，MAUREEN G. BANAVIGE，托尼 D. 鲍恩费恩德，TONY D. BAUERNFEIND，马修 J. 伯格，MATTHEW J. BURGER，斯科特 A. 该隐，SCOTT A. CAIN，凯斯 A. 坎宁安，CASE A. CUNNINGHAM，洛娜 B. 埃斯特普，LORNA B. ESTEP，小罗伯特 B. 福科斯，ROBERT B. FOOKES JR.，大卫 P. 加菲尔德，DAVID P. GARFIELD，亚历克斯 G. 格林威治，ALEXUS G. GRYNKEWICH，约翰 P. 希利，JOHN P. HEALY，詹姆斯 B. 赫克，JAMES B. HECKER，托马斯 K. 亨斯利，THOMAS K. HENSLEY，凯文 B. 肯尼迪，KEVIN B. KENNEDY，迈克尔 G. 科切斯基，MICHAEL G. KOSCHESKI，达林 D. 兰姆布里格，DARRIN D. LAMBRIGGER，约翰 D. 拉蒙塔涅，JOHN D. LAMONTAGNE，大卫 A. 米诺，DAVID A. MINEAU，史提芬 S. 诺德豪斯，STEVEN S. NORDHAUS，杰弗里 彭宁顿，JEFFREY PENNINGTON，罗纳德 R. 拉顿，RONALD R. RATTON，迈克尔 T. 罗尔斯，MICHAEL T. RAWLS，杜克 Z. 理查森，DUKE Z. RICHARDSON，小詹姆斯 R. 西尔斯，JAMES R. SEARS JR.，詹姆斯 C. 斯莱夫，JAMES C. \"JIM\" SLIFE，马克 V. 斯洛明斯基，MARK V. SLOMINSKI，丽贝卡 J. 桑基斯，REBECCA J. SONKISS，罗伯特 W. 范霍伊二世，ROBERT W. “ROB” VANHOY II，C. 麦考利 冯 霍夫曼，C. MCCAULEY VON HOFFMAN，凯西 L. 沃特恩，KATHY L. WATERN，约翰 阿达梅茨，John Adametz，詹姆斯 A. 艾肯，James A. Aiken，杰弗里 安德森，Jeffrey Anderson，托马斯 J. 安德森，Thomas J. Anderson，爱德华 安德森，Edward Anderson，布拉德利 安德罗斯，Bradley Andros，威廉 L. 安格曼，William L. \"Wilbur\" Angermann，约翰 阿奎利诺，John Aquilino，克里斯 托弗 阿塞尔塔，Christopher Asselta，斯图尔特 贝克，Stuart Baker，斯蒂芬 巴内特，Stephen Barnett，迈克尔 巴泽，Michael Baze，道格拉斯 比尔，Douglas Beal，迈克 伯纳基，Mike Bernacchi，马克 贝宁，Mark Behning，海蒂 伯格，Heidi Berg，尤金 布莱克，Eugene Black，肯尼斯 布莱克蒙，Kenneth Blackmon，贝特 玻利瓦尔，Bette Bolivar，罗纳德 博索尔，Ronald Boxall，迈克尔 波义耳，Michael Boyle，迈克尔 A. 布鲁克斯，Michael A. Brookes，理查德 布罗菲，Richard Brophy，查尔斯 布朗，Charles Brown，普特南 H. 布朗，Putnam H. Browne，苏珊 布莱尔乔伊纳，Susan BryerJoyner，安得烈 伯彻，Andrew Burcher，詹姆斯 巴特勒，James Butler，詹姆斯 拜纳姆，James Bynum，凯文 伯恩，Kevin Byrne，小威廉 伯恩，William Byrne Jr.，约瑟夫 卡希尔，Joseph Cahill，小詹姆斯 考德威尔，James Caldwell Jr.，安东尼 卡鲁洛，Anthony Carullo，小格拉夫顿 蔡斯，Grafton Chase Jr.，威廉 蔡斯三世，William Chase III，肖莎娜 查特菲尔德，Shoshana Chatfield，卡尔 P. 奇比，Carl P. Chebi，理查德 奇斯曼，Richard Cheeseman Jr.，丹尼尔 奇弗，Daniel Cheever，克雷格 克拉珀顿，Craig Clapperton，梅丽莎 科恩，Melissa Cohen，斯科特 康恩，Scott Conn，布莱克 康弗斯，Blake Converse，查尔斯 库珀二世，Charles Cooper II，布莱恩 科里，Brian Corey，理查德 科雷尔，Richard Correll，小达尔萨 克兰德尔，Darse Crandall Jr.，凯瑟琳 克雷顿，Kathleen Creighton，兰迪 克赖茨，Randy Crites，迈克尔 科伦，Michael Curran，杰弗里 泽鲁科，Jeffrey Czerewko，马克 道尔顿，Marc Dalton，伊维特 戴维斯，Yvette Davids，基思 戴维斯，Keith Davids，菲利普 戴维森，Philip Davidson，特伦特 德莫斯，Trent DeMoss，罗德尼 得沃特，Rodney DeWalt，小约瑟夫 迪瓜多，Joseph DiGuardo Jr.，威廉  狄龙，William  Dillon，迈克尔 唐纳利，Michael Donnelly，詹姆斯 唐尼，James Downey，汤姆 德鲁甘，Tom Druggan，肖恩 杜安，Shawn Duane，安 达夫，Ann Duff，迈克尔 杜蒙，Michael Dumont，保拉 邓恩，Paula Dunn，丹尼尔 德怀尔，Daniel Dwyer，特里 W. 埃丁格，Terry W. Eddinger，格雷戈里 K. 埃默里，Gregory K. Emery，克里斯 托弗 M. 恩达尔，Christopher M. Engdahl，肯尼斯 埃普斯，Kenneth Epps，克里斯汀 B. 法布里，Kristen B. Fabry，克雷格 福勒，Craig Faller，丹尼尔 菲永，Daniel Fillion，丽莎 弗兰凯蒂，Lisa Franchetti，里克 弗里德曼，Rick Freedman，克里斯 托弗 弗兰奇，Christopher French，罗纳德  弗里策梅尔，Ronald  Fritzemeier，斯科特 富勒，Scott Fuller，约翰 富勒，John Fuller，唐纳德 加布里尔森，Donald Gabrielson，谢恩 加哈根，Shane Gahagan，彼得 加文，Peter Garvin，罗伯特 戈谢，Robert Gaucher，詹姆斯 F. 格尔斯，James F. Geurts，迈克尔 吉尔迪，Michael Gilday，布鲁斯 吉林厄姆，Bruce Gillingham，大卫 戈金斯，David Goggins，克里斯 托弗 格雷迪，Christopher Grady，克里斯 托弗 格雷，Christopher Gray，凯文 格林，Kevin Green，科林 格林，Collin Green，威廉 格林，William Greene，约翰 甘布尔顿，John Gumbleton，詹姆斯 汉考克，James Hancock，约翰 汉尼克，John Hannink，托马斯 W. 哈克，Thomas W. Harker，格雷戈里 哈里斯，Gregory Harris，帕特里克 S. 海登，Patrick S. Hayden，R. 杜克 海因茨，R. Duke Heinz，托马斯 M. 亨德森切特，Thomas M. Henderschedt，乔恩 希尔，Jon Hill，迈克尔 霍兰德，Michael Holland，阿尔文 霍尔西，Alvin Holsey，尼古拉斯 霍曼，Nicholas Homan，威廉 休斯顿，William Houston，休 W. 霍华德三世，Hugh W. Howard III，格雷戈里 哈夫曼，Gregory Huffman，杰弗里 休斯，Jeffrey Hughes，布莱恩 赫尔利，Brian Hurley，杰弗里 贾布伦，Jeffrey Jablon，阿莱莱 詹金斯，Alaleh Jenkins，凯文 琼斯，Kevin Jones，基思 琼斯，Keith Jones，萨拉 乔伊纳，Sara Joyner，罗伊 凯利，Roy Kelley，凯瑟琳 凯斯米尔，Catherine Kessmeier，詹姆斯 基尔比，James Kilby，科林 基兰，Colin Kilrain，詹姆斯 柯克，James Kirk，斯蒂芬 T. 克勒，Stephen T. \"Web\" Koehler，约翰 科尔卡，John Korka，戴夫 克里特，Dave Kriete，提摩太 奎哈斯，Timothy Kuehhas，辛西娅 A. 库纳，Cynthia A. Kuehner，南希 拉科雷，Nancy Lacore，卡尔 拉赫蒂，Carl Lahti，艾琳 劳巴赫，Eileen Laubacher，西奥多 列克莱尔，Theodore LeClair，小菲利普 李，Phillip Lee Jr.，约翰 莱蒙，John Lemmon，安得烈 列侬，Andrew Lennon，凯文 P. 莱诺克斯，Kevin P. Lenox，威廉 莱舍尔，William Lescher，杨希 林赛，Yancy Lindsey，杰森 M. 劳埃德，Jason M. Lloyd，安得烈 卢瓦塞勒，Andrew Loiselle，弗雷德里克 卢奇曼，Fredrick Luchtman，丹尼尔 J. 麦克唐纳，Daniel J. MacDonnell，丹尼尔 麦克唐纳，Daniel MacDonnell，W. 马杰，W. Mager，大卫 马利龙，David Manero，霍华德 B. 马克，Howard B. Markle，托马斯 马洛塔，Thomas Marotta，丹尼尔 P. 马丁，Daniel P. Martin，加里 梅耶斯，Gary Mayes，卫斯理 R. 麦考尔，Wesley R. McCall，特洛伊 麦克莱兰，Troy McClelland，卢克 麦科勒姆，Luke McCollum，约翰 梅尔，John Meier，马克 梅尔森，Mark Melson，威廉 默兹，William Merz，迪伊 默伯恩，Dee Mewbourne，马克 米格斯，Marc Miguez，帕梅拉 米勒，Pamela Miller，德沃尔夫 米勒三世，DeWolfe Miller III，布莱恩 P. 莫纳汉，Brian P. Monahan，托马斯 莫罗，Thomas Moreau，马克 莫里茨，Mark Moritz，弗朗西斯 莫利，Francis Morley，凯西 莫顿，Casey Moton，马克 穆里斯基，Mark Mouriski，安得烈 米勒，Andrew Mueller，斯图尔特 芒施，Stuart Munsch，约翰 马斯汀，John Mustin，阮欢，Huan Nguyen，小约瑟夫 D. 诺布尔，Joseph D. \"Doug\" Noble Jr.，南希 诺顿，Nancy Norton，罗伯特 诺瓦科夫斯基，Robert Nowakowski，小约翰 诺威尔，John Nowell Jr.，卡塔尔 奥康纳，Cathal O'Connor，赛科 奥卡诺，Seiko Okano，马修 奥基夫，Matthew O'Keefe，约翰 奥肯，John Okon，马修 (马特) N. 奥特三世，Matthew (Matt) N. Ott III，斯科特 帕帕诺，Scott Pappano，史提夫 帕罗德，Steve Parode，布莱恩 佩查，Brian Pecha，道格拉斯  佩里，Douglas  Perry，G. 彼得 斯，G. Peters，埃里克 L. 彼得 森，Eric L. Peterson，詹姆斯 皮特斯，James Pitts，唐纳德 M. 普卢默，Donald M. Plummer，约翰 波洛茨克，John Polowczyk，基恩 普赖斯，Gene Price，塞德里克 普林格尔，Cedric Pringle，弗雷德 派尔，Fred Pyle，莱斯 里尔丹兹，Les Reardanz，柯特 伦肖，Curt Renshaw，加勒特 L. 雷辛，Garrett L. Ressing，艾伦 雷耶斯，Alan Reyes，查尔斯 理查德，Charles Richard，玛丽 里格斯，Mary Riggs，斯科特 罗伯逊，Scott Robertson，查尔斯 罗克，Charles Rock，理查德 罗德里格斯，Richard Rodriguez，弗雷德里克 罗格，Frederick Roegge，库尔特 罗森豪斯，Kurt Rothenhaus，埃里克 拉滕伯格，Eric Ruttenberg，罗伯特 桑德，Robert Sander，弥尔顿 桑茨三世，Milton Sands III，卡洛斯 萨尔迪耶洛，Carlos \"Los\" Sardiello，菲利普 索耶，Phillip Sawyer，托德 L. 谢弗，Todd L. Schafer，杰弗里 S. 舍伊特，Jeffrey S. Scheidt，保罗 施利斯，Paul Schlise，约翰 朔默，John Schommer，迈克尔 西瑞塔，Michael Sciretta，布伦特 斯科特，Brent Scott，理查德 赛夫，Richard Seif，洛林 塞尔比，Lorin Selby，盖尔 沙弗，Gayle Shaffer，罗伯特 夏普，Robert Sharp，迈克 沙丁斯基，Mike Shatynski，约翰 B. 希尔曼，John B. \"Brad\" Skillman，米歇尔 斯库比克，Michelle Skubic，格雷戈里 斯拉夫，Gregory Slavonic，拉塞尔 史密斯，Russell Smith，拉塞尔 L. 史密斯，Russell L. Smith，理查德 斯奈德，Richard Snyder，小保罗 斯佩德罗，Paul Spedero Jr.，约翰 斯宾塞，John Spencer，彼得 斯塔马托普洛斯，Peter Stamatopoulos，弗雷德里克 J. (杰伊) 斯蒂芬妮，Frederick J. (Jay) Stefany，迈克尔 斯特芬，Michael Steffen，安妮 M.斯沃普，Anne M.Swap，克里斯 托弗 斯威尼，Christopher Sweeney，唐纳德 Y. 斯，Donald Y. Sze，提姆 希曼斯基，Tim Szymanski，约翰 塔门，John Tammen，斯蒂芬 特德福德，Stephen Tedford，卡尔 托马斯，Karl Thomas，德里克 A.的黎克，Derek A.Trinque，格雷戈里 托德，Gregory Todd，路易斯 的黎波里，Louis Tripoli，杰弗里 特拉斯勒，Jeffrey Trussler，默里 丁科三世，Murray Tynch III，迪恩 范德利，Dean VanderLey，丹尼斯 韦雷斯，Dennis Velez，埃里克 维尔 黑格，Eric Ver Hage，达林 维亚，Darin Via，约翰 韦德，John Wade，达里尔 沃克，Darryl Walker，托马斯 沃尔，Thomas Wall，弥尔顿 华盛顿，Milton Washington，詹姆斯 沃特斯三世，James Waters III，拉里 沃特金斯，Larry Watkins，约翰 沃特金斯，John Watkins，提摩太 韦伯，Timothy Weber，罗伯特 韦斯滕多夫，Robert Westendorff，迈克尔 维特劳弗，Michael Wettlaufer，威廉 W. 惠勒三世，William W. \"Trey\" Wheeler III，弗兰克 惠特沃思，Frank Whitworth，乔治 维科夫，George Wikoff，杰罗米 B. 威廉姆斯，Jeromy B. Williams，汤姆 威廉姆斯，Tom Williams，小查尔斯 威廉姆斯，Charles Williams Jr.，瑞奇 威廉森，Ricky Williamson，小约翰尼 沃尔夫，Johnny Wolfe Jr.，菲利普 W. 于，Philip W. Yu，迈克尔 扎尔科夫斯基，Michael Zarkowski，威廉 K. 莱舍尔，William K. Lescher，詹姆斯 克里斯蒂，James Christie，比蒂，Beatty，埃里克 伊尔斯顿，Eric Illston，罗伯特 梅杰瑞斯，Robert Majoris，赖安 柯林斯，Ryan Collins，特洛伊 M. 纽森，Troy M. Newsom M. Newsom，克里斯托弗 席尔瓦，Cristopher Silva，克雷格 瓦夫鲁斯卡，Kraig Vavruska，加列戈斯，Gallegos，克里斯 托弗 齐格勒，Christopher Zeigler，海尔曼，Heilman，维罗尼卡 C. 霍利戴，Veronica C. Holliday，比尔 约翰逊，Bill Johnson，乔纳森 隆，Jonathan Long，杰森 R. 奥尔特加，Jason R. Ortega，詹姆斯 W. 奥斯博恩，James W. Osborne，奎因，Quin，佩德罗 桑托斯，Pedro \"Pete\" Santos，索贝克，Sobeck，杰森 L. 图姆林森，Jason L. Tumlinson，瓦康，Vacant，克雷格 A. 克拉珀顿，CRAIG A. \"CLAP\" CLAPPERTON，马吕斯 斯托克斯，MARLUIS STOKES，达里尔 廓德，DARYL CAUDLE，罗伯特 R. 巴尔马塞达，ROBERT R. BALMACEDA，尼克尔 C. 里奥斯，Nicole C. Rios，兰德尔 W. 佩克，RANDALL W. PECK，乔尔 G. 斯图尔特，JOEL G. STEWART，小霍华德 T. 帕克，Howard T. Parker, Jr.，约瑟夫 门德斯，JOSEPH MENDEZ，罗伯特 J. 哈利特，Robert J. Hallett，克里斯托弗 R. 德纳姆，CHRISTOPHER R. DENHAM，罗德尼 E. 内文格，RODNEY E. NEVINGER，托德 M. 科尔威尔，TODD M. COLWELL，格伦 R. 雷，GLENN R. RAY，迈克尔 E. 麦克威廉姆斯，Michael E. McWilliams，格雷格 T. 哈贝尔，Gregg T. Habel，唐纳德 E. 普莱特，DONALD E. PLATER，肖恩 M. 艾罗，SHAWN M. AIELLO，托马斯 A. 布西埃尔，Thomas A. Bussiere，约翰 J. 佩里曼，John J. Perryman，托马斯 E. 伊舍，Thomas E. Ishee，速水孝明，，梅尔维纳 A. 史密斯，MELVINA A. SMITH，布莱恩 S. 罗宾逊，BRIAN S. ROBINSON，查梅因 N 凯利，CHARMAINE N KELLEY，小肯尼斯 T. 比布，Kenneth T. Bibb, Jr.，耶利米 “苏格” 希斯曼，Jeremiah “Scot” Heathman，查德 W. 比克利，Chad W. Bickley，丹尼尔 A. 德沃，Daniel A. DeVoe，马克 D. 卡默勒，Mark D. Camerer，克雷格 M. 哈蒙，Craig M. Harmon，安东尼 W. 格林，Anthony W. Green，约翰 布拉巴松，John Brabazon，德里克 金斯利，Derrick Kingsley，潘詹，Giao Phan，弥尔顿 W. 特洛伊三世，Milton W. Troy, III，贾斯廷 格雷，Justin Gray，雅各布 N. 斯托瓦尔，Jacob N. Stovall，托马斯 斯宾塞，Thomas Spencer，约翰 W.R. 蒲伯三世，John W.R. Pope III，库尔特 J. 温德尔肯，Kurt J. Wendelken，肯尼斯 W. 埃普斯，Kenneth W. Epps，约翰 D. 索拉科，John D. Soracco，詹姆斯 H. 施特劳斯，James H. Strauss，珍妮弗 拉托尔，Jennifer LaTorre，辛蒂 雷达尔，Cindy Readal，玛丽亚 L. 阿加约，Maria L. Aguayo，戈登 E. 米克三世，Gordon E. Meek III，彼得 A. 里迪拉，Peter A. Ridilla，迈克 吉尔迪，Mike Gilday，彼得 E. 托雷斯，Peter E. Torres，理查德 E. 博尔顿，Richard E. Bolton，詹姆斯 贝茨，James Bates，詹姆斯 P. 麦克唐纳，James P. McDonough，罗伯特 W. 马修森，Robert W. Mathewson，伯纳德 菲格罗亚，Bernard Figueroa，卡里姆 科尔，Karim Cole，彼得 A. 加文，PETER A. GARVIN，斯科特 W. 拉斯顿，Scott W. Ruston，基思 L. 贝克，Keith L. Beck，约翰 R. 琼斯，John R. Jones，马修 哈里斯，Matthew Harris，珍妮弗 S. 科图雷，Jennifer S. Couture，瑞克 门格尔，Rick Mengel，大卫 L. 科贝尔，David L. Cobbel，亚伦 李，Aaron Lee，库尔特 科普利，Curt Copley，小肯尼斯 M. 科廷，Kenneth M. Curtin Jr.，雷金纳德 普雷斯顿，Reginald Preston，凯伦 沃兰德，Kellen Voland，罗宾 H. 洛克西雷，Robin H. Locksley，格雷格 埃默里，Greg Emery，特雷西 L. 亨特，Tracy L. Hunt，托马斯 E. 林茨，Thomas E. Lintz，爱德华 L. 卡拉汉，Edward L. Callahan，马克 R. 施洛瑟，MARK R. SCHLOSSER，马克 索哈尼，Mark Sohaney，杰西 赫斯，Jessee Hess，克里斯托弗 G. 博纳，Christopher G. Bohner，科里 D. 赫德，Corey D. Hurd，约瑟夫 A. 瓦茨，Joseph A. Watts，威廉 A. 帕金斯，William A. Perkins，卡尔 S. 利普塔克，Carl S. Liptak，道格拉斯 M. 彼得森，Douglas M. Peterson，克里斯托弗 费希尔，Christopher Fisher，罗伯特 D. 比奇，Robert D. Beachy，杰里米 ‘JV’ 沃恩，Jeremy ‘JV’ Vaughan，杰森 西蒙，Jason Simon，杰森 M. 查德顿，Jason M. Chadderton，杰森 威廉姆斯，Jasen Williams，特德 卡尔森，TED CARLSON，格伦 R. 托德，GLENN R. TODD，拉蒂夫 康普顿，LATEEF COMPTON，罗伯特 “巴尔” 金纳赫三世，Robert “Barr” Kimnach III，柯克 A. 拉格奎斯特，Kirk A. Lagerquist，安德烈 D. 布朗，Andre D. Brown，迈克尔 R. 史泰博，Michael R. Staples，比利 史密斯，Billy Smith，弗兰克 肯德尔，Frank Kendall，大卫 W. 韦德，David W. Wade，大卫 S. 查斯，DAVID S. CHACE，科伊特 D. 哈格斯，Coyt D. Hargus，蒂亚 亨德森，TIAA HENDERSON，劳伦斯 M. 埃弗贝克，LAWRENCE M. AVERBECK，凯瑟琳 M. 戴，Kathleen M. Day，詹姆斯 A. 哈默尔，JAMES A. HAMEL，克里斯托弗 A. 布朗，CHRISTOPHER A. BROWN，约翰 J. 希茨，JOHN J. SHEETS，安东尼 A 克里格，ANTHONY A KLEIGER，迈克尔 D. 杜格雷，MICHAEL D. DUGRE，贝雷克特 “巴里” 坦茹，BEREKET “BARRY” TANJU，埃米利奥 埃尔南德斯，Emilio Hernandez，小劳尔 比利亚雷亚尔，Raul Villarreal, Jr，凯文 M. 杰米森，KEVIN M. JAMIESON，托德 R. 戴尔，TODD R. DYER，约书亚 D. 道姆特斯，JOSHUA D. DEMOTTS，亚历克斯 '三分球' 摩根三世，ALEX 'TREY' MORGAN III，卡梅隆 戴德佳，CAMERON DADGAR，肖恩 B. 奈茨克，Sean B. Neitzke，杰弗里 D. 马丁，JEFFERY D. MARTIN，小威廉 杨，William Young, Jr.，埃里克 保尔森，Eric Paulson，大卫 索瑟尔二世，David Southall II，弗雷德里克 '三分球' 科尔曼三世，Fredrick 'Trey' Coleman III，卡尔 R.  温布莱特，Karl R.  Weinbrecht，科内利斯 'CT' 汤普森，Cornelious 'CT' Thompson，大卫 米诺，David Mineau，索尼亚 李，Sonia Lee，埃里克 C. 施密特，ERIC C. SCHMIDT，杰森 B. 贝尔，JASON B. BELL，阿德里安 R. 沃伦，ADRIENNE R. WARREN，约瑟夫 C. 特纳姆，Joseph C. Turnham，保罗 E. 希茨，Paul E. Sheets，克雷格 R. 安德莱，CRAIG R. ANDRLE，迈克尔 L. 盖特，MICHAEL L. GETTE，凯伦 E. 克洛伊德，KAREN E. CLOYD，威廉 J. 克里登，WILLIAM J. CREEDEN，理查德 B. 福斯特，Richard B. Foster，克里斯托弗 M. 格拉德尔，Christopher M. Gradel，格雷戈里 S. 博列伊，GREGORY S. BEAULIEU，哈里 D. 亨，Harry D. Hung，拉塞尔 P. 库克，RUSSELL P. COOK，詹姆斯 M. 威尔丰，JAMES M. WILFONG，丹妮尔 L. 威利斯，DANIELLE L. WILLIS，肖恩 A. 安德鲁斯，SHAWN A. ANDREWS，埃内斯托 M. 迪维托瑞，ERNESTO M. DIVITTORIO，卢卡斯 J. 蒂尔，LUCAS J. TEEL，约书亚 R. 蒂德韦尔，Joshua R. Tidwell，贾马尔 E. 梅斯，JAMAAL E. MAYS，杰弗里 A. 伯代特，JEFFREY A. BURDETTE，埃里克 L. 菲利普斯，ERIC L. PHILLIPS，米歇尔 C. 卡恩斯，MICHELLE C. CARNS，卡莱布 瓦登，CALEB VADEN，库尔特 C. 赫芬斯汀，KURT C. HELPHINSTINE，埃里克 M. 韦斯特，ERIC M. WEST，史蒂芬 L. 拉尼尔，STEPHEN L. LANIER，罗伯特 N. 奥斯本，ROBERT N. OSBORN，大卫 J. 梅尔比，DAVID J. MELBY，劳伦斯 T. 沙利文，LAWRENCE T. SULLIVAN，凯文 D. 弗莱希科克，KEVIN D. HICOK，格雷戈里 M. 莫斯利，GREGORY M. MOSELEY，克里斯托弗 W. 彼得斯，CHRISTOPHER W. PETERS，凯瑟琳 A. 格雷厄姆，KATHERINE A. GRABHAM，凯文 P. 科伊尔，KEVEN P. COYLE，韦恩 M. 弗罗斯特，WAYNE M. FROST，米歇尔 T. 布朗宁，MICHELLE T. BROWNING，马歇尔 B. _布拉德_ 韦伯.韦伯，MARSHALL B. _BRAD_ WEBB.webp，埃里克 汤普森，Erik Thompson，杰弗里 S. ''杰夫'' 辛里奇，JEFFREY S. ''JEFF'' HINRICHS，Benjamin T. Watson，Benjamin T. Watson，希瑟 L. 普林格尔，HEATHER L. PRINGLE，PAUL E. HENDERSON，PAUL E. HENDERSON，TIMOTHY SAKULICH，TIMOTHY SAKULICH，TIMOTHY J. BUNNING，TIMOTHY J. BUNNING，GEORGE M. DOUGHERTY，GEORGE M. DOUGHERTY，JAMES E. FITCH II，JAMES E. FITCH II，NATHAN P. DILLER，NATHAN P. DILLER，RIC J. FELT，RIC J. FELT，FRED E. GARCIA II，FRED E. GARCIA II，AMANDA GENTRY，AMANDA GENTRY，CHARLES Q. BROWN, JR，CHARLES Q. BROWN, JR，TERRENCE A. ADAMS，TERRENCE A. ADAMS，JAMES C. DAWKINS JR.，JAMES C. DAWKINS JR.，道格拉斯 斯莫尔，Douglas Small，Thormod Forseth，Thormod Forseth，威廉 加利尼斯，William Galinis，肖恩 巴克，Sean Buck，David S. Forman，David S. Forman，RICHARD M. CLARK，RICHARD M. CLARK，OTIS C. JONES，OTIS C. JONES，PAUL D. MOGA，PAUL D. MOGA，SARAH BALIAN，SARAH BALIAN，GAIL COLVIN，GAIL COLVIN，SCOTT E. WILLIAMS，SCOTT E. WILLIAMS，CHRISTOPHER J. LEONARD，CHRISTOPHER J. LEONARD，MICHAEL R. STOLLEY，MICHAEL R. STOLLEY，NATHAN PINE，NATHAN PINE，Abel Griego，Abel Griego，AARON GUILL，AARON GUILL，亚当·J·维兹，Adam J. Viz，Adam M. Gharati，Adam M. Gharati，Adam Vizi，Adam Vizi，艾伯特·L·克纳普四世，ALBERT L. KNAPP IV，Alex C. Morgan III，Alex C. Morgan III，Alice A. Clark，Alice A. Clark，Allen E. Duckworth，Allen E. Duckworth，Alonza J Ross，Alonza J Ross，Amanda G. Kato，Amanda G. Kato，AMY D. HOLBECK，AMY D. HOLBECK，André McMillian，André McMillian，安德鲁·N·科里，Andrew N. Corey，Andrew Stephan，Andrew Stephan，ANTHONY J. FRANKS，ANTHONY J. FRANKS，Anthony L. Blum，Anthony L. Blum，安东尼·P·“托尼”·安杰洛，Anthony P. “Tony” Angello，ANTONIO J. GOLDSTROM，ANTONIO J. GOLDSTROM，ARTHUR F. HUBER II，ARTHUR F. HUBER II，小阿瑟·M·阿雷巴洛，Arthur M. Arebalo Jr.，Barry Dickey，Barry Dickey，巴里·E·利特尔，Barry E. Little，Becky Beers，Becky Beers，BENJAMIN M. CASON，BENJAMIN M. CASON，本杰明·W·海顿，Benjamin W. Hedden，贝特西·J·罗斯，BETSY J. ROSS，Betty A. Venth，Betty A. Venth，Bill Smalts，Bill Smalts，BOHDAN PYWOWARCZUK II，BOHDAN PYWOWARCZUK II，BOSTON A. ALEXANDER，BOSTON A. ALEXANDER，布拉德福德·R·埃弗曼，Bradford R. Everman，布拉德利·约翰逊，BRADLEY L. JOHNSON，布拉德利·M·马格拉思，Bradley M. Magrath，Bradley R. Stevens，Bradley R. Stevens，Bret T. Copple，Bret T. Copple，布赖恩·罗豪尔，BRIAN C ROHAUER，Brian E. Vaughn，Brian E. Vaughn，BRIAN E. WISH，BRIAN E. WISH，Brian J. Budde，Brian J. Budde，布赖恩·J·丹尼，BRIAN J. DENNY，布莱恩·J·麦克多纳，Brian J. McDonough，布莱恩·M·斯塔姆普，Brian M. Stumpe，Brian P. Fort，Brian P. Fort，Brian Pummill，Brian Pummill，布莱恩·R·蒙哥马利，Brian R. Montgomery，Brian R. Moore，Brian R. Moore，BRIAN R. STAFFORD，BRIAN R. STAFFORD，布莱恩·赖德奥特，Brian Rideout，Brian Thompson，Brian Thompson，Brian Vance，Brian Vance，布里奇特·W·吉格里奥蒂，Bridget V. Gigliotti，Brigadier General Jason L. Morris，Brigadier General Jason L. Morris，布莱恩·P·夏洛克，BRYAN P. SHERLOCK，布莱恩·T·卡拉汉，BRYAN T. CALLAHAN，卡尔文·鲍威尔，CALVIN B. POWELL，卡梅伦 C. 戴维斯，CAMERON C. DAVIS，CARL J. MAGNUSSON，CARL J. MAGNUSSON，CARL W. DANE，CARL W. DANE，Carlos E. Labrador，Carlos E. Labrador，Casey Burril，Casey Burril，凯西·P·多兹，Casey P. Dodds，CATHERINE E. BUCHANAN，CATHERINE E. BUCHANAN，凯瑟琳·洛根，Catherine Logan，Catherine M. Logan，Catherine M. Logan，塞萨尔·弗洛雷斯，Cesar Flores，CHAD R. ELLSWORTH，CHAD R. ELLSWORTH，CHAD R. W. BIEHL，CHAD R. W. BIEHL，Charles A. DeCesari，Charles A. DeCesari，CHARLES B. CAIN，CHARLES B. CAIN，CHARLES D. BARKHURST，CHARLES D. BARKHURST，Charles D. Bush，Charles D. Bush，Charles E.  Ched Beam，Charles E.  Ched Beam，Charles E. Buchanan，Charles E. Buchanan，Charles E. Dudik，Charles E. Dudik，查尔斯·F·其沃格尔，Charles F. Ziervogel，CHARLES J. METZGAR，CHARLES J. METZGAR，Charles Smith，Charles Smith，Charles “Chuck” W. Frizzell，Charles “Chuck” W. Frizzell，CHASIDY D. SELLS，CHASIDY D. SELLS，Chester T. Parks，Chester T. Parks，秦·M·考克斯，Chin M. Cox，Chris B. Hammond，Chris B. Hammond，Chris Dunlap，Chris Dunlap，Chris E. Leak，Chris E. Leak，Chris Kotz，Chris Kotz，CHRISTIAN BIANCUR，CHRISTIAN BIANCUR，CHRISTOPHER B. WILLIAMS，CHRISTOPHER B. WILLIAMS，CHRISTOPHER C. VANNATTA，CHRISTOPHER C. VANNATTA，CHRISTOPHER D. GLAVIANO，CHRISTOPHER D. GLAVIANO，克里斯托弗·D·托马斯，Christopher D. Thomas，CHRISTOPHER D. WEBB，CHRISTOPHER D. WEBB，威廉·T·麦克尔欣尼，Christopher D. Witter，克里斯托弗·G·霍恩，Christopher G. Hawn，Christopher Hall，Christopher Hall，Christopher J. Germann，Christopher J. Germann，CHRISTOPHER K. LACOUTURE，CHRISTOPHER K. LACOUTURE，克里斯托弗·M·克拉克，Christopher M. Clark，克里斯托弗·M·齐德克，Christopher M. Zidek，克里斯托弗·P·奥康纳，Christopher P. O'Connor，Christopher Peter，Christopher Peter，克里斯托弗·帕里什，CHRISTOPHER R. PARRISH，CHRISTOPHER S. BLUTO, JR.，CHRISTOPHER S. BLUTO, JR.，CHRISTOPHER S. WELCH，CHRISTOPHER S. WELCH，Christopher T. Fisher，Christopher T. Fisher，克里斯托弗·T·莱，Christopher T. Lay，克里斯托弗·T·雷蒙德，Christopher T. Raymond，Christy L. Peterson，Christy L. Peterson，CLARENCE C. HUCKS JR.，CLARENCE C. HUCKS JR.，克拉伦斯哈克斯，CLARENCE HUCKS，Clinton A. Ross，Clinton A. Ross，克林顿 卡什，Clinton L. Cash，COL. TEAGUE A. PASTEL，COL. TEAGUE A. PASTEL，Collin Baulch，Collin Baulch，科林·J·鲍尔奇，Collin J. Baulch，Collin Wynter，Collin Wynter，Colonel Charles B. Dockery，Colonel Charles B. Dockery，Colonel Curtis V. Ebitz, Jr.，Colonel Curtis V. Ebitz, Jr.，Colonel David A. Suggs，Colonel David A. Suggs，Colonel Edward R. Sullivan，Colonel Edward R. Sullivan，Colonel Gregory Pace，Colonel Gregory Pace，Colonel Richard T. Anderson，Colonel Richard T. Anderson，指挥官海尔曼，Commander Heilman，Courtney Freeman，Courtney Freeman，克雷格·D·普拉瑟，Craig D. Prather，Craig Drescher，Craig Drescher，Craig McPike，Craig McPike，CURTIS R. BASS，CURTIS R. BASS，Cynthia A. Abbott，Cynthia A. Abbott，辛西娅·维拉，Cynthia Villa，\"戴尔 B. 斯金纳，DALE B. SKINNER，\"，Dan Osburn，Dan Osburn，戴恩·克劳福德，Dane Crawford，Daniel Irwin，Daniel Irwin，Daniel J. West，Daniel J. West，Daniel R. McDonough，Daniel R. McDonough，Daniel Weimer，Daniel Weimer，DANYELL C. STOUTAMIRE，DANYELL C. STOUTAMIRE，DARIN S. LACOUR，DARIN S. LACOUR，Darrell Phillipson，Darrell Phillipson，DARREN R. BORUFF，DARREN R. BORUFF，大卫·B·约翰逊，David B. Johnson，大卫·巴塞尔，David Bussel，大卫·C·斯纳尔，David C. Snarr，大卫 柯夫曼，David Curfman，大卫·H·丹蒂诺，David H. Dentino,，David J. Ross，David J. Ross，DAVID J. WILSON，DAVID J. WILSON，David M. Castaneda，David M. Castaneda，大卫·R·克利夫顿，DAVID R. CLIFTON，DAVID S. MILLER，DAVID S. MILLER，大卫·史密斯，David Smith，David “Bo” Rice，David “Bo” Rice，道恩·德金斯，Dawne Deskins，迪恩。马丁，DEAN B. MARTIN，迪安·D·斯尼戈夫斯基，Dean D. Sniegowski，迪恩•彼得斯，Dean Peters，DENISE J. EDWARDS，DENISE J. EDWARDS，Denisha Ward，Denisha Ward，Derek B. Routt，Derek B. Routt，Derrick K. Cote，Derrick K. Cote，Diana M. Brown，Diana M. Brown，DONALD E. KIRKLAND，DONALD E. KIRKLAND，DONALD S. PETERS，DONALD S. PETERS，\"，道格拉斯·斯托弗，DOUGLAS A. STOUFFER\"，Douglas D. DeMaio，Douglas D. DeMaio，道格拉斯·R·布里安，Douglas R. Burian，Douglas R. Turner，Douglas R. Turner，Douglas Stouffer，Douglas Stouffer，DUKE A. PIRAK，DUKE A. PIRAK，艾德·钱德勒，Ed Chandler，Edward A. Ramirez，Edward A. Ramirez，Edward G. Goebel，Edward G. Goebel，Edward J. Irick III，Edward J. Irick III，Edward M. Vedder，Edward M. Vedder，ELISE REDZINIAK，ELISE REDZINIAK，ERIC A. CARNEY，ERIC A. CARNEY，埃里克·M·史密斯，ERIC M. SMITH，Erica K. Rabe，Erica K. Rabe，Erica K. Rhea，Erica K. Rhea，Erica Rabe，Erica Rabe，Ericka Farmer-Hill，Ericka Farmer-Hill，ERICKA S. FARMER-HILL，ERICKA S. FARMER-HILL，ERIES L. G. MENTZER，ERIES L. G. MENTZER，埃里克·C·汤普森，Erik C. Thompson，ERIK G. BRINE，ERIK G. BRINE，埃里克·L·奥德海德，ERIK L. AUFDERHEIDE，Ezekiel A. Ross，Ezekiel A. Ross，弗兰克·肯德尔三世，Frank Kendall III，FRANK R. KINCAID，FRANK R. KINCAID，FRANK R. VERDUGO，FRANK R. VERDUGO，Frederick E. Kuehn，Frederick E. Kuehn，Frederick Lance Lewis,Jr，Frederick Lance Lewis,Jr，G. HALL SEBREN, JR.，G. HALL SEBREN, JR.，加里·R·牛顿，Garry R. Newton，GAVIN D. TADE，GAVIN D. TADE，基因雅各布，GENE JACOBUS，约书亚 M.奥尔森，General Joshua M. Olson，乔治·马丁·雷诺兹，George Martin Reynolds，乔治 N.“内特”沃格尔，George N. \"Nate\" Vogel，GERALD A. DONOHUE，GERALD A. DONOHUE，吉娜·奥尔蒂斯·琼斯，Gina Ortiz Jones，查德·比尔，GLENN COLLINS，Greg G. Peterson，Greg G. Peterson，Greg Leingang，Greg Leingang，Greg Semmel，Greg Semmel，格雷格·T·阿贝尔，Gregg T. Habel.jpg，GREGORY A. GWYN II，GREGORY A. GWYN II，GREGORY A. LEINGANG，GREGORY A. LEINGANG，GREGORY B. BERRY，GREGORY B. BERRY，格雷戈里·D·布坎南，GREGORY D. BUCHANAN，Gregory J. Slavonic，Gregory J. Slavonic，Gregory P. Haynes，Gregory P. Haynes，Heath Tempel，Heath Tempel，HEATHER RICHINS，HEATHER RICHINS，Heather W. Blackwell，Heather W. Blackwell，Henry Dolberry Jr.，Henry Dolberry Jr.，Henry R. Jeffress，Henry R. Jeffress，赫里伯托·迪亚斯，Heriberto Diaz，Hussain Al Zadjali，Hussain Al Zadjali，Ian J.M. Gillis，Ian J.M. Gillis，ISRAEL NUNEZ，ISRAEL NUNEZ，Israel Rivera，Israel Rivera，Jack L. Smock，Jack L. Smock，杰奎琳·L·马蒂，JACQUELYN L. MARTY，詹姆斯·C·斯利夫，James C. “Jim” Slife，JAMES E. SMITH，JAMES E. SMITH，JAMES FINLAYSON，JAMES FINLAYSON，詹姆斯·J·伯迈斯特，JAMES J. BURMEISTER，JAMES J. MATTEY，JAMES J. MATTEY，James Kafer，James Kafer，JAMES L. HERRICK，JAMES L. HERRICK，James Lyda，James Lyda，James R. Kafer，James R. Kafer，詹姆斯·S·伯内特，JAMES S. BURNETT，Jamesha M. Barnes，Jamesha M. Barnes，JANETTE L. THODE，JANETTE L. THODE，Jasen L. Hoffman，Jasen L. Hoffman，Jason A. Davey，Jason A. Davey，杰森 B 哈莫克，JASON B. HAMMOCK，杰森·B·瓦格纳，JASON B. WAGNER，贾森K.费蒂格，Jason K. Fettig，JASON L. KNIGHT，JASON L. KNIGHT，JASON L. SMITH，JASON L. SMITH，杰森·帕维尔夏克，Jason P. Pavelschak，杰森 Q. 博姆，JASON Q. BOHM，Jason Q. Shaffer，Jason Q. Shaffer，Jason R. DeLucy，Jason R. DeLucy，Jason Scott，Jason Scott，杰伊·A·约翰逊，Jay A. Johnson，Jeanne I. Hardrath，Jeanne I. Hardrath，Jeff Chism，Jeff Chism，杰弗里·摩根，JEFFERY M. MORGAN，杰弗里·A·范·杜廷，Jeffrey A. Van Dootingh，杰弗里·B·爱德华兹，Jeffrey B. Edwards，杰弗里·H·福尔摩斯，JEFFREY H HOLMES，Jeffrey H. Welborn，Jeffrey H. Welborn，杰弗里·L·巴特勒，JEFFREY L. BUTLER，Jeffrey T. Schreiner，Jeffrey T. Schreiner，Jenise M. Carroll，Jenise M. Carroll，珍妮丝·卡罗尔，Jenise M. Carroll ，珍妮·R·约翰逊，JENNIE R. JOHNSON，詹妮弗·A·费德勒，Jennifer A. Fiederer，詹妮弗·J·富勒，JENNIFER J. FULLER，杰里米·M·马尔科姆，Jeremy M. Malcom，JEREMY N. MALCOM，JEREMY N. MALCOM，吉姆·马蒂，Jim Mattey，乔安妮 M. 迪米特里奥，JO ANNE M. DIMITRIOU，Joey B. Dodgen，Joey B. Dodgen，JOHN A. BOCCIERI，JOHN A. BOCCIERI，John A. Cluck，John A. Cluck，John Breazeale，John Breazeale，JOHN D. MCKAYE，JOHN D. MCKAYE，John De Pree，John De Pree，约翰·E·特里昂，JOHN E. TRYON，John F. Robinson，John F. Robinson，约翰·冈萨雷斯，John Gonzales，小约翰·J·艾伦，JOHN J. ALLEN, JR.，JOHN M. SCHUTTE，JOHN M. SCHUTTE，约翰·P·奇尔科特，John P. Chilcote，约翰·R·安德鲁斯，JOHN R. ANDRUS，John W. Pogorek，John W. Pogorek，小乔纳森·S·格拉森，Jonathan S. Gration Jr，Jonathan S. Gration Jr.，Jonathan S. Gration Jr.，约瑟夫·米勒，JOSEPH C. MILLER，Joseph D. Janik，Joseph D. Janik，约瑟夫·F·施莱德，JOSEPH F. SHRADER，约瑟夫 M. 里维特，JOSEPH M. REVIT，Joseph Miller，Joseph Miller，JOSEPH R. AUGUSTINE，JOSEPH R. AUGUSTINE，约瑟夫·T·马西内克，JOSEPH T. MARCINEK，乔希·科斯洛夫，Josh Koslov，Joshua Cox，Joshua Cox，Justin B. Spears，Justin B. Spears，贾斯汀·康菲尔德，Justin Canfield，\"Justin CanfieldJustin Canfield，，Justin CanfieldJustin Canfield，\"，Justin G. Apticar，Justin G. Apticar，JUSTIN R. HOFFMAN，JUSTIN R. HOFFMAN，KAHN W. SCALISE，KAHN W. SCALISE，Kandace M. Steinbrink，Kandace M. Steinbrink，凯瑟琳·M·迈克尔，Kathleen M. McCool，Katrina C. Stephens，Katrina C. Stephens，KEITH M. ROESSIG，KEITH M. ROESSIG，Keith Matthew Henry，Keith Matthew Henry，KEITH WHITEHOUSE，KEITH WHITEHOUSE，小凯斯威尔克森，Keith Wilkerson Jr，KENNETH J. OSTRAT，KENNETH J. OSTRAT，小肯尼斯·M·布鲁斯，Kenneth M. Bruce, Jr.，KENNETH P. WOODCOCK，KENNETH P. WOODCOCK，小肯尼斯·T·比布，Kenneth T. Bibb，肯农·d·阿诺德，Kennon D. Arnold，KEVIN C. MARTIN，KEVIN C. MARTIN，KEVIN J. ROETHE，KEVIN J. ROETHE，凯文·R·曼托瓦尼，KEVIN R. MANTOVANI，KIMBERLY L. LORD，KIMBERLY L. LORD，KIRK W. PETERSON，KIRK W. PETERSON，科里·阿蒙森，Korey Amundson，Kris E. Barcomb，Kris E. Barcomb，Kristen Beals，Kristen Beals，克里斯蒂娜·L·罗杰斯，Kristina L. Rogers，库尔特·A·马修斯，Kurt A. Matthews，KYLEE ENGEL，KYLEE ENGEL，LARA B. MORRISON，LARA B. MORRISON，拉里 杜尚，Larry Douchand，拉里·K·克拉克，Larry K Clark，Laurie Dickson，Laurie Dickson，LAURIE M. FARRIS，LAURIE M. FARRIS，LAWRENCE D. PEAVLER，LAWRENCE D. PEAVLER，LEE E. HOOVER JR.，LEE E. HOOVER JR.，李 E. 默克尔，LEE E. MERKLE，伦·安德森，Len Anderson，LEO J. KAMPHAUS, JR.，LEO J. KAMPHAUS, JR.，利昂·O·卡罗威，Leon O. Calloway，伦纳德·维尔纳，三世，Leonard Werner, III，莱斯利·豪克，Leslie Hauck，LESLIE S. HADLEY，LESLIE S. HADLEY，莱斯特格柏“雷”，Lester \"Ray\" Gerber，Lieutenant Colonel Andrew J. “Hobbit” Norris，Lieutenant Colonel Andrew J. “Hobbit” Norris，Lieutenant Colonel John E. Bilas，Lieutenant Colonel John E. Bilas，Lieutenant Colonel Matthew P. Cook，Lieutenant Colonel Matthew P. Cook，LILLIAN R. PRINCE，LILLIAN R. PRINCE，琳赛·德罗兹，LINDSAY C. DROZ，Lisa A. Nemeth，Lisa A. Nemeth，LISA M. CRAIG，LISA M. CRAIG，罗娜 马霍尔，Lorna Major，LT. COL. DARREL L. CHOAT，LT. COL. DARREL L. CHOAT，Luke Casper，Luke Casper，LYLE K. DREW，LYLE K. DREW，LYNDON K. MCKOWN，LYNDON K. MCKOWN，Lynn E. Williams，Lynn E. Williams，Major General Austin E. Renforth，Major General Austin E. Renforth，Marc Schoellkopf，Marc Schoellkopf，Marcus Jackson，Marcus Jackson，MARIANNE P. MALIZIA，MARIANNE P. MALIZIA，MARJANA D. ZUPCSAN，MARJANA D. ZUPCSAN，MARK CROCKETT，MARK CROCKETT，马克·D·里奇，Mark D. Richey，MARK D. VAN BRUNT，MARK D. VAN BRUNT，马克 H. 克林根，MARK H. CLINGAN，Mark J. Hawley，Mark J. Hawley，马克·米哈伊奇，Mark Mehalic，马克·克罗克特，Mark R. Crockett，马尔-盖伊，Marr-Gaye，Marshall R. Monteville，Marshall R. Monteville，Mary E. Dearman，Mary E. Dearman，Mason R. Dula，Mason R. Dula，马修 G. 布兰卡托，Mathew G. Brancato，Matthew A. Bourassa，Matthew A. Bourassa，MATTHEW C. JENSEN，MATTHEW C. JENSEN，马修·F·阿米顿，Matthew F. Amidon，Matthew Higer，Matthew Higer，MATTHEW M. FRITZ，MATTHEW M. FRITZ，Matthew Magness，Matthew Magness，Matthew O. Berry，Matthew O. Berry，MATTHEW P. HANSON，MATTHEW P. HANSON，马修·雷尔曼，MATTHEW REILMAN，马修·T·马格尼斯，Matthew T. Magness，Matthew W. Higer，Matthew W. Higer，Maurice C. Azar，Maurice C. Azar，莫里斯·L·威廉姆斯，Maurice L. Williams，弥迦书。兰伯特，Micah I. Lambert，迈克尔·B·帕克斯，MICHAEL B. PARKS，MICHAEL D. CURRY，MICHAEL D. CURRY，MICHAEL D. STAPLETON，MICHAEL D. STAPLETON，Michael D. Stohle，Michael D. Stohle，Michael E. Dixon Jr.，Michael E. Dixon Jr.，Michael E. Phillips，Michael E. Phillips，迈克尔·F·费伊，Michael F. Fahey，Michael F. Sears，Michael F. Sears，MICHAEL J VANZO，MICHAEL J VANZO，Michael J. Higgins，Michael J. Higgins，迈克尔·J·普里查德，Michael J. Pritchard，Michael J. Zuhlsdorf，Michael J. Zuhlsdorf，MICHAEL L. REID，MICHAEL L. REID，MICHAEL M. MOEDING，MICHAEL M. MOEDING，Michael Newman，Michael Newman，Michael O. Wentzel，Michael O. Wentzel，MICHAEL P. CRUFF，MICHAEL P. CRUFF，Michael R. Berry，Michael R. Berry，迈克尔·R·博尔顿，Michael R. Bolton，MICHAEL R. MORGAN，MICHAEL R. MORGAN，Michael S. Senigo，Michael S. Senigo，小迈克尔·W·班克，Michael W. Bank Jr.，MICHELLE L. WAGNER，MICHELLE L. WAGNER，Miguel A. Cruz，Miguel A. Cruz，迈克·马丁内斯，Mike Martinez，Mr. Brian L. Thompson，Mr. Brian L. Thompson，MYNDA G. OHMAN，MYNDA G. OHMAN，内森·A·汤普森，Nathan A. Thompson，Nathan Fallin，Nathan Fallin，NATHAN J HILL，NATHAN J HILL，Nathan Parks，Nathan Parks，NATHAN T. DAY，NATHAN T. DAY，Nicholas R. Pederson，Nicholas R. Pederson，O.J. Weiss，O.J. Weiss，Omar J. Randall，Omar J. Randall，Omar T. Basnight，Omar T. Basnight，约瑟夫·瓦诺尼，oseph Vanoni，Patrick G. Miller，Patrick G. Miller，帕特里克·奥沙利文，Patrick O'Sullivan，Patrick W. Donaldson，Patrick W. Donaldson，帕特里克·温普勒，Patrick Wampler，保罗·G·菲尔切克，Paul G. Filcek，保罗·M·斯基普沃思，PAUL M. SKIPWORTH，Paul Tomlinson，Paul Tomlinson，彼得·布恩，Peter Boone，菲尔·赫塞尔廷，PHIL HESELTINE，菲尔·拉姆，Phil Lamb，Philip O. Warlick II，Philip O. Warlick II，Rachel Castellon，Rachel Castellon，拉斐尔·法昆多，Rafael “Rafi” Facundo，Randel J. Gordon，Randel J. Gordon，Ray Gerber，Ray Gerber，Rayford A. Robinson，Rayford A. Robinson，小雷蒙德 A. 史密斯，RAYMOND A. SMITH, JR.，Raymond C. Robles，Raymond C. Robles，雷蒙德·勒布朗，Raymond LeBlanc，REBECCA ARBONA，REBECCA ARBONA，Restituto D. Paz，Restituto D. Paz，Rey D. Schultz，Rey D. Schultz，Richard G. Rhinehart，Richard G. Rhinehart，里克·H·布特维尔，Rick H. Boutwell，Robert A. Long，Robert A. Long，Robert Allen King，Robert Allen King，罗伯特·C·艾吉，ROBERT C. AGEE，罗伯特·C·诺瓦科夫斯基，Robert C. Nowakowski，ROBERT D. DAVIS，ROBERT D. DAVIS，Robert J. Bodisch，Robert J. Bodisch，Robert J. Devall，Robert J. Devall，Robert K. Nash，Robert K. Nash，罗伯特·L·布朗，Robert L. Brown，Rockie K. Wilson，Rockie K. Wilson，Rodney E. McCraine，Rodney E. McCraine，Rodney Jenkins，Rodney Jenkins，罗杰·A·托伯曼，Roger A. Towberman，Rolf E. Mammen，Rolf E. Mammen，Ronald Harper，Ronald Harper，Ronnie J. Woods，Ronnie J. Woods，Rosalie A. Duarte，Rosalie A. Duarte，ROY H. OBERHAUS，ROY H. OBERHAUS，Roy Kitchener\u200B，Roy Kitchener\u200B，Roy Oberhaus，Roy Oberhaus，Roy V. Walton，Roy V. Walton，Russell S. Williford，Russell S. Williford，瑞安·海德，Ryan Hayde，瑞安·J·诺林，Ryan J. Nowlin，Ryan P. Keeney，Ryan P. Keeney，塞缪尔·L·迈耶，SAMUEL L. MEYER，桑德拉·L·威尔逊，Sandra L. Wilson，SARAH H. RUSS，SARAH H. RUSS，Sarah M. Esparza，Sarah M. Esparza，SCOTT A. GOETZE，SCOTT A. GOETZE，斯科特·M·斯塔福德，SCOTT M. STAFFORD，肖恩·C·比特纳，SEAN C. BITTNER，SEAN S. MCKENNA，SEAN S. MCKENNA，Sergeant Major Abel T. Leal，Sergeant Major Abel T. Leal，Sergeant Major Douglas W. Gerhardt，Sergeant Major Douglas W. Gerhardt，Sergeant Major Edward C. Kretschmer，Sergeant Major Edward C. Kretschmer，Sergeant Major Jason R. Cain，Sergeant Major Jason R. Cain，Sergeant Major Jason R. Gillespie，Sergeant Major Jason R. Gillespie，Sergeant Major Johnathan R. Radel，Sergeant Major Johnathan R. Radel，Sergeant William C. Hebb，Sergeant William C. Hebb，Seth Graham，Seth Graham，Sevin Balkuvvar，Sevin Balkuvvar，SGT. MAJ. ADRIAN L. TAGLIERE，SGT. MAJ. ADRIAN L. TAGLIERE，SHANNON O’HARREN，SHANNON O’HARREN，\"沙里夫尔·汗，SHARIFUL M. KHAN，\"，SHARIFUL M. KHAN，SHARIFUL M. KHAN，SHAUNTE Y. COOPER，SHAUNTE Y. COOPER，SHERONNE L. KING，SHERONNE L. KING，Spencer Thomas  ，Spencer Thomas  ，斯泰西·G·伯顿，Stacy G. Burton，斯坦利·C·卡德尔，Stanley C. Cadell，Stanley P. Niedorowski，Stanley P. Niedorowski，STEPHANIE A. MONCALIERI，STEPHANIE A. MONCALIERI，斯蒂芬妮·凯茨，Stephanie Cates，STEPHEN D. GRAY，STEPHEN D. GRAY，Stephen G. Purdy Jr.，Stephen G. Purdy Jr.，STEPHEN NICHOLAS，STEPHEN NICHOLAS，Stephen R. Gwinn，Stephen R. Gwinn，Stephen R. Tedford，Stephen R. Tedford，史蒂夫·C·塞诺夫，Steve. C. Cenov，STEVEN G. BEHMER，STEVEN G. BEHMER，Steven Gonzalves，Steven Gonzalves，Steven R. Thomas，Steven R. Thomas，史蒂文·柯克帕特里克，Steven W. Kirkpatrick，Steven Zubowicz，Steven Zubowicz，Surya J. Frickel，Surya J. Frickel，Taryn N. Stys，Taryn N. Stys，Terence A. Propes，Terence A. Propes，Terri L. Bailey，Terri L. Bailey，特里·L·奥利弗，TERRY L. OLIVER，特里·W·麦克莱恩，TERRY W. MCCLAIN，Thomas Blount，Thomas Blount，Thomas Dean Howell，Thomas Dean Howell，Thomas E. Temple，Thomas E. Temple，Thomas M. Clohessy，Thomas M. Clohessy，THOMAS M. ZERBA，THOMAS M. ZERBA，Thomas O. Pemberton，Thomas O. Pemberton，Thomas R. Rary III，Thomas R. Rary III，Thomas Wolfe，Thomas Wolfe，蒂格·E·普拉特，TIGE E. PLATT，小蒂莫西·怀特，Timothy C. White Jr.，Timothy T. Martin，Timothy T. Martin，Timothy Welter，Timothy Welter，托比·B·罗奇，TOBY B. ROACH，Todd E. Swass，Todd E. Swass，Todd M. Popovic，Todd M. Popovic，Todd Moore，Todd Moore，Tracy Cornett，Tracy Cornett，Travis L. Woodworth，Travis L. Woodworth，Travis M. Board，Travis M. Board，TRENA SAVAGEAU，TRENA SAVAGEAU，Trevor L. James，Trevor L. James，特洛伊·阿杰·布拉希尔，Troy A. J. Brashear，特洛伊·布莱克，Troy E. Black，Tyler M. Griffith，Tyler M. Griffith，Tyler R. Schaff，Tyler R. Schaff，瓦莱丽·A·杰克逊，Valerie A. Jackson，VICKI L. ROBERTSON，VICKI L. ROBERTSON，Victor M. Pereira，Victor M. Pereira，Vince Baker，Vince Baker，文森特·J·奥康纳，Vincent J. O’Connor，Vito S. Smyth，Vito S. Smyth，WANDA T. JONES-HEATH，WANDA T. JONES-HEATH，韦伦·D·佩蒂，Waylon D. Petty，WILL CLARK，WILL CLARK，WILLIAM C. HEBB，WILLIAM C. HEBB，威廉·E·苏扎三世，William E. Souza III，WILLIAM H. GUTERMUTH，WILLIAM H. GUTERMUTH，William H. Hunter，William H. Hunter，威廉·H·麦基班，WILLIAM H. McKIBBAN，WILLIAM J. FRIDAY, JR.，WILLIAM J. FRIDAY, JR.，Naval Sea Systems Command，William J. Galinis，William Martin II，William Martin II，William Radiff，William Radiff，WILLIAM SAROCCO，WILLIAM SAROCCO，WILLIAM T. MCELHINNEY，WILLIAM T. MCELHINNEY，Zachary S. Warakomski，Zachary S. Warakomski，Zachary “Shay” Warakomski，Zachary “Shay” Warakomski，杰森·E·贝利， Jason E. Bailey， Joseph Scala， Joseph Scala，迈克尔·A·康斯托克， Michael A. Comstock，特伦斯·G·泰勒， Terence G. Taylor，Steve Mucklow，Steve Mucklow，Matthew J. Schecter，Matthew J. Schecter，MICHAEL A. SMITH，MICHAEL A. SMITH，WILLIAM WEBSTER，WILLIAM WEBSTER，David R. Markle，David R. Markle，Andy Schreiner，Andy Schreiner，Walter S. Dittmar，Walter S. Dittmar，Mr. Thomas Schoenbeck，Mr. Thomas Schoenbeck，Clifford Collins Jr.，Clifford Collins Jr.，Mr. Kent Miller，Mr. Kent Miller，Brian W. Cavanaugh，Brian W. Cavanaugh，DAVID C. HYMAN，DAVID C. HYMAN，MR. WILLIAM CARTY，MR. WILLIAM CARTY，KEITH D. REVENTLOW，KEITH D. REVENTLOW，ERIN M. KERN，ERIN M. KERN，WILLIAM J. BOWERS，WILLIAM J. BOWERS，Jeff Morgan，Jeff Morgan，Dennis A. Sanchez，Dennis A. Sanchez，Brad Cooper，Brad Cooper，Joshua Lasky，Joshua Lasky，Philip Dennis，Philip Dennis，Jim Aiken，Jim Aiken，Douglas W. Sasse, III，Douglas W. Sasse, III，Michael J. Weaver，Michael J. Weaver，Stephen G. Mack，Stephen G. Mack，Chase D. Patrick，Chase D. Patrick，Calvin M. Foster，Calvin M. Foster，Mistie Marcucci，Mistie Marcucci，Patrick J. Barrett，Patrick J. Barrett，Christina Hicks，Christina Hicks，John H. Walker Jr.，John H. Walker Jr.，Douglas A. Sykes，Douglas A. Sykes，Rear Admiral Darryl L. Walker，Rear Admiral Darryl L. Walker，Jessica N. Mihailin，Jessica N. Mihailin，Joe Calantoni，Joe Calantoni，Jason D. Anderson，Jason D. Anderson，Tom Rudowsky，Tom Rudowsky，Todd Evans，Todd Evans，CARLOS A. MUÑOZ，CARLOS A. MUÑOZ，JEFFREY J. KILIAN，JEFFREY J. KILIAN，PETER LYNCH，PETER LYNCH，MARK K. EDELSON，MARK K. EDELSON，LAWRENCE W. SHARPE，LAWRENCE W. SHARPE，JAMES L. “JK” KELLY，JAMES L. “JK” KELLY，DAVID G. GUNTER，DAVID G. GUNTER，Tyson Kindness，Tyson Kindness，THOMAS K. “TOM” GAINEY，THOMAS K. “TOM” GAINEY，TRENT C. DAVIS，TRENT C. DAVIS，JACK P. GARDNER，JACK P. GARDNER，Floyd Dunstan，Floyd Dunstan，Matthew A. Leard，Matthew A. Leard，Randy P. Oakland，Randy P. Oakland，MICHAEL A. MILLER，MICHAEL A. MILLER，STEVEN T. COX，STEVEN T. COX，Pat Hannifin，Pat Hannifin，Zachary “Stanger” Stang，Zachary “Stanger” Stang，James R. Imlah，James R. Imlah，DUSTIN S. Kuers，DUSTIN S. Kuers，Mikael A. Rockstad，Mikael A. Rockstad，Richie Jenkins，Richie Jenkins，Matthew J. Schecter，Matthew J. Schecter，Christopher D. Stone，Christopher D. Stone，Christopher Cavanaugh，Christopher Cavanaugh，Richard M. Martin，Richard M. Martin，Israel Pedregon，Israel Pedregon，Erik Kenny，Erik Kenny，Christopher Alexander，Christopher Alexander，Jason Wells，Jason Wells，Kevin Norton，Kevin Norton，Kevin Ralston，Kevin Ralston，Heather S. Dent，Heather S. Dent，Nicholas Tilbrook，Nicholas Tilbrook，BRADFORD J. GERING，BRADFORD J. GERING，ROBERT C. FULFORD，ROBERT C. FULFORD，SAMUEL L. MEYER，SAMUEL L. MEYER，PETER A. SIAW，PETER A. SIAW，Michael J. Steffen，Michael J. Steffen，Brad Dunham，Brad Dunham，Michael Doble，Michael Doble，Kenneth J. Williams，Kenneth J. Williams，Michelle L. Leccia，Michelle L. Leccia，Eric Mason，Eric Mason，Robert Buckner，Robert Buckner，Mike Brookes，Mike Brookes，Jon A. O’Connor，Jon A. O’Connor，ALICIA D. ABRAMS，ALICIA D. ABRAMS，JEFFREY J. OLINGER，JEFFREY J. OLINGER，KELLY J. BRANSCOM，KELLY J. BRANSCOM，JOHN C. BURGESS，JOHN C. BURGESS，RICHARD R. DICKENS，RICHARD R. DICKENS，DENNY L. RICHARDSON，DENNY L. RICHARDSON，SHANNON O'HARREN，SHANNON O'HARREN，MATTHEW D. DINMORE，MATTHEW D. DINMORE，Thomas Stockton，Thomas Stockton，Asim Khan，Asim Khan，Gabriel M. Kelly，Gabriel M. Kelly，Mark J. Miller，Mark J. Miller，Khyim Bonas，Khyim Bonas，ROBERT S. WEILER，ROBERT S. WEILER，ANDREW M. NIEBEL，ANDREW M. NIEBEL，JOHN J. WIENER，JOHN J. WIENER，WILLIAM V. OSBORNE，WILLIAM V. OSBORNE，Thomas M. Siverts，Thomas M. Siverts，Steven M. Sprigg，Steven M. Sprigg，Stuart W. Glenn，Stuart W. Glenn，Eric M. Olson，Eric M. Olson，JOSHUA D. MILLER，JOSHUA D. MILLER，JOHN W. SCHLAUD，JOHN W. SCHLAUD，Erich B. Bergiel，Erich B. Bergiel，Wesley O. Turner II，Wesley O. Turner II，John C. Beck，John C. Beck，CALVERT WORTH JR.，CALVERT WORTH JR.，Douglas K. Clark，Douglas K. Clark，Allan G. Jaster，Allan G. Jaster，William (Trey) S. Chairsell III，William (Trey) S. Chairsell III，Christopher J. Adams，Christopher J. Adams，Matthew R. Paul，Matthew R. Paul，JUSTIN W. PHILLIPS，JUSTIN W. PHILLIPS，Thomas N. Trimble，Thomas N. Trimble，Todd E. Mahar，Todd E. Mahar，Adam Coker，Adam Coker，JOSEF H. WIESE，JOSEF H. WIESE，Kevin Shea，Kevin Shea，Timothy J. Myers，Timothy J. Myers，Gerry \"Dutch\" Tritz，Gerry \"Dutch\" Tritz，William Frank，William Frank，Chad \"CHOAD\" Heirigs，Chad \"CHOAD\" Heirigs，James M. Kinter，James M. Kinter，Brent Jaquith，Brent Jaquith，Randall T. Bonine，Randall T. Bonine，Scott F. Benedict，Scott F. Benedict".split("，");
        int count = 0;
        List<String> keywordList = new ArrayList<>();
        //1. 创建Request对象
        SearchRequest request = new SearchRequest("lr_news");

        //2. 指定查询条件
        SearchSourceBuilder builder = new SearchSourceBuilder();
        builder.from(0);
        builder.size(5000);

        request.source(builder);
        for (int i = 0; i < keywords.length; i++) {
            System.err.println(i);
            System.err.println(count);
            System.err.println(String.join(",", keywordList));
            String keyword = keywords[i];
            //3. 执行查询
            builder.query(QueryBuilders.matchPhraseQuery("profile_en", keyword));
            SearchResponse resp = restClientBuilder.search(request, RequestOptions.DEFAULT);
            SearchHits hits = resp.getHits();
            //4. 获取到_source中的数据，并展示
            for (SearchHit hit : hits) {
                if (!keywordList.contains(keyword)) {
                    keywordList.add(keyword);
                }
                Map<String, Object> result = hit.getSourceAsMap();
                System.out.println(result);
            }
        }
        System.err.println("检索结束");
    }*/

    String weaponryJson = "[{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1975\",\"equipmentImg\":\"/img/equip/imgNA.jpg\",\"equipmentName\":\"2A36 Giatsint-B (Hyacinth)\",\"equipmentType\":\"152mm Towed Field Gun\"},{\"country\":\"/img/flag/russia.png\",\"year\":\"1989\",\"equipmentImg\":\"/img/equip/imgNA.jpg\",\"equipmentName\":\"2A45 Sprut (Kraken)\",\"equipmentType\":\"Towed Anti-Tank (AT) Gun System\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1987\",\"equipmentImg\":\"/img/equip/msta-b-152mm-towed-howitzer-russia.jpg\",\"equipmentName\":\"2A65 MSTA-B\",\"equipmentType\":\"152mm Towed Howitzer\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1972\",\"equipmentImg\":\"/img/equip/2s1-gvozdika-self-propelled-artillery-vehicle-soviet-union.jpg\",\"equipmentName\":\"2S1 Gvozdika (M1974)\",\"equipmentType\":\"122mm Self-Propelled Artillery (SPA)\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1989\",\"equipmentImg\":\"/img/equip/2s19-msta-self-propelled-howitzer-vehicle-russia.jpg\",\"equipmentName\":\"2S19 MSTA\",\"equipmentType\":\"Self-Propelled Artillery (SPA) Vehicle\"},{\"country\":\"/img/flag/ukraine.png\",\"year\":\"2018\",\"equipmentImg\":\"/img/equip/2s22-bohdana-6x6-wheeled-self-propelled-gun-carrier-ukraine.jpg\",\"equipmentName\":\"2S22 Bohdana\",\"equipmentType\":\"6x6 Wheeled Self-Propelled Gun Carrier\"},{\"country\":\"/img/flag/russia.png\",\"year\":\"2007\",\"equipmentImg\":\"/img/equip/2s25-sprut-sd.jpg\",\"equipmentName\":\"2S25 (Sprut-SD)\",\"equipmentType\":\"Self-Propelled Tank Destroyer / Light Tank / Infantry Fighting Vehicle\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1971\",\"equipmentImg\":\"/img/equip/2s3-akatsiya-m1973-self-propelled-gun.jpg\",\"equipmentName\":\"2S3 Akatsiya (SO-152) / (M1973)\",\"equipmentType\":\"152mm Self-Propelled Artillery (SPA)\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1975\",\"equipmentImg\":\"/img/equip/2S4-sm240-tyulpan-m1975.jpg\",\"equipmentName\":\"2S4 / SM-240 Tyulpan (M1975)\",\"equipmentType\":\"Self-Propelled Mortar\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1978\",\"equipmentImg\":\"/img/equip/2s5-giatsint-s-hyacinth-model-1981-self-propelled-artillery-soviet-union.jpg\",\"equipmentName\":\"2S5 Giatsint-S (Hyacinth) (M1981)\",\"equipmentType\":\"Self-Propelled Artillery (SPA)\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1976\",\"equipmentImg\":\"/img/equip/2s7-pion-malka-m1975-203mm-tracked-self-propelled-howitzer-vehicle-soviet-union-russia.jpg\",\"equipmentName\":\"2S7 Pion / Malka (M1975)\",\"equipmentType\":\"203mm Tracked Self-Propelled Howitzer (SPH)\"},{\"country\":\"/img/flag/russia.png\",\"year\":\"2011\",\"equipmentImg\":\"/img/equip/9a52-4-tornado-multiple-launch-rocket-system-vehicle.jpg\",\"equipmentName\":\"9A52-4 Tornado\",\"equipmentType\":\"Wheeled Multiple Launch Rocket System (MLRS)\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1965\",\"equipmentImg\":\"/img/equip/imgNA.jpg\",\"equipmentName\":\"9K52 Luna-M (Frog-7)\",\"equipmentType\":\"8x8 Mobile Battlefield Rocket Launcher\"},{\"country\":\"/img/flag/poland.png\",\"year\":\"2015\",\"equipmentImg\":\"/img/equip/ahs-krab-spg.jpg\",\"equipmentName\":\"AHS Krab (Crab)\",\"equipmentType\":\"Self-Propelled Artillery (SPA) Vehicle\"},{\"country\":\"/img/flag/britain.png\",\"year\":\"1978\",\"equipmentImg\":\"/img/equip/fv103-alvis-spartan-apc.jpg\",\"equipmentName\":\"Alvis FV103 Spartan\",\"equipmentType\":\"Armored Personnel Carrier (APC)\"},{\"country\":\"/img/flag/britain.png\",\"year\":\"1981\",\"equipmentImg\":\"/img/equip/alvis-fv4333-stormer-armored-personnel-carrier.jpg\",\"equipmentName\":\"Alvis FV4333 Stormer\",\"equipmentType\":\"Armored Personnel Carrier (APC) / Multirole Armored Fighting Vehicle (AFV)\"},{\"country\":\"/img/flag/france.png\",\"year\":\"1981\",\"equipmentImg\":\"/img/equip/giat-amx-10rc.jpg\",\"equipmentName\":\"AMX-10RC\",\"equipmentType\":\"6-Wheeled Armored Reconnaissance / Support Vehicle\"},{\"country\":\"/img/flag/russia.png\",\"year\":\"2022\",\"equipmentImg\":\"/img/equip/marker-unmanned-ground-vehicle-russia.jpg\",\"equipmentName\":\"AT/ARF Marker\",\"equipmentType\":\"Unmanned Ground Vehicle (UGV)\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1964\",\"equipmentImg\":\"/img/equip/bm21-grad_7.jpg\",\"equipmentName\":\"BM-21 (Grad)\",\"equipmentType\":\"Multiple Launch Rocket System (MLRS)\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1949\",\"equipmentImg\":\"/img/equip/bm24-mlrs.jpg\",\"equipmentName\":\"BM-24 (Katyusha)\",\"equipmentType\":\"6x6 Wheeled Multiple Launch Rocket System\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1975\",\"equipmentImg\":\"/img/equip/bm27-uragan-hurricane-9p140.jpg\",\"equipmentName\":\"BM-27 (Uragan ) / 9P140\",\"equipmentType\":\"Self-Propelled Multiple Rocket Launcher\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1989\",\"equipmentImg\":\"/img/equip/bm30-smerch-mlrs-8x8-wheeled-vehicle_2.jpg\",\"equipmentName\":\"BM-30 (Smerch) / 9A52-2\",\"equipmentType\":\"8x8 Wheeled Multiple Launch Rocket System (MLRS)\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1939\",\"equipmentImg\":\"/img/equip/bm13-katyusha.jpg\",\"equipmentName\":\"BM-8 / BM-13 / BM-31 (Katyusha)\",\"equipmentType\":\"Multiple Launch Rocket System\"},{\"country\":\"/img/flag/turkey.png\",\"year\":\"2010\",\"equipmentImg\":\"/img/equip/bmc-kirpi-mrap-vehicle-turkey_2.jpg\",\"equipmentName\":\"BMC Kirpi\",\"equipmentType\":\"Mine-Resistant, Ambush-Protected (MRAP) Vehicle\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1969\",\"equipmentImg\":\"/img/equip/bmd1-infantry-fighting-vehicle.jpg\",\"equipmentName\":\"BMD-1 (Boyevaya Mashina Desanta)\",\"equipmentType\":\"Airborne Amphibious Light Tank / Infantry Fighting Vehicle\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1985\",\"equipmentImg\":\"/img/equip/bmd2-infantry-fighting-vehicle.jpg\",\"equipmentName\":\"BMD-2 (Boyevaya Mashina Desanta)\",\"equipmentType\":\"Airborne Amphibious Light Tank / Infantry Fighting Vehicle\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1990\",\"equipmentImg\":\"/img/equip/bmd3-infantry-fighting-vehicle_2.jpg\",\"equipmentName\":\"BMD-3 (Boyevaya Mashina Desanta)\",\"equipmentType\":\"Airborne Amphibious Light Tank / Infantry Fighting Vehicle\"},{\"country\":\"/img/flag/russia.png\",\"year\":\"2005\",\"equipmentImg\":\"/img/equip/bmd4-infantry-fighting-vehicle_2.jpg\",\"equipmentName\":\"BMD-4 (Boyevaya Mashina Desanta)\",\"equipmentType\":\"Airborne Amphibious Light Tank / Light Infantry Fighting Vehicle (LIFV)\"},{\"country\":\"/img/flag/russia.png\",\"year\":\"2001\",\"equipmentImg\":\"/img/equip/imgNA.jpg\",\"equipmentName\":\"BMO-T\",\"equipmentType\":\"Heavy Armored Personnel Carrier\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1966\",\"equipmentImg\":\"/img/equip/bmp-1-infantry-fighting-vehicle.jpg\",\"equipmentName\":\"BMP-1 (Boyevaya Mashina Pekhoty)\",\"equipmentType\":\"Infantry Fighting Vehicle (IFV) / Light Tank\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1982\",\"equipmentImg\":\"/img/equip/bmp-2-infantry-fighting-vehicle_9.jpg\",\"equipmentName\":\"BMP-2 (Boyevaya Mashina Pekhoty)\",\"equipmentType\":\"Infantry Fighting Vehicle (IFV)\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1987\",\"equipmentImg\":\"/img/equip/bmp-3-infantry-fighting-vehicle.jpg\",\"equipmentName\":\"BMP-3 (Boyevaya Mashina Pekhoty)\",\"equipmentType\":\"Infantry Fighting Vehicle (IFV) / Light Tank\"},{\"country\":\"/img/flag/russia.png\",\"year\":\"2011\",\"equipmentImg\":\"/img/equip/bmpt-terminator-heavy-infantry-fighting-vehicle-russia_2.jpg\",\"equipmentName\":\"BMPT (Terminator) (Object 199)\",\"equipmentType\":\"Heavy Armored Support Vehicle (HASV)\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1966\",\"equipmentImg\":\"/img/equip/brdm2.jpg\",\"equipmentName\":\"BRDM-2\",\"equipmentType\":\"4x4 Wheeled Amphibious Light Armored Scout Car\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1944\",\"equipmentImg\":\"/img/equip/100mm-m1944-d10-towed-gun.jpg\",\"equipmentName\":\"BS-3 (Model 1944)\",\"equipmentType\":\"100mm Towed Artillery / Anti-Tank Gun\"},{\"country\":\"/img/flag/ukraine.png\",\"year\":\"2009\",\"equipmentImg\":\"/img/equip/btr4-8x8-wheeled-armored-personnel-carrier-ukraine.jpg\",\"equipmentName\":\"BTR-4\",\"equipmentType\":\"8-Wheeled Armored Personnel Carrier (APC)\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1960\",\"equipmentImg\":\"/img/equip/btr-60-armored-personnel-carrier.jpg\",\"equipmentName\":\"BTR-60\",\"equipmentType\":\"8x8 Armored Personnel Carrier (APC)\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1972\",\"equipmentImg\":\"/img/equip/btr70.jpg\",\"equipmentName\":\"BTR-70\",\"equipmentType\":\"8x8 Wheeled Armored Personnel Carrier (APC)\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1987\",\"equipmentImg\":\"/img/equip/btr80_7.jpg\",\"equipmentName\":\"BTR-80\",\"equipmentType\":\"Armored Personnel Carrier (APC)\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1974\",\"equipmentImg\":\"/img/equip/btrd-airborne-amphibious-vehicle-soviet-union.webp\",\"equipmentName\":\"BTR-D (Bronetransportyor Desanta)\",\"equipmentType\":\"Amphibious Air-Droppable Armored Fighting Vehicle (AFV)\"},{\"country\":\"/img/flag/poland.png\",\"year\":\"1967\",\"equipmentImg\":\"/img/equip/bmp-1-infantry-fighting-vehicle.jpg\",\"equipmentName\":\"BWP-1 (Bojowy Woz Piechoty-1)\",\"equipmentType\":\"Infantry Fighting Vehicle (IFV)\"},{\"country\":\"/img/flag/sweden.png\",\"year\":\"1993\",\"equipmentImg\":\"/img/equip/combat-vehicle-90-cv90-stridsfordon-ifv_8.jpg\",\"equipmentName\":\"Combat Vehicle 90 / Stridsfordon 90 (CV90 / Strf 90\",\"equipmentType\":\"Infantry Fighting Vehicle (IFV) / Light Tank\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1955\",\"equipmentImg\":\"/img/equip/d20.jpg\",\"equipmentName\":\"D-20 (Model 1955)\",\"equipmentType\":\"152mm Towed Field Howitzer\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1963\",\"equipmentImg\":\"/img/equip/122mm-model-1963-d30-gun.jpg\",\"equipmentName\":\"D-30 / 2A18 (Model 1963)\",\"equipmentType\":\"122mm Towed Field Howitzer\"},{\"country\":\"/img/flag/netherlands.png\",\"year\":\"2003\",\"equipmentImg\":\"/img/equip/fennek-light-armored-vehicle.jpg\",\"equipmentName\":\"Fennek LGS (Leichter Gepanzerter Spahwagen)\",\"equipmentType\":\"Light Armored Reconnaissance Vehicle\"},{\"country\":\"/img/flag/sweden.png\",\"year\":\"2016\",\"equipmentImg\":\"/img/equip/fh77bw-l52-archer-6x6-wheeled-self-propelled-artillery-vehicle-sweden.jpg\",\"equipmentName\":\"FH77BW L52 (Archer)\",\"equipmentType\":\"6x6 Wheeled Self-Propelled Artillery (SPA)\"},{\"country\":\"/img/flag/united_states.png\",\"year\":\"2002\",\"equipmentImg\":\"/img/equip/cougar-mrap.jpg\",\"equipmentName\":\"Force Protection Cougar\",\"equipmentType\":\"Troop Transport / Command and Control Communications / EOD / Lead Convoy Vehicle\"},{\"country\":\"/img/flag/russia.png\",\"year\":\"2007\",\"equipmentImg\":\"/img/equip/gaz-tigr-multirole-military-vehicle-russia_2.jpg\",\"equipmentName\":\"GAZ Tigr (Tiger)\",\"equipmentType\":\"4x4 Infantry Mobility Vehicle (IMV)\"},{\"country\":\"/img/flag/canada.png\",\"year\":\"2016\",\"equipmentImg\":\"/img/equip/general-dynamics-land-systems-canada-lav6-ifv.webp\",\"equipmentName\":\"GDLS-C LAV-6 (LAV-VI / ACSV)\",\"equipmentType\":\"8x8 Wheeled Infantry Fighting Vehicle (IFV)\"},{\"country\":\"/img/flag/united_states.png\",\"year\":\"2002\",\"equipmentImg\":\"/img/equip/m1126-stryker.jpg\",\"equipmentName\":\"General Dynamics Stryker\",\"equipmentType\":\"8x8 Wheeled Multirole Armored Fighting Vehicle (AFV)\"},{\"country\":\"/img/flag/sweden.png\",\"year\":\"2005\",\"equipmentImg\":\"/img/equip/hagglunds-bandvagn-206-sweden.jpg\",\"equipmentName\":\"Hagglunds Bandvagn BvS 10 (Viking)\",\"equipmentType\":\"All-Terrain Articulated Light Armored Vehicle\"},{\"country\":\"/img/flag/united_states.png\",\"year\":\"1982\",\"equipmentImg\":\"/img/equip/hemtt-heavy-tactical-military-vehicle-usarmy.jpg\",\"equipmentName\":\"HEMTT (Heavy Expanded Mobility Tactical Truck)\",\"equipmentType\":\"Heavy-class Military Vehicle\"},{\"country\":\"/img/flag/united_states.png\",\"year\":\"1989\",\"equipmentImg\":\"/img/equip/hmmwv-avenger_6.jpg\",\"equipmentName\":\"HMMWV Avenger\",\"equipmentType\":\"4-Wheeled Mobile Air Defense Missile System (ADMS)\"},{\"country\":\"/img/flag/united_states.png\",\"year\":\"1994\",\"equipmentImg\":\"/img/equip/hmmwv-m1114uah_6.jpg\",\"equipmentName\":\"HMMWV M1114 UAH (Up-Armored Humvee)\",\"equipmentType\":\"Up-Armored HMMWV Armament Carrier Vehicle\"},{\"country\":\"/img/flag/united_states.png\",\"year\":\"2006\",\"equipmentImg\":\"/img/equip/international-mxt-mv-mrap-vehicle-united-states.jpg\",\"equipmentName\":\"International MXT-MV (Miltary Extreme Truck - Military Version)\",\"equipmentType\":\"Mine-Resistant, Ambush-Protected (MRAP) Military Truck\"},{\"country\":\"/img/flag/italy.png\",\"year\":\"2005\",\"equipmentImg\":\"/img/equip/iveco-light-multirole-vehicle-italy.jpg\",\"equipmentName\":\"IVECO LMV (Light Multirole Vehicle)\",\"equipmentType\":\"Infantry Mobility Vehicle (IMV)\"},{\"country\":\"/img/flag/russia.png\",\"year\":\"2014\",\"equipmentImg\":\"/img/equip/jsc-uran6-unmanned-mine-clearance-vehicle-russia.jpg\",\"equipmentName\":\"JSC Uran-6\",\"equipmentType\":\"Unmanned Mine-Clearing Vehicle\"},{\"country\":\"/img/flag/russia.png\",\"year\":\"2013\",\"equipmentImg\":\"/img/equip/kamaz-sba-60k2-bulat-6x6-wheeled-armored-personnel-carrier-russia.jpg\",\"equipmentName\":\"KAMAZ SBA-60K2 Bulat\",\"equipmentType\":\"6x6 Wheeled Armored Personnel Carrier (APC)\"},{\"country\":\"/img/flag/russia.png\",\"year\":\"2014\",\"equipmentImg\":\"/img/equip/kamaz-tornado-g-mlrs-vehicle-russian-army.jpg\",\"equipmentName\":\"KAMAZ Tornado-G\",\"equipmentType\":\"6x6 Wheeled Mobile Multiple-Launch Rocket System (MLRS)\"},{\"country\":\"/img/flag/russia.png\",\"year\":\"2014\",\"equipmentImg\":\"/img/equip/kamaz-typhoon-armored-vehicle-russia.jpg\",\"equipmentName\":\"KAMAZ Typhoon\",\"equipmentType\":\"Mine-Resistant, Ambush-Protected (MRAP) Vehicle\"},{\"country\":\"/img/flag/russia.png\",\"year\":\"2020\",\"equipmentImg\":\"/img/equip/kamaz-53949-mrap-military-vehicle-russia.jpg\",\"equipmentName\":\"KAMAZ-53949 (Taifun K-53949)\",\"equipmentType\":\"Mine-Resistant, Ambush-Protected (MRAP) Vehicle\"},{\"country\":\"/img/flag/russia.png\",\"year\":\"2014\",\"equipmentImg\":\"/img/equip/imgNA.jpg\",\"equipmentName\":\"Krasukha MEWC\",\"equipmentType\":\"Mobile Electronic Warfare Complex\"},{\"country\":\"/img/flag/germany.png\",\"year\":\"2001\",\"equipmentImg\":\"/img/equip/krauss-maffei-wegmann-dingo_2.jpg\",\"equipmentName\":\"Krauss-Maffei Wegmann Dingo\",\"equipmentType\":\"4x4 Armored Personnel Carrier (APC) / MRAP\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1950\",\"equipmentImg\":\"/img/equip/100mm-air-defense-gun-ks19.jpg\",\"equipmentName\":\"KS-19 (100mm Air Defense Gun)\",\"equipmentType\":\"100mm Towed Anti-Aircraft Gun\"},{\"country\":\"/img/flag/germany.png\",\"year\":\"1965\",\"equipmentImg\":\"/img/equip/leopard-1-mbt_12.jpg\",\"equipmentName\":\"Leopard 1\",\"equipmentType\":\"Main Battle Tank (MBT)\"},{\"country\":\"/img/flag/germany.png\",\"year\":\"1979\",\"equipmentImg\":\"/img/equip/leopard-2-main-battle-tank-germany.jpg\",\"equipmentName\":\"Leopard 2\",\"equipmentType\":\"Main Battle Tank (MBT)\"},{\"country\":\"/img/flag/yugoslavia.png\",\"year\":\"1985\",\"equipmentImg\":\"/img/equip/m84-mbt_4.jpg\",\"equipmentName\":\"M-84 (MBT)\",\"equipmentType\":\"Main Battle Tank\"},{\"country\":\"/img/flag/united_states.png\",\"year\":\"1980\",\"equipmentImg\":\"/img/equip/m1-abrams-main-battle-tank-united-states.jpg\",\"equipmentName\":\"M1 Abrams\",\"equipmentType\":\"Main Battle Tank (MBT)\"},{\"country\":\"/img/flag/united_states.png\",\"year\":\"1960\",\"equipmentImg\":\"/img/equip/m113-armored-personnel-carrier.jpg\",\"equipmentName\":\"M113 APC\",\"equipmentType\":\"Tracked Armored Personnel Carrier\"},{\"country\":\"/img/flag/united_states.png\",\"year\":\"2005\",\"equipmentImg\":\"/img/equip/m142-himars-mlrs.jpg\",\"equipmentName\":\"M142 High Mobility Artillery Rocket System (HIMARS)\",\"equipmentType\":\"Multiple Launch Rocket System (MLRS)\"},{\"country\":\"/img/flag/united_states.png\",\"year\":\"1981\",\"equipmentImg\":\"/img/equip/m2-bradley-infantry-fighting-vehicle-united-states.jpg\",\"equipmentName\":\"M2 Bradley\",\"equipmentType\":\"Infantry Fighting Vehicle (IFV)\"},{\"country\":\"/img/flag/united_states.png\",\"year\":\"1969\",\"equipmentImg\":\"/img/equip/m727-guided-missile-carrier.jpg\",\"equipmentName\":\"M727 Hawk\",\"equipmentType\":\"Tracked Guided Missile Equipment Carrier\"},{\"country\":\"/img/flag/britain.png\",\"year\":\"2005\",\"equipmentImg\":\"/img/equip/m777-ufh-ultralightweight-field-howitzer-united-states.jpg\",\"equipmentName\":\"M777 UFH (Ultra-lightweight Field Howitzer)\",\"equipmentType\":\"155mm Lightweight Towed Artillery\"},{\"country\":\"/img/flag/united_states.png\",\"year\":\"1981\",\"equipmentImg\":\"/img/equip/mim104-patriot-missile-system.jpg\",\"equipmentName\":\"MIM-104 Patriot\",\"equipmentType\":\"Surface-to-Air Missile (SAM) System\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1968\",\"equipmentImg\":\"/img/equip/mt-lb-armored-personnel-carrier_16.jpg\",\"equipmentName\":\"MT-LB (M1970)\",\"equipmentType\":\"Multi-Purpose Tracked Vehicle\"},{\"country\":\"/img/flag/united_states.png\",\"year\":\"2007\",\"equipmentImg\":\"/img/equip/navistar-international-maxxpro-mrap.jpg\",\"equipmentName\":\"Navistar International MaxxPro\",\"equipmentType\":\"Mine Resistant Ambush Protected (MRAP) Vehicle\"},{\"country\":\"/img/flag/france.png\",\"year\":\"2008\",\"equipmentImg\":\"/img/equip/nexter-giat-caesar-6x6-wheeled-self-propelled-gun-vehicle.webp\",\"equipmentName\":\"Nexter (GIAT) CAESAR (CAmion Equipe d'un Systeme d'ARtillerie)\",\"equipmentType\":\"Wheeled Self-Propelled Howtizer (SPH)\"},{\"country\":\"/img/flag/ukraine.png\",\"year\":\"2014\",\"equipmentImg\":\"/img/equip/praktika-kozak-light-armored-vehicle-ukraine.jpg\",\"equipmentName\":\"Practika Kozak\",\"equipmentType\":\"4x4 Multi-Purpose Light-Armored Vehicle\"},{\"country\":\"/img/flag/germany.png\",\"year\":\"1998\",\"equipmentImg\":\"/img/equip/pzh-2000_5.jpg\",\"equipmentName\":\"PzH 2000 (Panzerhaubitze 2000)\",\"equipmentType\":\"Self-Propelled Gun (SPG) / Howitzer\"},{\"country\":\"/img/flag/czech.png\",\"year\":\"1972\",\"equipmentImg\":\"/img/equip/rm70-wheeled-rocket-projecting-vehicle-czechoslovakia.jpg\",\"equipmentName\":\"RM-70 (Raketomet vz. 70)\",\"equipmentType\":\"Wheeled Multiple Rocket Launcher (MRL)\"},{\"country\":\"/img/flag/canada.png\",\"year\":\"2020\",\"equipmentImg\":\"/img/equip/roshel-senator-4x4-vehicle-canada.jpg\",\"equipmentName\":\"Roshel Senator\",\"equipmentType\":\"4x4 Light Armored Security Vehicle\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1978\",\"equipmentImg\":\"/img/equip/sa10-grumble-anti-aircraft-missile-system-russia.jpg\",\"equipmentName\":\"SA-10 (Grumble) / S-300\",\"equipmentType\":\"Long-Range Anti-Aircraft Mobile Defense System\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1979\",\"equipmentImg\":\"/img/equip/sa11-gadfly_3.jpg\",\"equipmentName\":\"SA-11 (Gadfly) / 9K37 Buk\",\"equipmentType\":\"Medium-Range Surface-to-Air Missile (SAM) System\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1988\",\"equipmentImg\":\"/img/equip/sa12.jpg\",\"equipmentName\":\"SA-12 (Gladiator / Giant) / S-300V\",\"equipmentType\":\"Long-Range, High-Altitude Self-Propelled Surface-to-Air Missile (SAM) System\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1979\",\"equipmentImg\":\"/img/equip/sa13-gopher-9k35-strela10.jpg\",\"equipmentName\":\"SA-13 (Gopher) / 9K35 Strela-10\",\"equipmentType\":\"Self-Propelled, Tracked SAM System\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1986\",\"equipmentImg\":\"/img/equip/sa15-gauntlet-9k330-tor-sam-vehicle-russia.jpg\",\"equipmentName\":\"SA-15 (Gauntlet) / 9K330 Tor\",\"equipmentType\":\"Self-Propelled Surface-to-Air Missile (SAM) Vehicle\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1982\",\"equipmentImg\":\"/img/equip/2s6.jpg\",\"equipmentName\":\"SA-19 (Grisom) / 2K22 Tunguska\",\"equipmentType\":\"Self-Propelled Air Defense System\"},{\"country\":\"/img/flag/russia.png\",\"year\":\"2007\",\"equipmentImg\":\"/img/equip/s400-air-defense-vehicle-russia.jpg\",\"equipmentName\":\"SA-21 (Growler) / S-400 Triumf\",\"equipmentType\":\"Mobile Surface-to-Air Missile (SAM) System\"},{\"country\":\"/img/flag/russia.png\",\"year\":\"2003\",\"equipmentImg\":\"/img/equip/sa22-greyhound-pantsir-s1-air-defense-vehicle-russia_2.jpg\",\"equipmentName\":\"SA-22 (Greyhound) / Pantsir-S1\",\"equipmentType\":\"Mobile Self-Propelled Anti-Aircraft System (SPAAS)\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1967\",\"equipmentImg\":\"/img/equip/sa5.jpg\",\"equipmentName\":\"SA-5 / S-200 (Gammon)\",\"equipmentType\":\"Surface-to-Air Missile (SAM) Defense System\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1972\",\"equipmentImg\":\"/img/equip/sa8-gecko_3.jpg\",\"equipmentName\":\"SA-8 (Gecko) / 9K33 OSA\",\"equipmentType\":\"6x6 Wheeled Self-Propelled SAM Vehicle\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1968\",\"equipmentImg\":\"/img/equip/sa9-gaskin.jpg\",\"equipmentName\":\"SA-9 (Gaskin) / 9K31 Strela-1\",\"equipmentType\":\"4x4 Mobile Anti-Aircraft Missile System\"},{\"country\":\"/img/flag/czech.png\",\"year\":\"1981\",\"equipmentImg\":\"/img/equip/dana-152mm_3.jpg\",\"equipmentName\":\"SpGH DANA (ZUZANA)\",\"equipmentType\":\"8x8 Wheeled Self-Propelled Gun (SPG)\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1976\",\"equipmentImg\":\"/img/equip/ss21-scarab-otr21-tochka-ballistic-missile-launcher-vehicle-soviet-union.jpg\",\"equipmentName\":\"SS-21 (Scarab) OTR-21 Tochka\",\"equipmentType\":\"6x6 Wheeled Ballistic Missile Launcher Vehicle\"},{\"country\":\"/img/flag/russia.png\",\"year\":\"2006\",\"equipmentImg\":\"/img/equip/ss26-stone-9k720-iskander-short-range-ballistic-missile-launcher-system-russia.jpg\",\"equipmentName\":\"SS-26 (Stone) / 9K720 Iskander\",\"equipmentType\":\"Mobile Short-Ranged Battlefield Missile Launcher\"},{\"country\":\"/img/flag/britain.png\",\"year\":\"2003\",\"equipmentImg\":\"/img/equip/supacat-coyote-carrier-car_2.jpg\",\"equipmentName\":\"Supacat Jackal MWMIK\",\"equipmentType\":\"Light Patrol Vehicle (LPV)\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1949\",\"equipmentImg\":\"/img/equip/t54-main-battle-tank.jpg\",\"equipmentName\":\"T-54\",\"equipmentType\":\"Medium Tank / Main Battle Tank (MBT)\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1958\",\"equipmentImg\":\"/img/equip/t55-main-battle-tank.jpg\",\"equipmentName\":\"T-55\",\"equipmentType\":\"Medium Tank / Main Battle Tank (MBT)\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1961\",\"equipmentImg\":\"/img/equip/t62-main-battle-tank_6.jpg\",\"equipmentName\":\"T-62\",\"equipmentType\":\"Medium Tank / Main Battle Tank (MBT)\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1966\",\"equipmentImg\":\"/img/equip/t64-main-battle-tank.jpg\",\"equipmentName\":\"T-64\",\"equipmentType\":\"Main Battle Tank (MBT)\"},{\"country\":\"/img/flag/ukraine.png\",\"year\":\"1999\",\"equipmentImg\":\"/img/equip/t64bm-bulat-main-battle-tank-ukraine.jpg\",\"equipmentName\":\"T-64BM (Bulat)\",\"equipmentType\":\"Main Battle Tank (MBT)\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1972\",\"equipmentImg\":\"/img/equip/t72-main-battle-tank.jpg\",\"equipmentName\":\"T-72 (Ural)\",\"equipmentType\":\"Main Battle Tank (MBT)\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1976\",\"equipmentImg\":\"/img/equip/t80-main-battle-tank-soviet-union_2.jpg\",\"equipmentName\":\"T-80 (MBT)\",\"equipmentType\":\"Main Battle Tank\"},{\"country\":\"/img/flag/ukraine.png\",\"year\":\"1999\",\"equipmentImg\":\"/img/equip/t84-main-battle-tank-ukraine.jpg\",\"equipmentName\":\"T-84 (Oplot)\",\"equipmentType\":\"Main Battle Tank (MBT)\"},{\"country\":\"/img/flag/russia.png\",\"year\":\"1995\",\"equipmentImg\":\"/img/equip/t90-main-battle-tank-russia_12.jpg\",\"equipmentName\":\"T-90\",\"equipmentType\":\"Main Battle Tank (MBT)\"},{\"country\":\"/img/flag/australia.png\",\"year\":\"2004\",\"equipmentImg\":\"/img/equip/thales-bushmaster-mrap-australia.jpg\",\"equipmentName\":\"Thales Bushmaster\",\"equipmentType\":\"Mine-Resistant, Ambush-Protected (MRAP) Wheeled Armored Vehicle\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1988\",\"equipmentImg\":\"/img/equip/tos1-multiple-launch-rocket-system-tracked-combat-vehicle-russia.jpg\",\"equipmentName\":\"TOS-1 (TOC-1)\",\"equipmentType\":\"Multiple Launch Rocket System (MLRS)\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1978\",\"equipmentImg\":\"/img/equip/ur77-meteorite-mine-clearing-vehicle-soviet-union-russia.jpg\",\"equipmentName\":\"UR-77 (Meterorit)\",\"equipmentType\":\"Mine Clearing Vehicle\"},{\"country\":\"/img/flag/russia.png\",\"year\":\"2014\",\"equipmentImg\":\"/img/equip/ural-typhoon-mrap-vehicle-russia.jpg\",\"equipmentName\":\"Ural Typhoon\",\"equipmentType\":\"Mine-Resistant, Ambush-Protected (MRAP) Vehicle\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1977\",\"equipmentImg\":\"/img/equip/ural-43206-light-cargo-military-truck-russia.jpg\",\"equipmentName\":\"Ural-43206\",\"equipmentType\":\"4x4 Light Cargo Military Truck\"},{\"country\":\"/img/flag/russia.png\",\"year\":\"1989\",\"equipmentImg\":\"/img/equip/imgNA.jpg\",\"equipmentName\":\"Ural-5323\",\"equipmentType\":\"Multirole 8x8 Wheeled Military Truck\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1964\",\"equipmentImg\":\"/img/equip/imgNA.jpg\",\"equipmentName\":\"ZiL-131\",\"equipmentType\":\"6x6 Wheeled Military Truck\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1965\",\"equipmentImg\":\"/img/equip/zsu-23-4-shilka.jpg\",\"equipmentName\":\"ZSU-23-4 (Shilka)\",\"equipmentType\":\"Self-Propelled Anti-Aircraft Gun (SPAAG)\"},{\"country\":\"/img/flag/soviet_union.png\",\"year\":\"1960\",\"equipmentImg\":\"/img/equip/zu23.jpg\",\"equipmentName\":\"ZU-23-2 / ZU-23\",\"equipmentType\":\"Towed Anti-Aircraft Artillery (AAA)\"}]";

    /**
     * 装备相关
     *
     * @return 结果
     */
    @Override
    public String updateWeaponry() {
        List<WeaponryJson> list = new ArrayList<>();
        List<Long> equipmentIdList = new ArrayList<>();
        try {
            JSONArray jsonArray = JSONArray.parseArray(weaponryJson);
            for (int i = 0; i < jsonArray.size(); i++) {
                String object = jsonArray.getJSONObject(i).toString();
                WeaponryJson weaponry = JSONObject.parseObject(object, WeaponryJson.class);
                String equipmentName = weaponry.getEquipmentName();
                if ("MIM-104 Patriot".equals(equipmentName)) {
                    equipmentName = "MIM-104";
                }
                List<IdwWeaponryBasic> weaponryList = weaponryBasicMapper.selectByNameCnOrNameEn(null, equipmentName);
                if (!weaponryList.isEmpty()) {
                    weaponry.setEquipmentCode(weaponryList.get(0).getWeaponryCode());
                } else {
                    List<Long> idList = testMapper.selectEquipmentIdByName(equipmentName);
                    if (!idList.isEmpty()) {
                        weaponry.setEquipmentCode(idList.get(0).toString());
                        equipmentIdList.add(idList.get(0));
                    }
                }
                list.add(weaponry);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        Gson gson = new Gson();
        String json = gson.toJson(list);
        String idJson = gson.toJson(equipmentIdList);
        return "操作完成！";
        /*userName = ShiroUtils.getUserName();
        nowDate = DateUtils.getNowDate();
        List<IdwMultimedia> multimediaList = testMapper.seelctCollectMultimedia();
        for (IdwMultimedia multimedia : multimediaList) {
            multimedia.setThumbnail(multimedia.getThumbnail().replace("/prod-api", ""));
            multimedia.setStoragePath(multimedia.getStoragePath().replace("/prod-api", ""));
            String storagePath = multimedia.getStoragePath();
            if (StringUtils.isBlank(multimedia.getMediaType()) && StringUtils.isNotBlank(storagePath)) {
                multimedia.setMediaType(storagePath.substring(storagePath.lastIndexOf(".") + 1));
            }
            if (StringUtils.isBlank(multimedia.getMd5()) && StringUtils.isNotBlank(storagePath)) {
                String md5 = FileUtils.getMd5(storagePath);
                multimedia.setMd5(StringUtils.isBlank(md5) ? "暂无" : md5);
            }
            if (StringUtils.isBlank(multimedia.getWebsiteName())) {
                multimedia.setWebsiteName("www.dvidshub.net");
            }
            String releaseDate = multimedia.getReleaseDate();
            if (StringUtils.isNotBlank(releaseDate)) {
                String[] date = releaseDate.split("\\.");
                multimedia.setReleaseDate(date[2] + "-" + date[0] + "-" + date[1]);
            }
            multimedia.setCreateBy(userName);
            multimedia.setCreateTime(nowDate);
            multimedia.setAssociationId("暂无");
            idwMultimediaMapper.insertIdwMultimedia(multimedia);
        }
        return "操作完成！";*/
    }

    /**
     * Excel相关
     *
     * @return 结果
     */
    @Override
    public String excel() {
        List<PeopleVo> educationalExperienceList = new ArrayList<>();
        try {
            ExcelUtil<IdwPeopleMain> util = new ExcelUtil<IdwPeopleMain>(IdwPeopleMain.class);
            InputStream is = new FileInputStream(new File("C:\\Users\\<USER>\\Desktop\\海空太空军相关1500人员.xls"));
            List<IdwPeopleMain> list = util.importExcel("2023-12-27 导出海空太空军相关1500人员", is);
            ExcelUtil<PeopleVo> peopleVoExcelUtil = new ExcelUtil<PeopleVo>(PeopleVo.class);
            for (IdwPeopleMain people : list) {
                String educationalExperience = people.getEducationalExperience();
                if (StringUtils.isBlank(educationalExperience)) {
                    continue;
                }
                educationalExperience=educationalExperience.replaceAll("U. S.","US#####").replaceAll("S.","S#####").replaceAll("U.","U#####").replaceAll("M.S.","MS#####").replaceAll("B.S.","BS#####").replaceAll("\\r\\n",".").replaceAll("\\r",".").replaceAll("\\n",".");
                String[] educationalExperienceArr = educationalExperience.split("\\.");
                for (String e : educationalExperienceArr) {
                    if (StringUtils.isNotBlank(e)) {
                        e=e.replaceAll("US#####","U. S.").replaceAll("S#####","S.").replaceAll("U#####","U.").replaceAll("BS#####","B.S.").replaceAll("MS#####","M.S.");
                        PeopleVo ePeople = new PeopleVo();
                        ePeople.setPeopleCode(people.getPeopleCode());
                        ePeople.setContent(e);
                        educationalExperienceList.add(ePeople);
                    }
                }
            }
            peopleVoExcelUtil.exportExcel(educationalExperienceList, "人员教育经历");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "操作完成！";
    }

    /**
     * 整合资源库->联合作战资源库导入
     *
     * @return 结果
     */
    public String integrationResouce() {
        String errStr = "";
        List<IdwResourceDictCategory> resourceDictCategoryList = testMapper.selectTDocumentType();
        for (IdwResourceDictCategory resourceDictCategory : resourceDictCategoryList) {
            resourceDictCategory.setAncestors("0,15");
            idwResourceDictCategoryMapper.insertIdwResourceDictCategory(resourceDictCategory);
            resourceDictCategory.setAncestors(resourceDictCategory.getAncestors() + "," + resourceDictCategory.getCategoryId());
            idwResourceDictCategoryMapper.updateIdwResourceDictCategory(resourceDictCategory);
            if (StringUtils.isNotBlank(resourceDictCategory.getUpdateBy()) && resourceDictCategory.getUpdateBy().split(",").length > 1) {
                String[] tCategoryIds = resourceDictCategory.getUpdateBy().split(",");
                String tCategoryId = tCategoryIds[1];
                List<IdwResourceDictCategory> subsetList = testMapper.selectTDocumentTypeSubsetByTCategoryIds(tCategoryId);
                for (IdwResourceDictCategory category : subsetList) {
                    category.setParentId(resourceDictCategory.getCategoryId());
                    category.setAncestors(resourceDictCategory.getAncestors());
                    idwResourceDictCategoryMapper.insertIdwResourceDictCategory(category);
                    category.setAncestors(category.getAncestors() + "," + category.getCategoryId());
                    idwResourceDictCategoryMapper.updateIdwResourceDictCategory(category);
                }
            }
        }
        return "操作完成！";

        /*List<IdwResource> resourceList = testMapper.selectTDocument();
        for (IdwResource resource : resourceList) {
            if (StringUtils.isNotBlank(resource.getPublishDate())) {
                String publishDate = DateUtils.updateDateSeparator(resource.getPublishDate());
                if (StringUtils.isNotBlank(publishDate)) {
                    resource.setPublishDate(publishDate);
                }
            }
            String thumbnail = resource.getThumbnail();
            if (StringUtils.isNotBlank(thumbnail)) {
                String filePath = thumbnail.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile());
                if (!new File(filePath).exists()) {
                    resource.setThumbnail("");
                }
            }
            String storagePath = resource.getStoragePath();
            if (StringUtils.isNotBlank(storagePath)) {
                String filePath = storagePath.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile());
                File file = new File(filePath);
                if (!file.exists()) {
                    errStr = StringUtils.isNotBlank(errStr) ? errStr : (errStr + "，");
                    errStr += "ID为：" + resource.getUpdateBy() + "，文献不存在！";
                } else {
                    resource.setFileName(resource.getNameCn() + ".pdf");
                    int pages = 0;
                    String fileName = filePath.substring(filePath.lastIndexOf("/") + 1, filePath.length());
                    String fileSuffix = fileName.substring(fileName.indexOf(".") + 1, fileName.length());
                    try {
                        if (fileSuffix.equalsIgnoreCase("pdf")) {
                            PDDocument pdfReader = PDDocument.load(new File((filePath)));
                            pages = pdfReader.getNumberOfPages();
                            pdfReader.close();
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    resource.setPageNum(pages);
                    resource.setFileType("pdf");
                }
            }
        }
        if (StringUtils.isBlank(errStr)) {
            for (IdwResource resource : resourceList) {
                idwResourceMapper.insertIdwResource(resource);
            }
        }
        return StringUtils.isNotBlank(errStr) ? errStr : "操作完成！";*/
    }

    /**
     * 更新资源库标签
     *
     * @return 结果
     */
    public String updateResouceTags() {
        //lhzz 共8304个文献
        /*int nullDataCount = 0;
        int count = 0;
        List<IdwResourceCategory> resourceCategoryList = testMapper.selectTDocumentCategory();
        for (IdwResourceCategory resourceCategory : resourceCategoryList) {
            if (StringUtils.isNotBlank(resourceCategory.getCreateBy()) && StringUtils.isNotBlank(resourceCategory.getUpdateBy())) {
                Long resourceId = testMapper.selectResourceIdByTDocumentId(resourceCategory.getCreateBy());
                Long categoryId = testMapper.selectCategoryIdByTCategoryId(resourceCategory.getUpdateBy());
                if (StringUtils.isNull(categoryId)) {
                    categoryId = testMapper.selectResourceCategoryIdByTDocumentId(resourceCategory.getCreateBy());
                }
                resourceCategory.setResourceId(resourceId);
                resourceCategory.setCategoryId(categoryId);
                resourceCategory.setCreateBy("联合作战");
                resourceCategory.setCreateTime(DateUtils.getNowDate());
                idwResourceCategoryMapper.insertIdwResourceCategory(resourceCategory);
                count++;
            } else {
                nullDataCount++;
                System.out.println("空数据!");
            }
        }
        return "操作完成！，共：" + count + "条数据，" + nullDataCount + "条空数据";*/
        List<IdwResource> resourceList = testMapper.selectResourceList();
        for (IdwResource resource : resourceList) {
            List<String> categoryList = testMapper.selectResourceCategoryByResourceId(resource.getResourceId());
            String categoryNames = categoryList.stream().distinct().collect(Collectors.joining(","));
            testMapper.updateResourceTagsByResourceId(resource.getResourceId(), categoryNames);
        }
        return "操作完成！";
    }

    /**
     * 修改机构关系ID
     *
     * @return 结果
     */
    public String updateOrgRelationshipId() {
        List<IdwOrgRelationship> orgRelationshipList = testMapper.selectAllOrgRelationship();
        for (IdwOrgRelationship relationship : orgRelationshipList) {
            long id = SnowIdUtils.uniqueLong();// 获取雪花id
            testMapper.updateRelationshipId(relationship.getRelationshipId(), id);
            testMapper.updateRelationshipParentId(relationship.getRelationshipId(), id);
        }
        return "操作完成！";
    }

    /**
     * 修改机构编码-解决机构编码冲突问题
     *
     * @return 结果
     */
    public String updateOrgCode() {
        String newOrgCode = "O10000";
        String[] orgCodes = "O03001,O03002,O03003,O03004,O03005,O03006,O03008,O03007,O03009,O03010,O00212".split(",");
        for (int i = 0; i < orgCodes.length; i++) {
            Integer orgCodeNumber = Integer.valueOf(newOrgCode.substring(1));
            newOrgCode = "W" + String.format("%06d", orgCodeNumber + 1);
            String orgCode = orgCodes[i];
            testMapper.updateOrgOrgCode(orgCode, newOrgCode);
            testMapper.updateOrgDevelopmentHistoryOrgCode(orgCode, newOrgCode);
            testMapper.updateOrgDocumentOrgCode(orgCode, newOrgCode);
            testMapper.updateOrgFacilitiesOrgCode(orgCode, newOrgCode);
            testMapper.updateOrgFinancialOrgCode(orgCode, newOrgCode);
            testMapper.updateOrgNewsOrgCode(orgCode, newOrgCode);
            testMapper.updateOrgProductOrgCode(orgCode, newOrgCode);
            testMapper.updateMultimediaOrgCode(orgCode, newOrgCode);
            testMapper.updateOrgProjectOrgCode(orgCode, newOrgCode);
            testMapper.updateOrgRelationshipOrgCode(orgCode, newOrgCode);
            testMapper.updateOrgRelationshipParentCode(orgCode, newOrgCode);
            testMapper.updateOrgRelationshipAncestors(orgCode, newOrgCode);
            testMapper.amendOrgStaffOrgCode(orgCode, newOrgCode);
        }
        return "操作完成！";
    }

    /**
     * 技术领域相关
     *
     * @return 结果
     */
    @Override
    public String technosphere() {
        technosphereValuationAncestors(0);
        return "操作完成！";
    }

    public void technosphereValuationAncestors(Integer parentId) {
        List<IdwTechnosphere> technosphereList = testMapper.selectTechnosphereByParentId(parentId);
        if (technosphereList.size() > 0) {
            for (IdwTechnosphere technosphere : technosphereList) {
                if (parentId != 0) {
                    Technosphere parentTechnosphere = testMapper.selectTechnosphereById(technosphere.getParentId());
                    technosphere.setAncestors(parentTechnosphere.getAncestors() + "," + parentTechnosphere.getId());
                    technosphere.setLevel(parentTechnosphere.getLevel() + 1);
                    testMapper.updateIdwTechnosphere(technosphere);
                }
                technosphereValuationAncestors(technosphere.getId().intValue());
            }
        }
    }

    /**
     * 外台军机构关系迁移
     */
    /*private void wtjRelationshipMigration() {
        List<IdwOrgStructure> orgRelationships = testMapper.selectTopOrgReturnStructure();
        for (IdwOrgStructure relationship : orgRelationships) {
            //顶层机构关系
            String topOrgCode = relationship.getOrgCode();
            wtjRelationshipMigrationByRelationship(relationship, topOrgCode);
        }
    }

    *//**
     * 迁移外台军机构关系
     *
     * @param parentRelationship 机构关系
     * @param topOrgCode         顶层机构编码
     *//*
    private void wtjRelationshipMigrationByRelationship(IdwOrgStructure parentRelationship, String topOrgCode) {
        List<IdwOrgStructure> structureList = testMapper.selectRelationshipByParentId(parentRelationship.getStructureId());
        for (IdwOrgStructure structure : structureList) {
            structure.setOrgCode(topOrgCode);
            IdwOrgStructure parentStructure = testMapper.selectOrgStructureByStructureId(structure.getParentId());
            if (StringUtils.isNull(parentStructure)) {
                structure.setParentId((long) 0);
                structure.setAncestors("0");
                structure.setLevel(1);
            } else {
                structure.setAncestors(parentStructure.getAncestors() + "," + parentStructure.getStructureId());
                structure.setLevel(parentStructure.getLevel() + 1);
            }
            if (StringUtils.isBlank(structure.getSource())) {
                structure.setSource("暂无");
            }
            if (StringUtils.isBlank(structure.getNameCn())) {
                structure.setNameCn(structure.getNameEn());
            }
            try {
                testMapper.insertOrgStructure(structure);
            } catch (Exception e) {
                e.printStackTrace();
            }
            List<IdwOrgStructure> list = testMapper.selectRelationshipByParentId(structure.getStructureId());
            if (list.size() > 0) {
                //存在子集，迁移
                wtjRelationshipMigrationByRelationship(structure, topOrgCode);
            }
        }
    }*/

    /**
     * 机构关系迁移
     */
  /*  public void relationshipMigration() {
        List<IdwOrgRelationship> orgRelationships = testMapper.selectTopOrg();
        for (IdwOrgRelationship relationship : orgRelationships) {
            String orgCode = relationship.getOrgCode();
            List<IdwOrgRelationship> relationshipList = testMapper.selectOrgRelationshipByParentCode(orgCode);
            for (IdwOrgRelationship orgRelationship : relationshipList) {
                orgRelationshipMigrationByOrgCode(orgCode, orgRelationship);
            }
        }
    }*/

    /**
     * 迁移机构关系
     *
     * @param relationship 机构关系
     */
    /*public void orgRelationshipMigrationByOrgCode(String topOrgCode, IdwOrgRelationship relationship) {
        String userName = ShiroUtils.getUserName();
        Date nowDate = DateUtils.getNowDate();
        List<IdwOrgRelationship> orgRelationships = testMapper.selectOrgRelationshipByParentCode(relationship.getParentCode());
        if (StringUtils.isNotNull(orgRelationships) && orgRelationships.size() > 0) {
            for (IdwOrgRelationship orgRelationship : orgRelationships) {
                IdwOrgStructure orgStructure = new IdwOrgStructure();
                IdwOrg org = idwOrgMapper.selectOrgByOrgCode(orgRelationship.getOrgCode());
                orgStructure.setOrgCode(topOrgCode);
                if (orgRelationship.getLevel() != 2) {
                    IdwOrg pOrg = idwOrgMapper.selectOrgByOrgCode(orgRelationship.getParentCode());
                    IdwOrgStructure pOrgStructure = testMapper.selectOrgStructureByOrgCodeAndOrgNameCn(topOrgCode, pOrg.getOrgNameCn());
                    if (StringUtils.isNotNull(pOrgStructure)) {
                        orgStructure.setParentId(pOrgStructure.getStructureId());
                        orgStructure.setAncestors(pOrgStructure.getAncestors() + "," + pOrgStructure.getStructureId());
                        orgStructure.setLevel(pOrgStructure.getLevel() + 1);
                    } else {
                        orgStructure.setParentId((long) 0);
                        orgStructure.setAncestors("0");
                        orgStructure.setLevel(1);
                    }
                } else {
                    orgStructure.setParentId((long) 0);
                    orgStructure.setAncestors("0");
                    orgStructure.setLevel(1);
                }
                try {
                    orgStructure.setNameCn(org.getOrgNameCn());
                } catch (Exception e) {
                    e.printStackTrace();
                }
                orgStructure.setNameEn(org.getOrgNameEn());
                orgStructure.setStructureOrgCode(org.getOrgCode());
                orgStructure.setAvatar(org.getAvatar());
                orgStructure.setProfileCn(org.getProfileCn());
                orgStructure.setProfileEn(org.getProfileEn());
                orgStructure.setSource(org.getSource());
                orgStructure.setCreateBy(userName);
                orgStructure.setCreateTime(nowDate);
                IdwOrgStructure oldOrgStructure = testMapper.selectOrgStructureByOrgCodeAndOrgNameCn(topOrgCode, orgStructure.getNameCn());
                if (StringUtils.isNull(oldOrgStructure)) {
                    if (StringUtils.isBlank(orgStructure.getSource())) {
                        orgStructure.setSource("暂无");
                    }
                    testMapper.insertOrgStructure(orgStructure);
                }
                List<IdwOrgRelationship> relationships = testMapper.selectOrgRelationshipByParentCode(relationship.getOrgCode());
                if (StringUtils.isNotNull(relationships) && relationships.size() > 0) {
                    for (IdwOrgRelationship idwOrgRelationship : relationships) {
                        IdwOrgStructure structure = new IdwOrgStructure();
                        IdwOrg o = idwOrgMapper.selectOrgByOrgCode(idwOrgRelationship.getOrgCode());
                        IdwOrg parentOrg = idwOrgMapper.selectOrgByOrgCode(idwOrgRelationship.getParentCode());
                        IdwOrgStructure parentOrgStructure = testMapper.selectOrgStructureByOrgCodeAndOrgNameCn(topOrgCode, parentOrg.getOrgNameCn());
                        if (idwOrgRelationship.getLevel() != 2) {
                            IdwOrg pOrg = idwOrgMapper.selectOrgByOrgCode(idwOrgRelationship.getParentCode());
                            IdwOrgStructure pOrgStructure = testMapper.selectOrgStructureByOrgCodeAndOrgNameCn(topOrgCode, pOrg.getOrgNameCn());
                            orgStructure.setParentId(pOrgStructure.getStructureId());
                            orgStructure.setAncestors(pOrgStructure.getAncestors() + "," + pOrgStructure.getStructureId());
                            orgStructure.setLevel(pOrgStructure.getLevel() + 1);
                        } else {
                            orgStructure.setParentId((long) 0);
                            orgStructure.setAncestors("0");
                            orgStructure.setLevel(1);
                        }
                        structure.setOrgCode(topOrgCode);
                        structure.setParentId(parentOrgStructure.getStructureId());
                        structure.setLevel(parentOrgStructure.getLevel() + 1);
                        structure.setNameCn(o.getOrgNameCn());
                        structure.setNameEn(o.getOrgNameEn());
                        structure.setStructureOrgCode(o.getOrgCode());
                        structure.setAvatar(o.getAvatar());
                        structure.setProfileCn(o.getProfileCn());
                        structure.setProfileEn(o.getProfileEn());
                        structure.setSource(o.getSource());
                        structure.setCreateBy(userName);
                        structure.setCreateTime(nowDate);
                        orgRelationshipMigrationByOrgCode(topOrgCode, idwOrgRelationship);
                    }
                }
            }
        }
    }*/

    /**
     * 文件拷贝
     *
     * @param oldPath 文件绝对路径
     * @param newPath 新的文件绝对路径
     */
    public static void copyFile(String oldPath, String newPath) {
        try {
            int byteread = 0;
            File oldfile = new File(oldPath);
            File newFile = new File(newPath);
            //判断原文件是否存在 存在才进行拷贝
            if (oldfile.exists()) {
                //判断新文件路径中包含的所有文件夹是否存在
                if (!newFile.getParentFile().exists()) {
                    //如果文件夹不存在就新建文件夹
                    newFile.getParentFile().mkdirs();
                }
                InputStream inStream = new FileInputStream(oldPath);
                FileOutputStream fs = new FileOutputStream(newPath);
                byte[] buffer = new byte[1024];
                while ((byteread = inStream.read(buffer)) != -1) {
                    fs.write(buffer, 0, byteread);
                }
                inStream.close();
                fs.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 人员数据迁移
     *
     * @param migrationDatabase 需要迁移的数据库
     */
    public void peopleMigration(String migrationDatabase) {
        List<IdwPeopleMain> peopleList = testMapper.selectPeopleByDatabaseName(migrationDatabase);
        for (IdwPeopleMain people : peopleList) {
            IdwPeopleMain peopleMain = idwPeopleMainMapper.selectPeopleByPeopleCode(people.getPeopleCode());
            if (StringUtils.isNull(peopleMain)) {
                //人员不存在 直接导入
                people.setPeopleId(null);
                idwPeopleMainMapper.insertIdwPeopleMain(people);
            } else if (people.getUpdateTime().getTime() > peopleMain.getUpdateTime().getTime()) {
                //需迁移的数据为最新数据 更新
                idwPeopleMainMapper.updatePeople(people);
            }
            //教育经历
            List<IdwPeopleEducation> peopleEducationList = testMapper.selectPeopleEducationByDatabaseNameAndPeopleCode(migrationDatabase, people.getPeopleCode());
            for (IdwPeopleEducation peopleEducation : peopleEducationList) {
                IdwPeopleEducation oldPeopleEducation = peopleEducationMapper.selectIdwPeopleEducationByPeopleCodeAndCollegeAndMajor(peopleEducation.getPeopleCode(), peopleEducation.getCollege(), peopleEducation.getMajor(), peopleEducation.getDegree());
                if (StringUtils.isNull(oldPeopleEducation)) {
                    peopleEducationMapper.insertPeopleEducation(peopleEducation);
                }
            }
            //社交媒体账号
            List<IdwPeopleSocialAccount> peopleSocialAccountList = testMapper.selectPeopleSocialAccountByDatabaseNameAndPeopleCode(migrationDatabase, people.getPeopleCode());
            for (IdwPeopleSocialAccount peopleSocialAccount : peopleSocialAccountList) {
                IdwPeopleSocialAccount oldPeopleSocialAccount = peopleSocialAccountMapper.selectByPeopleCodeAndMediaTypeAndAccount(peopleSocialAccount.getPeopleCode(), peopleSocialAccount.getMediaType(), peopleSocialAccount.getAccount());
                if (StringUtils.isNull(oldPeopleSocialAccount)) {
                    peopleSocialAccountMapper.insertIdwPeopleSocialAccount(peopleSocialAccount);
                }
            }
            //社交媒体
            List<IdwPeopleSocialMedia> peopleSocialMediaList = testMapper.selectPeopleSocialMediaByDatabaseNameAndPeopleCode(migrationDatabase, people.getPeopleCode());
            for (IdwPeopleSocialMedia peopleSocialMedia : peopleSocialMediaList) {
                IdwPeopleSocialMedia oldPeopleSocialMedia = peopleSocialMediaMapper.selectByPeopleCodeAndMediaTypeAndAccountAndPublishDateAndContent(peopleSocialMedia.getPeopleCode(), peopleSocialMedia.getMediaType(), peopleSocialMedia.getAccount(), peopleSocialMedia.getPublishDate(), peopleSocialMedia.getContent());
                if (StringUtils.isNull(oldPeopleSocialMedia)) {
                    peopleSocialMediaMapper.insertIdwPeopleSocialMedia(peopleSocialMedia);
                }
            }
            //新闻报道
            List<IdwPeopleNews> peopleNewsList = testMapper.selectPeopleNewsByDatabaseNameAndPeopleCode(migrationDatabase, people.getPeopleCode());
            for (IdwPeopleNews peopleNews : peopleNewsList) {
                IdwPeopleNews oldPeopleNews = peopleNewsMapper.selectByPeopleCodeAndTitleAndDate(peopleNews.getType(), peopleNews.getPeopleCode(), peopleNews.getTitle(), peopleNews.getPublishDate());
                if (StringUtils.isNull(oldPeopleNews)) {
                    peopleNewsMapper.insertIdwPeopleNews(peopleNews);
                }
            }
            //工作经历
            List<IdwPeopleWorkExperience> peopleWorkExperienceList = testMapper.selectPeopleWorkExperienceByDatabaseNameAndPeopleCode(migrationDatabase, people.getPeopleCode());
            for (IdwPeopleWorkExperience peopleWorkExperience : peopleWorkExperienceList) {
                IdwPeopleWorkExperience oldPeopleWorkExperience = peopleWorkExperienceMapper.selectIdwPeopleWorkExperienceByPeopleCodeAndCompanyNameAndStartDate(peopleWorkExperience.getPeopleCode(), peopleWorkExperience.getCompanyName(), peopleWorkExperience.getTitle(), peopleWorkExperience.getStartDate());
                if (StringUtils.isNull(oldPeopleWorkExperience)) {
                    peopleWorkExperienceMapper.insertIdwPeopleWorkExperience(peopleWorkExperience);
                }
            }
            //荣誉奖项
            List<IdwPeopleHonor> peopleHonorList = testMapper.selectPeopleHonorByDatabaseNameAndPeopleCode(migrationDatabase, people.getPeopleCode());
            for (IdwPeopleHonor peopleHonor : peopleHonorList) {
                IdwPeopleHonor oldPeopleHonor = peopleHonorMapper.verifyPeopleHonor(peopleHonor.getPeopleCode(), peopleHonor.getTitle(), peopleHonor.getIssueDate(), peopleHonor.getIssuer());
                if (StringUtils.isNull(oldPeopleHonor)) {
                    peopleHonorMapper.insertIdwPeopleHonor(peopleHonor);
                }
            }
            //发表作品
            List<IdwPeoplePublishedWorks> peoplePublishedWorksList = testMapper.selectPeoplePublishedWorksByDatabaseNameAndPeopleCode(migrationDatabase, people.getPeopleCode());
            for (IdwPeoplePublishedWorks peoplePublishedWorks : peoplePublishedWorksList) {
                IdwPeoplePublishedWorks oldPeoplePublishedWorks = peoplePublishedWorksMapper.selectByPeopleCodeAndJournalNameAndPublicationDate(peoplePublishedWorks.getPeopleCode(), peoplePublishedWorks.getTitleCn(), peoplePublishedWorks.getJournalName(), peoplePublishedWorks.getPublicationDate());
                if (StringUtils.isNull(oldPeoplePublishedWorks)) {
                    Long worksId = peoplePublishedWorks.getWorksId();
                    peoplePublishedWorksMapper.insertIdwPeoplePublishedWorks(peoplePublishedWorks);
                    //发表作品作者
                    List<IdwPeoplePublishedWorksAuthor> peoplePublishedWorksAuthorList = testMapper.selectPeoplePublishedWorksAuthorByDatabaseNameAndWorksId(migrationDatabase, worksId);
                    for (IdwPeoplePublishedWorksAuthor peoplePublishedWorksAuthor : peoplePublishedWorksAuthorList) {
                        peoplePublishedWorksAuthor.setWorksId(peoplePublishedWorks.getWorksId());
                        peoplePublishedWorksAuthorMapper.insertIdwPeoplePublishedWorksAuthor(peoplePublishedWorksAuthor);
                    }
                }
            }
            //专利发明
            List<IdwPeoplePatent> peoplePatentList = testMapper.selectPeoplePatentByDatabaseNameAndPeopleCode(migrationDatabase, people.getPeopleCode());
            for (IdwPeoplePatent peoplePatent : peoplePatentList) {
                IdwPeoplePatent oldPeoplePatent = peoplePatentMapper.selectByPeopleCodeAndApplyNumber(peoplePatent.getPeopleCode(), peoplePatent.getApplyNumber());
                if (StringUtils.isNull(oldPeoplePatent)) {
                    Long patentId = peoplePatent.getPatentId();
                    peoplePatentMapper.insertIdwPeoplePatent(peoplePatent);
                    //专利发明人
                    List<IdwPeoplePatentInventor> peoplePatentInventorList = testMapper.selectPeoplePatentInventorByDatabaseNameAndPatentId(migrationDatabase, patentId);
                    for (IdwPeoplePatentInventor peoplePatentInventor : peoplePatentInventorList) {
                        peoplePatentInventor.setPatentId(peoplePatent.getPatentId());
                        peoplePatentInventorMapper.insertIdwPeoplePatentInventor(peoplePatentInventor);
                    }
                }
            }
            //人员关系
            List<IdwPeopleRelationship> peopleRelationshipList = testMapper.selectPeopleRelationshipByDatabaseNameAndPeopleCode(migrationDatabase, people.getPeopleCode());
            for (IdwPeopleRelationship peopleRelationship : peopleRelationshipList) {
                IdwPeopleRelationship oldPeopleRelationship = peopleRelationshipMapper.selectByPeopleCodeAndPeopleNameAndRelationship(peopleRelationship.getPeopleCode(), peopleRelationship.getRelatedPeopleName(), peopleRelationship.getRelationship());
                if (StringUtils.isNull(oldPeopleRelationship)) {
                    peopleRelationshipMapper.insertIdwPeopleRelationship(peopleRelationship);
                }
            }
            //工作成果
            List<IdwPeopleAchievement> peopleAchievementList = testMapper.selectPeopleAchievementByDatabaseNameAndPeopleCode(migrationDatabase, people.getPeopleCode());
            for (IdwPeopleAchievement peopleAchievement : peopleAchievementList) {
                IdwPeopleAchievement oldPeopleAchievement = peopleAchievementMapper.selectByPeopleCodeAndNameAndDateAndLevel(peopleAchievement.getPeopleCode(), peopleAchievement.getName(), peopleAchievement.getAchievementDate(), peopleAchievement.getLevel(), peopleAchievement.getAchievementType());
                if (StringUtils.isNull(oldPeopleAchievement)) {
                    peopleAchievementMapper.insertIdwPeopleAchievement(peopleAchievement);
                }
            }
            //图片视频
            List<IdwMultimedia> multimediaList = testMapper.selectMultimediaByDatabaseNameAndBusinessTypeAndAssociationId(migrationDatabase, "personnel", people.getPeopleCode());
            for (IdwMultimedia multimedia : multimediaList) {
                IdwMultimedia oldMultimedia = testMapper.selectMultimediaByDatabaseNameAndBusinessTypeAndAssociatioIdAndTitle(migrationDatabase, "personnel", people.getPeopleCode(), multimedia.getTitle());
                if (StringUtils.isNull(oldMultimedia)) {
                    multimediaMapper.insertIdwMultimedia(multimedia);
                }
            }
        }
    }

    /**
     * 格式化发布作品机构名称
     *
     * @return 结果
     */
    public String splitAwardOrgName() {
        String[] excludeNames = "Industrial Technology Research Institute, Taiwan@@@@Activated Research Company, LLCADVA Optical Networking, IBMAlcan International, BomemAlchimer SA, PublitekANSYS, Inc.ANDE, A Division of NetBioATS-MER, L.L.C. (dba Materials and Electrochemical Research Corp.)ATS-MER, LLCAurora SFC Systems, Inc.Babcock &amp; Wilcox Technical Services Y-12, LLCBioVentures, Research GeneticsBlack Box Network Services, Fiddlehead Inc.Bridger Photonics, Inc.BroadSoft, Inc.Bruker AXS, Inc.C Technologies, Inc.Cambridge Research and Instrumentation, Inc.CHZ Technologies, LLCDermaClip US, LLCEpic Communications, Inc.ETHICON, Inc.Euclid TechLabs, LLCFallbrook Technologies, Inc.FLIR Systems, IncGenomatica, Inc.Gigajot Technology, Inc.Global Graphene Group, Inc.GrafTech International, Inc.Hitachi, Ltd.HP, Inc.HRL Laboratories, LLC.Hybrid Plastics, Inc.IN10DID, Inc.Iowa State University, AmesKeithley Instruments, Inc.Keysight Technologies, Inc.KMLabs, Inc.Labcyte, Inc.Lake Shore Cryotronics, Inc.Lightning Packs, LLCLytron, Inc.ManTech SRS Technologies, Inc.Matsushita Electric Industrial Co., Ltd.MEI Micro, Inc.Membrion, Inc.Mettler-Toledo, Inc.Nalu Medical, Inc.National Security Technologies, LLCNICCA Chemical Co., Ltd.Nortis, Inc.NSR Technologies, Inc.OG Technologies, Inc.Opcondys, Inc.Orthocare Innovations, LLCOsteonovus, Inc.Pendar Technologies, LLC.PerkinElmer, Inc.Phenomenex, Inc.Picarro, Inc.Praxair, Inc.Q-Carbon, LLCRadiation Detection Technologies, Inc.Radiation Monitoring Devices, Inc.RedWave Energy, Inc.ReliaCoat Technologies, LLCSpheryx, Inc.Tekscan, Inc.Tescan Orsay Holding, a.s.Tessera Technologies, Inc.Toyota Central R&amp;D Labs., Inc.Tru-Design, LLCViance, LLCVisual Pathways, Inc.".split("@@@@");
        List<RdAwardOrg> patentOrgList = testMapper.selectAwardMuchName(excludeNames);
        for (RdAwardOrg rdAwardOrg : patentOrgList) {
            String[] names = rdAwardOrg.getName().split(",");
            for (int i = 0; i < names.length; i++) {
                String name = names[i];
                int nextIndex = i + 1;
                if (nextIndex < names.length) {
                    String nextName = names[nextIndex].trim();
                    if ("Inc.".equals(nextName) || "LLC".equals(nextName) || "L.L.C.".equals(nextName) || "Ltd.".equals(nextName)) {
                        i++;
                        name = name + ", " + nextName;
                    }
                }
                if (i == 0) {
                    testMapper.updateAwardById(rdAwardOrg.getId(), name.trim());
                } else {
                    testMapper.insertAward(rdAwardOrg.getAwardId(), name.trim());
                }
            }
        }
        return "发布作品机构拆分完成！";
    }

    /**
     * 格式化专利机构名称
     *
     * @return 结果
     */
    public String splitPatentOrgName() {
        List<PatentOrg> patentOrgList = testMapper.selectPatentMuchName();
        for (PatentOrg patentOrg : patentOrgList) {
            String[] names = patentOrg.getName().split(" {44}");
            for (int i = 0; i < names.length; i++) {
                if (i == 0) {
                    testMapper.updatePatentOrgById(patentOrg.getId(), names[i].trim());
                } else {
                    testMapper.insertPatentOrg(patentOrg.getPatentId(), names[i].trim());
                }
            }
        }
        return "专利机构拆分完成！";
    }

    /**
     * 提取文件内容
     *
     * @param file 文件
     */
    public void extractFileContent(File file) {
        File[] files = file.listFiles();
        if (files != null && files.length > 0) {
            //文件夹
            for (File f : files) {
                extractFileContent(f);
            }
        } else {
            String fileName = file.getName();
            //获取文件后缀
            String postfix = fileName.substring(fileName.lastIndexOf(".") + 1);

            // 将文件记录存储到数据库中
            long id = SnowIdUtils.uniqueLong();// 获取雪花id
            OriginalDocument originalDocument = new OriginalDocument();
            originalDocument.setId(id);
            originalDocument.setTitle(fileName);
            originalDocument.setType(postfix);
            originalDocument.setFilePath(file.getAbsolutePath());
            originalDocument.setUrl("有无人和文献数据清单");
            originalDocument.setCollectTime(DateUtils.getNowDate());
            if (postfix.equals("pdf")) {
                try {
                    FileInputStream inputStream = new FileInputStream(file);
                    PDDocument document = PDDocument.load(inputStream);
                    document.getClass();
                    //使用PDFTextStripper 工具
                    PDFTextStripper tStripper = new PDFTextStripper();
                    //设置文本排序，有规则输出
                    tStripper.setSortByPosition(true);
                    //获取所有文字信息
                    String content = tStripper.getText(document);
                    index++;
                    if (StringUtils.isBlank(contents)) {
                        contents = id + "\t" + content.replaceAll("[\\t\\n\\r]", "");
                    } else {
                        contents += "\r\n" + id + "\t" + content.replaceAll("[\\t\\n\\r]", "");
                    }
                    originalDocument.setContent(content);
                    testMapper.insertOriginalDocument(originalDocument);
                    inputStream.close();
                    document.close();
                } catch (Exception e) {
                    e.printStackTrace();
                }
            } else if (postfix.equals("mhtml")) {
                String content = Html2Text(readHTML(file.getAbsolutePath()));
                index++;
                if (StringUtils.isBlank(contents)) {
                    contents = id + "\t" + content.replaceAll("[\\t\\n\\r]", "");
                } else {
                    contents += "\r\n" + id + "\t" + content.replaceAll("[\\t\\n\\r]", "");
                }
                originalDocument.setContent(content);
                testMapper.insertOriginalDocument(originalDocument);
            }
        }
    }

    public static String readHTML(String filepath) {
        StringBuffer htmlSb = new StringBuffer();

        if (!new File(filepath).exists()) {
            return htmlSb.toString();
        }
        BufferedReader br = null;
        try {
            br = new BufferedReader(new InputStreamReader(new FileInputStream(filepath)));
            while (br.ready()) {
                htmlSb.append(br.readLine());
            }

        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (br != null) {
                try {
                    br.close();
                } catch (IOException e) {
                    // TODO Auto-generated catch block
                    e.printStackTrace();
                }
            }
        }
        return htmlSb.toString();
    }

    public static String Html2Text(String inputString) {
        String htmlStr = inputString; //含html标签的字符串
        String textStr = "";
        java.util.regex.Pattern p_script;
        java.util.regex.Matcher m_script;
        java.util.regex.Pattern p_style;
        java.util.regex.Matcher m_style;
        java.util.regex.Pattern p_html;
        java.util.regex.Matcher m_html;
        try {
            String regEx_script = "<[\\s]*?script[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?script[\\s]*?>"; //定义script的正则表达式{或<script[^>]*?>[\\s\\S]*?<\\/script> }
            String regEx_style = "<[\\s]*?style[^>]*?>[\\s\\S]*?<[\\s]*?\\/[\\s]*?style[\\s]*?>"; //定义style的正则表达式{或<style[^>]*?>[\\s\\S]*?<\\/style> }
            String regEx_html = "<[^>]+>"; //定义HTML标签的正则表达式
            p_script = Pattern.compile(regEx_script, Pattern.CASE_INSENSITIVE);
            m_script = p_script.matcher(htmlStr);
            htmlStr = m_script.replaceAll(""); //过滤script标签
            p_style = Pattern.compile(regEx_style, Pattern.CASE_INSENSITIVE);
            m_style = p_style.matcher(htmlStr);
            htmlStr = m_style.replaceAll(""); //过滤style标签
            p_html = Pattern.compile(regEx_html, Pattern.CASE_INSENSITIVE);
            m_html = p_html.matcher(htmlStr);
            htmlStr = m_html.replaceAll(""); //过滤html标签
            textStr = htmlStr;
        } catch (Exception e) {
            e.printStackTrace();
        }
        return textStr;//返回文本字符串
    }

    /**
     * 将内容存入文本文件
     *
     * @param path    文件路径
     * @param content 文件内容
     */
    private static void write2File(String path, String content) {
        try {
            FileOutputStream writerStream = new FileOutputStream(path);
            BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(writerStream, "UTF-8"));
            writer.write(content);
            writer.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 维护机构关系祖籍列表字段
     *
     * @param parentCode      上级机构编码
     * @param parentAncestors 祖籍列表
     * @return 结果
     */
    public String vindicateOrgAncestors(Long parentId, String parentCode, String parentAncestors) {
        /*List<OrgRelationship> orgRelationshipList = testMapper.selectOrgRelByParentCode(parentCode);
        for (OrgRelationship orgRelationship : orgRelationshipList) {
            orgRelationship.setParentId(parentId);
            orgRelationship.setAncestors(parentAncestors + "," + parentCode);
            orgRelationship.setLevel((long) orgRelationship.getAncestors().split(",").length);
            idwOrgRelationshipMapper.updateById(orgRelationship);
            vindicateOrgAncestors(orgRelationship.getRelationshipId(), orgRelationship.getOrgCode(), orgRelationship.getAncestors());
        }*/
        return "操作成功！";
    }

    /**
     * 格式化装备规格 解决装备规格将指标值当做指标问题
     */
    public void formattingSpecifications() {
        List<IdwWeaponrySpecifications> weaponrySpecificationsList = testMapper.selectWeaponrySpecificationsCategory();
        for (IdwWeaponrySpecifications weaponrySpecifications : weaponrySpecificationsList) {
            List<IdwWeaponrySpecifications> specificationsList = testMapper.selectWeaponrySpecificationsByParentId(weaponrySpecifications.getSpecificationsId());
            int count = 0;
            String specificationsNames = "";
            String specificationsIds = "";
            for (IdwWeaponrySpecifications specifications : specificationsList) {
                if (StringUtils.isBlank(specificationsNames)) {
                    specificationsNames = StringUtils.isNotBlank(specifications.getNameCn()) ? specifications.getNameCn() : specifications.getNameEn();
                    specificationsIds = String.valueOf(specifications.getSpecificationsId());
                } else {
                    specificationsNames += "\n" + (StringUtils.isNotBlank(specifications.getNameCn()) ? specifications.getNameCn() : specifications.getNameEn());
                    specificationsIds += "," + String.valueOf(specifications.getSpecificationsId());
                }
                if (StringUtils.isBlank(specifications.getValue())) {
                    count++;
                }
            }
            if (count == specificationsList.size()) {
                //所有指标值为空 将指标名称收集后赋值到分类value字段中 并将分类改为指标
                weaponrySpecifications.setValue(specificationsNames);
                weaponrySpecifications.setType("指标");
                weaponrySpecifications.setUpdateBy("格式化数据");
                idwWeaponrySpecificationsMapper.updateIdwWeaponrySpecifications(weaponrySpecifications);
                idwWeaponrySpecificationsMapper.deleteByIds(specificationsIds.split(","), "admin");
            }
        }
    }

    /**
     * 根据pdf文件首页生成图片
     *
     * @param filePath 要转图片的pdf地址
     * @return 生成图片路径
     * @throws PDFSecurityException
     * @throws PDFException
     * @throws IOException
     * @throws InterruptedException
     */
    public static String pdfToImage(String filePath) throws PDFSecurityException, PDFException, IOException, InterruptedException {
        if (filePath.contains(Constants.RESOURCE_PREFIX)) {
            filePath = filePath.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile());
        }
        String imgPath = "";
        File pdfFile = new File(filePath);
        if (!pdfFile.exists()) {
            return "";
        }
        Document document = new Document();
        document.setFile(filePath);
        float scale = 1f;//缩放比例
        float rotation = 0f;//旋转角度
        if (document.getNumberOfPages() > 0) {
            BufferedImage image = (BufferedImage) document.getPageImage(0, GraphicsRenderingHints.SCREEN, org.icepdf.core.pobjects.Page.BOUNDARY_CROPBOX, rotation, scale);
            try {
                String parentPath = pdfFile.getParent().replace("\\", "/") + "/";
                String pdfFileName = pdfFile.getName();
                String newFilePath = parentPath + pdfFileName.replace(".pdf", "") + ".jpg";
                imgPath = newFilePath;
                File file = new File(newFilePath);//生成的图片地址
                if (!file.exists()) {
                    ImageIO.write(image, "jpg", file);
                    //缩略图路径
                    String thumbnailImgPath = parentPath + "thumbnail-" + pdfFileName.replace(".pdf", "") + ".jpg";
                    imgPath = thumbnailImgPath;
                    if (!new File(thumbnailImgPath).exists()) {
                        FileOutputStream fileOutputStream = new FileOutputStream(thumbnailImgPath);
                        Thumbnails.of(file)//这一段代码是用来把图片压缩的，不需要可以直接删掉
                                .scale(0.5f) //图片大小（长宽）压缩比例 从0-1，1表示原图
                                .outputQuality(0.5f) //图片质量压缩比例 从0-1，越接近1质量越好
                                .toOutputStream(fileOutputStream);//压缩后的图片地址
                        //file.delete(); //这里是用来删除没有压缩的图片，
                        fileOutputStream.close();
                    }
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            image.flush();
        }
        document.dispose();
        return imgPath;
    }

    /**
     * 判断字符串中是否包含中文
     *
     * @param str 待校验字符串
     * @return 是否为中文
     */
    public static boolean isContainChinese(String str) {
        Pattern p = Pattern.compile("[\u4E00-\u9FA5|\\！|\\，|\\。|\\（|\\）|\\《|\\》|\\“|\\”|\\？|\\：|\\；|\\【|\\】]");
        Matcher m = p.matcher(str);
        if (m.find()) {
            return true;
        }
        return false;
    }

    /**
     * 重构外台军机构关系
     *
     * @return 结果
     */
    public String reconsitutionOrgRelationshipByFile() {
       /* Date nowDate = DateUtils.getNowDate();
        int orgCount = 0;
        int orgRelationshipCount = 0;
        try {
            ExcelUtil<Organization> util = new ExcelUtil<Organization>(Organization.class);
            InputStream is = new FileInputStream(new File("F:/HCZY/Excel导入完成备份/待处理/2022-04-27 外台军编制架构重构/外台编制（校对）0426.xlsx"));
            List<Organization> list = util.importExcel("Sheet1", is);
            if (StringUtils.isNull(list) || list.size() < 1) {
                return "数据为空！";
            }
            int count = 0;
            for (Organization organization : list) {
                count++;
                //校验当前机构是否存在
                String orgName = idwOrgMapper.selectOrgNameByOrgCode(organization.getOrgCode(), false);
                if (StringUtils.isBlank(orgName)) {
                    //当前机构不存在 新增机构
                    IdwOrg org = new IdwOrg();
                    org.setOrgCode(organization.getOrgCode());
                    org.setUniqueCode(organization.getOrgCode());
                    org.setOrgNameCn(StringUtils.isNotBlank(organization.getOrgNameCn()) ? organization.getOrgNameCn() : organization.getOrgNameEn());
                    if (StringUtils.isBlank(org.getOrgNameCn())) {
                        return "第" + count + "行机构名称为空！";
                    }
                    org.setOrgNameEn(organization.getOrgNameEn());
                    org.setCountry("US");
                    org.setOrgType("国防单位");
                    org.setOrgTypeAlias("国防单位");
                    org.setOrderNum(idwOrgMapper.selectMaxOrderNum("国防单位"));
                    org.setSource(StringUtils.isNotBlank(organization.getSource()) ? organization.getSource() : "待完善");
                    idwOrgMapper.insertIdwOrg(org);
                    orgCount++;
                } else {
                    testMapper.updateOrgNameByOrgCode(organization.getOrgCode(), organization.getOrgNameCn(), organization.getOrgNameEn());
                }
                //新增机构关系
                if (StringUtils.isBlank(organization.getParentCodeOne())) {
                    IdwOrgRelationship orgRelationship = new IdwOrgRelationship();
                    orgRelationship.setOrgCode(organization.getOrgCode());
                    orgRelationship.setParentCode("0");
                    orgRelationship.setParentId((long) -1);
                    orgRelationship.setAncestors("0");
                    orgRelationship.setOrgType("国防单位");
                    orgRelationship.setLevel((long) 1);
                    orgRelationship.setAuditPhase("not_submit");
                    orgRelationship.setAssignUserId((long) 1);
                    orgRelationship.setCreateBy("admin");
                    orgRelationship.setCreateTime(nowDate);
                    IdwOrgRelationship oldOrgRelationship = idwOrgRelationshipMapper.selectRelationship(orgRelationship.getOrgCode(), orgRelationship.getParentCode(), orgRelationship.getAncestors());
                    if (StringUtils.isNull(oldOrgRelationship)) {
                        idwOrgRelationshipMapper.insert(orgRelationship);
                        orgRelationshipCount++;
                    }
                } else {
                    List<IdwOrgRelationship> parentOneOrgRelationshipList = idwOrgRelationshipMapper.selectByOrgCodeAndOrgType(organization.getParentCodeOne(), "国防单位");
                    for (IdwOrgRelationship parentOrgRelationship : parentOneOrgRelationshipList) {
                        IdwOrgRelationship orgRelationship = new IdwOrgRelationship();
                        orgRelationship.setOrgCode(organization.getOrgCode());
                        orgRelationship.setParentCode(parentOrgRelationship.getOrgCode());
                        orgRelationship.setParentId(parentOrgRelationship.getRelationshipId());
                        orgRelationship.setAncestors(parentOrgRelationship.getAncestors() + "," + parentOrgRelationship.getOrgCode());
                        orgRelationship.setOrgType("国防单位");
                        orgRelationship.setLevel((long) orgRelationship.getAncestors().split(",").length);
                        orgRelationship.setAuditPhase("not_submit");
                        orgRelationship.setAssignUserId((long) 1);
                        orgRelationship.setCreateBy("admin");
                        orgRelationship.setCreateTime(nowDate);
                        IdwOrgRelationship oldOrgRelationship = idwOrgRelationshipMapper.selectRelationship(orgRelationship.getOrgCode(), orgRelationship.getParentCode(), orgRelationship.getAncestors());
                        if (StringUtils.isNull(oldOrgRelationship)) {
                            idwOrgRelationshipMapper.insert(orgRelationship);
                            orgRelationshipCount++;
                        }
                    }
                    if (StringUtils.isNotBlank(organization.getParentCodeTwo())) {
                        List<IdwOrgRelationship> parentTwoOrgRelationshipList = idwOrgRelationshipMapper.selectByOrgCodeAndOrgType(organization.getParentCodeTwo(), "国防单位");
                        for (IdwOrgRelationship parentOrgRelationship : parentTwoOrgRelationshipList) {
                            IdwOrgRelationship orgRelationship = new IdwOrgRelationship();
                            orgRelationship.setOrgCode(organization.getOrgCode());
                            orgRelationship.setParentCode(parentOrgRelationship.getOrgCode());
                            orgRelationship.setParentId(parentOrgRelationship.getRelationshipId());
                            orgRelationship.setAncestors(parentOrgRelationship.getAncestors() + "," + parentOrgRelationship.getOrgCode());
                            orgRelationship.setOrgType("国防单位");
                            orgRelationship.setLevel((long) orgRelationship.getAncestors().split(",").length);
                            orgRelationship.setAuditPhase("not_submit");
                            orgRelationship.setAssignUserId((long) 1);
                            orgRelationship.setCreateBy("admin");
                            orgRelationship.setCreateTime(nowDate);
                            IdwOrgRelationship oldOrgRelationship = idwOrgRelationshipMapper.selectRelationship(orgRelationship.getOrgCode(), orgRelationship.getParentCode(), orgRelationship.getAncestors());
                            if (StringUtils.isNull(oldOrgRelationship)) {
                                idwOrgRelationshipMapper.insert(orgRelationship);
                                orgRelationshipCount++;
                            }
                        }
                    }
                    if (StringUtils.isNotBlank(organization.getParentCodeThree())) {
                        List<IdwOrgRelationship> parentThreeOrgRelationshipList = idwOrgRelationshipMapper.selectByOrgCodeAndOrgType(organization.getParentCodeThree(), "国防单位");
                        for (IdwOrgRelationship parentOrgRelationship : parentThreeOrgRelationshipList) {
                            IdwOrgRelationship orgRelationship = new IdwOrgRelationship();
                            orgRelationship.setOrgCode(organization.getOrgCode());
                            orgRelationship.setParentCode(parentOrgRelationship.getOrgCode());
                            orgRelationship.setParentId(parentOrgRelationship.getRelationshipId());
                            orgRelationship.setAncestors(parentOrgRelationship.getAncestors() + "," + parentOrgRelationship.getOrgCode());
                            orgRelationship.setOrgType("国防单位");
                            orgRelationship.setLevel((long) orgRelationship.getAncestors().split(",").length);
                            orgRelationship.setAuditPhase("not_submit");
                            orgRelationship.setAssignUserId((long) 1);
                            orgRelationship.setCreateBy("admin");
                            orgRelationship.setCreateTime(nowDate);
                            IdwOrgRelationship oldOrgRelationship = idwOrgRelationshipMapper.selectRelationship(orgRelationship.getOrgCode(), orgRelationship.getParentCode(), orgRelationship.getAncestors());
                            if (StringUtils.isNull(oldOrgRelationship)) {
                                idwOrgRelationshipMapper.insert(orgRelationship);
                                orgRelationshipCount++;
                            }
                        }
                    }
                }
            }
            is.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "重构成功！共新增：" + orgCount + "个机构," + orgRelationshipCount + "条关系";*/
        return "";
    }

    public void orgStructureMigrate(String oldParentCode, Long newParentId) {
        //查询子集
       /* List<IdwOrgRelationship> orgRelationshipList = testMapper.selectAllOrgStructureMigrateByPatentOrgCode(oldParentCode);
        IdwOrgRelationship parentOrgRelationship = idwOrgRelationshipMapper.selectByRelationshipId(newParentId, false);
        for (IdwOrgRelationship orgRelationship : orgRelationshipList) {
            String oldOrgCode = orgRelationship.getOrgCode();
            long id = SnowIdUtils.uniqueLong();
            IdwOrg org = testMapper.selectOrgMigrateByOrgCode(oldOrgCode);
            //修改机构编码
            String orgCode = oldOrgCode + "TWTP";
            orgRelationship.setRelationshipId(id);//重新赋值ID
            orgRelationship.setOrgCode(orgCode);
            orgRelationship.setAncestors(parentOrgRelationship.getAncestors() + "," + parentOrgRelationship.getOrgCode());
            orgRelationship.setParentId(parentOrgRelationship.getRelationshipId());
            orgRelationship.setAuditPhase("not_submit");
            orgRelationship.setAssignUserId((long) 1);
            idwOrgRelationshipMapper.insert(orgRelationship);
            org.setOrgCode(orgCode);
            org.setUniqueCode(orgCode);
            idwOrgMapper.insertIdwOrg(org);
            orgStructureMigrate(oldOrgCode, id);
            orgStructureMigrate(oldOrgCode, id);
        }*/
    }

    /**
     * 处理机构关系
     *
     * @param people     人员
     * @param peopleList 所有人员
     */
    public void insertOrgRelationship(OriginalPeople people, List<OriginalPeople> peopleList) {
        List<IdwOrg> orgList = idwOrgMapper.selectOrgByOrgName(people.getOrgNameCn(), people.getOrgNameEn());
        if (orgList.size() > 0) {
            //机构存在 直接关联人员
            IdwOrg org = orgList.get(0);
            IdwOrgStaff orgStaff = idwOrgStaffMapper.selectByOrgCodeAndPeopleName(org.getOrgCode(), people.getNameCn(), people.getNameEn(), (long) 0);
            if (StringUtils.isNull(orgStaff)) {
                List<IdwPeopleMain> peopleMainList = idwPeopleMainMapper.selectByPeopleName(people.getNameCn(), people.getNameEn());
                IdwOrgStaff staff = new IdwOrgStaff();
                staff.setOrgCode(org.getOrgCode());
                if (peopleMainList.size() > 0) {
                    IdwPeopleMain peopleMain = peopleMainList.get(0);
                    staff.setPeopleCode(peopleMain.getPeopleCode());
                    staff.setPeopleNameCn(peopleMain.getNameCn());
                    staff.setPeopleNameEn(peopleMain.getNameEn());
                    staff.setAvatar(peopleMain.getAvatar());
                    staff.setPosition(peopleMain.getPost());
                    staff.setProfileCn(peopleMain.getProfileCn());
                    staff.setProfileEn(peopleMain.getProfileEn());
                } else {
                    staff.setPeopleNameCn(people.getNameCn());
                    staff.setPeopleNameEn(people.getNameEn());
                    staff.setPosition(people.getPost());
                    staff.setProfileEn(people.getDescription());
                }
                staff.setStatus("在职");
                staff.setSource(people.getSource());
                idwOrgStaffMapper.insertIdwOrgStaff(staff);
            }
        } else {
            //机构不存在 新增机构关系
            //查询上级机构
            String parentOrgName = people.getParentName();
            String parentOrgCode = "";//上级机构编码
            String ancestors = "";//祖籍列表
            if (StringUtils.isNotBlank(people.getParentName())) {
                List<IdwOrg> parentOrgList = idwOrgMapper.selectOrgByOrgName("", parentOrgName);
            } else {
                parentOrgCode = "0";
                ancestors = "0";
            }

        }
    }

    /**
     * 拷贝架构
     *
     * @param newParentOrgRelationship 新的父级机构关系
     * @param orgRelationshipList      机构关系列表
     */
    public void copyOrganizationalStructure(IdwOrgRelationship newParentOrgRelationship, List<IdwOrgRelationship> orgRelationshipList) {
       /* for (IdwOrgRelationship orgRelationship : orgRelationshipList) {
            String orgCode = orgRelationship.getOrgCode();
            String oldAncestors = orgRelationship.getAncestors();
            orgRelationship.setRelationshipId(null);
            orgRelationship.setParentId(newParentOrgRelationship.getRelationshipId());
            orgRelationship.setAncestors(newParentOrgRelationship.getAncestors() + "," + newParentOrgRelationship.getOrgCode());
            orgRelationship.setLevel((long) orgRelationship.getAncestors().split(",").length);
            idwOrgRelationshipMapper.insert(orgRelationship);
            //查询子集
            List<IdwOrgRelationship> children = testMapper.selectChildrenByOrgCodeAndAncestors(orgCode, oldAncestors);
            if (children.size() > 0) {
                copyOrganizationalStructure(orgRelationship, children);
            }
        }*/
    }

    /**
     * 修改文件名称为UUID
     *
     * @param filePath 文件路径
     * @return 新的文件路径
     */
    public static String updateFileName(String filePath) {
        String[] paths = filePath.split("/");
        //获取文件名称
        String fileName = paths[paths.length - 1];
        //获取文件后缀
        String postfix = fileName.substring(fileName.lastIndexOf(".") + 1);
        //生成新的文件名称
        String newFileName = UUID.randomUUID().toString() + "." + postfix;
        //获取文件决定路径
        String fileAbsolutePath = filePath.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile());
        File file = new File(fileAbsolutePath);
        String newFileAbsolutePath = fileAbsolutePath.replace(fileName, newFileName);
        if (file.exists()) {
            FileUploadUtils.copyFile(fileAbsolutePath, newFileAbsolutePath);
            return newFileAbsolutePath.replaceAll(WebdpConfig.getProfile(), Constants.RESOURCE_PREFIX);//重命名成功 返回新文件路径
        }
        //文件不存在 返回原文件路径
        return filePath;
    }

    /**
     * 根据装备规格ID排序子集
     *
     * @param specificationsId 装备规格ID
     */
    public void accordingSpecificationsIdSort(Long specificationsId) {
        List<IdwWeaponrySpecifications> weaponrySpecificationsList = testMapper.selectChildrenBySpecificationsId(specificationsId);
        int orderNum = 0;
        if (StringUtils.isNotNull(weaponrySpecificationsList) && weaponrySpecificationsList.size() > 0) {
            for (IdwWeaponrySpecifications weaponrySpecifications : weaponrySpecificationsList) {
                orderNum++;
                testMapper.updateWeaponrySpecificationsOrderNumBySpecificationsId(weaponrySpecifications.getSpecificationsId(), orderNum);
                accordingSpecificationsIdSort(weaponrySpecifications.getSpecificationsId());
            }
        }
    }
}
