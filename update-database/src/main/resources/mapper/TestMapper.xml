<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lirong.project.member.mapper.TestMapper">

    <!--根据人员id删除-->
    <update id="deleteAllPeopleByPeopleId">
        update all_people set is_delete = 1 where people_id = #{peopleId}
    </update>

    <!--根据ID删除-->
    <update id="deleteNewOrgRelById">
        DELETE
        FROM
            new_org_rel
        WHERE
            id = #{id}
    </update>

    <!--根据ID删除-->
    <update id="deleteNewOrgRelByIds">
        UPDATE new_org_rel
        SET is_delete = 1
        WHERE
            id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!--删除重复机构-->
    <update id="deleteRepetitionOrg">
        UPDATE idw_org
        SET is_delete = 1,
        update_by = CONCAT( #{orgCode}, '重复机构' ),
        update_time = SYSDATE( )
        WHERE
            org_code = #{orgCode}
    </update>

    <!--根本机构类型查询顶层机构机构编码-->
    <select id="selectTopOrgCodeByOrgType" resultType="java.lang.String">
        SELECT
            org.org_code
        FROM
            idw_org org
            LEFT JOIN idw_org_relationship relationship ON relationship.org_code = org.org_code
            AND relationship.is_delete = 0
        WHERE
            org.is_delete = 0
            AND relationship.parent_code = '0'
            AND org.org_type = #{orgType}
    </select>

    <!--根据父机构编码查询机构编码-->
    <select id="selectOrgByParentCode" resultType="java.lang.String">
        SELECT
            org.org_code
        FROM
            idw_org org
            LEFT JOIN idw_org_relationship relationship ON relationship.org_code = org.org_code
            AND relationship.is_delete = 0
        WHERE
            org.is_delete = 0
            AND relationship.parent_code = #{parentCode}
    </select>

    <!--根据人员类型查询该人员类型下所有人员编码-->
    <select id="selectPeopleCodeByCategory" resultType="java.lang.String">
        SELECT
            people_code
        FROM
            idw_people_main
        WHERE
            is_delete = 0
            AND category = #{category}
    </select>

    <!--查询所有Excel导入的军事基地机构编码数据-->
    <select id="selectImportBaseOrgCode" resultType="java.lang.String">
        SELECT
            org_code
        FROM
            idw_org
        WHERE
            org_type = '军事基地'
            AND create_by != 'wangchao'
        ORDER BY
            avatar DESC
    </select>

    <!--查询所有从其他库迁移过来的数据-->
    <select id="selectMigrationBaseOrgCode" resultType="java.lang.String">
        SELECT
            org_code
        FROM
            idw_org
        WHERE
            org_type = '军事基地'
            AND create_by = 'wangchao'
        ORDER BY
            avatar DESC
    </select>

    <!--查询所有人员发表作品-->
    <select id="selectPeoplePublishedWorks" resultType="com.lirong.project.member.domain.PeoplePublishedWorks">
        SELECT
            works_id AS worksId,
            author
        FROM
            idw_people_published_works
    </select>

    <!--根据发表作品ID查询发表作品作者-->
    <select id="selectAuthorByWorksId" resultType="com.lirong.project.member.domain.PeoplePublishedWorksAuthor">
        SELECT
            author_id AS authorId,
            people_name AS peopleName
        FROM
            idw_people_published_works_author
        WHERE
            is_delete = 0
            AND works_id = #{worksId}
    </select>

    <!--查询原始数据相关数据-->
    <select id="selectSolrCorrelation" resultMap="IdwMultimediaResult">
        SELECT
            media_id,
            title,
            REPLACE(REPLACE(storage_path,'/profile/','D:\\webdp\\uploadPath\\'),'/','\\') AS storage_path
        FROM
            idw_multimedia
        WHERE
            is_delete = 0
            AND business_type = 'search'
    </select>

    <!--查询采集图片视频-->
    <select id="seelctCollectMultimedia" resultMap="IdwMultimediaResult">
        SELECT
            'weaponry' AS business_type,
            title,
            publisher AS author,
            publish_date AS release_date,
            thumbnail,
            storage_path,
            description AS introduction,
            tags,
            url AS source
        FROM
            `defense-daas`.media
        WHERE
            media_type = 'video'
          AND storage_path LIKE '%.mp4'
          AND storage_path LIKE '/prod%'
    </select>

    <resultMap type="com.lirong.project.member.domain.OrgRelationship" id="IdwOrgRelationshipResult">
        <result property="relationshipId"    column="relationship_id"    />
        <result property="orgCode"    column="org_code"    />
        <result property="orgName"    column="org_name"    />
        <result property="parentCode"    column="parent_code"    />
        <result property="parentName"    column="parent_name"    />
        <result property="parentId"    column="parent_id"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="orgType"    column="org_type"    />
        <result property="level"    column="level"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <!--查询所有机构关系-->
    <select id="selectAllOrgRelationship" resultMap="IdwOrgRelationshipResult">
        SELECT
            *
        FROM
            idw_org_relationship
        WHERE
            is_delete = 0
        ORDER BY
            level
    </select>

    <!--查询所有图片视频-->
    <select id="selectAllMultimedia" resultMap="IdwMultimediaResult">
        SELECT
            media_id,
            title,
            storage_path
        FROM
            idw_multimedia
    </select>

    <!--查询原始数据检索文档相关数据-->
    <select id="selectAllSearchFile" resultMap="IdwMultimediaResult">
        SELECT
            media_id,
            title,
            storage_path
        FROM
            idw_multimedia
        WHERE
            is_delete = 0
            AND business_type = 'search'
    </select>

    <!--根据机构编码和祖籍列表查询ID-->
    <select id="selectOrgRelationshipParentIdByParentCodeAndAncestors" resultType="java.lang.Long">
        SELECT
            relationship_id
        FROM
            idw_org_relationship
        WHERE
            is_delete = 0
            AND org_code = #{orgCode}
            AND ancestors = #{ancestors}
    </select>

    <resultMap type="com.lirong.organization.common.domain.IdwOrg" id="IdwOrgResult">
        <result property="orgId"    column="org_id"    />
        <result property="orgCode"    column="org_code"    />
        <result property="country"    column="country"    />
        <result property="orgNameCn"    column="org_name_cn"    />
        <result property="orgNameEn"    column="org_name_en"    />
        <result property="avatar"    column="avatar"    />
        <result property="establishTime"    column="establish_time"    />
        <result property="address"    column="address"    />
        <result property="telephone"    column="telephone"    />
        <result property="email"    column="email"    />
        <result property="profileCn"    column="profile_cn"    />
        <result property="profileEn"    column="profile_en"    />
        <result property="addressEn"    column="address_en"    />
        <result property="mainFunctions"    column="main_functions"    />
        <result property="officialWebsite"    column="official_website"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="orgType"    column="org_type"    />
        <result property="leader"    column="leader"    />
        <result property="leaderProfile"    column="leader_profile"    />
        <result property="troopsCategory"    column="troops_category"    />
        <result property="orderNum"    column="order_num"    />
        <result property="orgTypeAlias"    column="org_type_alias"    />
        <result property="peopleCount"    column="people_count"    />
        <result property="baseEstablishmentRatio"    column="base_establishment_ratio"    />
        <result property="field"    column="field"    />
        <result property="tags"    column="tags"    />
        <result property="source"    column="source"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!--查询所有数据来源为空的机构数据-->
    <select id="selectAllNullSourceOrg" resultMap="IdwOrgResult">
        SELECT
            org.org_id,
            org.org_code,
            relationship.ancestors,
            relationship.parent_code,
            org.source
        FROM
            idw_org org
            LEFT JOIN idw_org_relationship relationship ON relationship.is_delete = 0
        WHERE
            org.is_delete = 0
            AND relationship.org_code = org.org_code
            AND ( org.source IS NULL OR org.source = '' )
    </select>

    <!--根据机构编码查询机构数据来源-->
    <select id="selectSourceByOrgCode" resultType="java.lang.String">
        SELECT
            source
        FROM
            idw_org
        WHERE
            is_delete = 0
            AND org_code = #{orgCode}
    </select>

    <!--根据人员名称查询人员是否存在-->
    <select id="selectPeopleIsExistByNameEnInAllPeople" resultType="java.lang.Boolean">
        SELECT
        IF
            ( COUNT( * ) > 0, TRUE, FALSE ) AS is_exist
        FROM
            all_people
        WHERE
            name = #{name}
    </select>

    <!--查询最大人员编码-->
    <select id="selectMaxPeopleCode" resultType="java.lang.String">
        SELECT
            MAX( people_code ) AS max_people_code
        FROM
            all_people
    </select>

    <!--查询装备规格关联武器装备编码-->
    <select id="selectAllWeaponrySpecificationsRelevancy" resultType="java.lang.String">
        SELECT
            weaponry_code
        FROM
            idw_weaponry_specifications
        WHERE
            is_delete = 0
            AND weaponry_code IN ( SELECT weaponry_code FROM idw_weaponry WHERE is_delete = 0 )
        GROUP BY
            weaponry_code
    </select>

    <resultMap type="com.lirong.weaponry.specifications.domain.IdwWeaponrySpecifications" id="IdwWeaponrySpecificationsResult">
        <result property="specificationsId"    column="specifications_id"    />
        <result property="weaponryCode"    column="weaponry_code"    />
        <result property="parentId"    column="parent_id"    />
        <result property="nameCn"    column="name_cn"    />
        <result property="nameEn"    column="name_en"    />
        <result property="value"    column="value"    />
        <result property="type"    column="type"    />
        <result property="level"    column="level"    />
        <result property="introduction"    column="introduction"    />
        <result property="orderNum"    column="order_num"    />
        <result property="source"    column="source"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="parentName" column="parent_name" />
    </resultMap>

    <!--根据装备编码获取顶层装备规格-->
    <select id="selectTopWeaponrySpecificationsByWeaponryCode" resultMap="IdwWeaponrySpecificationsResult">
        SELECT
            specifications_id,
            parent_id
        FROM
            idw_weaponry_specifications
        WHERE
            is_delete = 0
            AND parent_id = 0
            AND weaponry_code = #{weaponryCode}
    </select>

    <!--根据装备规格ID查询下级装备规格-->
    <select id="selectChildrenBySpecificationsId" resultMap="IdwWeaponrySpecificationsResult">
        SELECT
            specifications_id,
            parent_id
        FROM
            idw_weaponry_specifications
        WHERE
            is_delete = 0
            AND parent_id = #{specificationsId}
    </select>

    <resultMap type="com.lirong.resource.domain.IdwResource" id="IdwResourceResult">
        <result property="resourceId"    column="resource_id"    />
        <result property="country"    column="country"    />
        <result property="nameCn"    column="name_cn"    />
        <result property="nameEn"    column="name_en"    />
        <result property="thumbnail"    column="thumbnail"    />
        <result property="publisher"    column="publisher"    />
        <result property="publishDate"    column="publish_date"    />
        <result property="author"    column="author"    />
        <result property="categoryIds"    column="category_id"    />
        <result property="summaryCn"    column="summary_cn"    />
        <result property="summaryEn"    column="summary_en"    />
        <result property="keyword"    column="keyword"    />
        <result property="fileName"    column="file_name"    />
        <result property="storagePath"    column="storage_path"    />
        <result property="pageNum"    column="page_num"    />
        <result property="fileType"    column="file_type"    />
        <result property="source"    column="source"    />
        <result property="status"    column="status"    />
        <result property="categoryName"    column="category_name"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!--查询资源库所有数据-->
    <select id="selectAllResource" resultMap="IdwResourceResult">
        SELECT
            *
        FROM
            idw_resource
        WHERE
            is_delete = 0
    </select>

    <!--根据上级编码查询上级机构关系ID-->
    <select id="selectOrgRelationshipByOrgCode" resultMap="IdwOrgRelationshipResult">
        SELECT
            *
        FROM
            idw_org_relationship
        WHERE
            is_delete = 0
            AND org_code = #{orgCode}
    </select>

    <!--查询没有机构架构的顶层机构关系数据-->
    <select id="selectWithoutOrganizationalStructureTopOrgRelationship" resultMap="IdwOrgRelationshipResult">
        SELECT
            *
        FROM
            idw_org_relationship
        WHERE
            is_delete = 0
            AND parent_code = '0'
            AND org_code IN ( SELECT org_code FROM idw_org_relationship WHERE is_delete = 0 GROUP BY org_code HAVING COUNT( * ) > 1 )
        </select>

    <!--根据机构编码查询顶层机构处于其他机构架构中的关系-->
    <select id="selectNotTopNodeOrgRelationshipByOrgCode" resultMap="IdwOrgRelationshipResult">
        SELECT
            *
        FROM
            idw_org_relationship
        WHERE
            is_delete = 0
            AND parent_code != '0'
            AND org_code = #{orgCode}
    </select>

    <!--根据机构编码与祖籍列表查询子级节点-->
    <select id="selectChildrenByOrgCodeAndAncestors" resultMap="IdwOrgRelationshipResult">
        SELECT
            *
        FROM
            idw_org_relationship
        WHERE
            is_delete = 0
            AND parent_code = #{orgCode}
            AND ancestors = CONCAT( #{ancestors}, ',', #{orgCode} )
    </select>

    <!--<resultMap type="com.lirong.organization.system.domain.IdwOrgSystemArchitecture" id="IdwOrgThemeResult">
        <result property="themeId"    column="theme_id"    />
        <result property="type"    column="type"    />
        <result property="orgCode"    column="org_code"    />
        <result property="name"    column="name"    />
        <result property="parentId"    column="parent_id"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="color"    column="color"    />
        <result property="isShow"    column="is_show"    />
        <result property="profile"    column="profile"    />
        <result property="orderNum"    column="order_num"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>-->

    <!--根据体系结构ID排序-->
   <!-- <select id="selectOrgThemeOrderByThemeId" resultMap="IdwOrgThemeResult">
        SELECT
            *
        FROM
            idw_org_system_architecture
        WHERE
            type = 'organization'
    </select>-->

    <!--查询所有机构编码-->
    <select id="selectOrgCodeExcludeOrgCodes" resultType="java.lang.String">
        SELECT
            org.org_code
        FROM
            idw_org org
            LEFT JOIN idw_org_relationship rel ON rel.is_delete = 0
            AND rel.org_code = org.org_code
        WHERE
            org.is_delete = 0
            AND org.org_code NOT IN
        <foreach item="orgCode" collection="orgCodes" open="(" separator="," close=")">
            #{orgCode}
        </foreach>
        ORDER BY
            rel.level
    </select>

    <!--查询二级体系结构-->
    <!--<select id="selectSecondLevelOrgTheme" resultMap="IdwOrgThemeResult">
        SELECT
            *
        FROM
            idw_org_system_architecture
        WHERE
            is_delete = 0
            AND parent_id = 1
    </select>-->

    <!--根据二级体系结构ID查询子集机构编码-->
    <select id="selectOrgThemeOrgCodeBySecondLevelOrgThemeId" resultType="java.lang.String">
        SELECT
            org_code
        FROM
            idw_org_system_architecture
        WHERE
            is_delete = 0
            AND type = 'organization'
            AND FIND_IN_SET( #{themeId}, ancestors )
    </select>

    <!--查询所有机构新闻-->
    <select id="selectAllOrgNews" resultType="com.lirong.project.member.domain.FormatMonthOrDay">
        SELECT
            news_id AS id,
            publish_date AS date
        FROM
            idw_org_news
        WHERE
            is_delete = 0
    </select>

    <!--查询所有机构采办项目-->
    <select id="selectAllOrgProject" resultType="com.lirong.project.member.domain.FormatMonthOrDay">
        SELECT
            project_id AS id,
            published_date AS date
        FROM
            idw_org_project
        WHERE
            is_delete = 0
    </select>

    <!--查询所有机构文献-->
    <select id="selectAllOrgDocument" resultType="com.lirong.project.member.domain.FormatMonthOrDay">
        SELECT
            document_id AS id,
            publish_date AS date
        FROM
            idw_org_document
        WHERE
            is_delete = 0
    </select>

    <resultMap type="com.lirong.project.member.domain.OriginalPeople" id="PeopleResult">
        <result property="id"    column="id"    />
        <result property="orgCode"    column="org_code"    />
        <result property="orgNameCn"    column="org_name_cn"    />
        <result property="orgNameEn"    column="org_name_en"    />
        <result property="parentName"    column="parent_name"    />
        <result property="nameCn"    column="name_cn"    />
        <result property="nameEn"    column="name_en"    />
        <result property="gender"    column="gender"    />
        <result property="troopsCategory"    column="troops_category"    />
        <result property="militaryRank"    column="military_rank"    />
        <result property="post"    column="post"    />
        <result property="workplace"    column="workplace"    />
        <result property="description"    column="description"    />
        <result property="avatar"    column="avatar"    />
        <result property="source"    column="source"    />
        <result property="remark"    column="remark"    />
        <result property="type"    column="type"    />
    </resultMap>
    <!--查询所有人员原始数据-->
    <select id="selectAllPeopleOriginalData" resultMap="PeopleResult">
        SELECT
            *
        FROM
            people
    </select>

    <resultMap type="com.lirong.project.member.domain.IdwOrgRelationship" id="OrgRelationshipResult">
        <result property="relationshipId"    column="relationship_id"    />
        <result property="orgCode"    column="org_code"    />
        <result property="parentCode"    column="parent_code"    />
        <result property="parentId"    column="parent_id"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="orgType"    column="org_type"    />
        <result property="level"    column="level"    />
        <result property="auditPhase"    column="audit_phase"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>


    <!--根据上级编码查询需要迁移的架构-->
    <select id="selectAllOrgStructureMigrateByPatentOrgCode" resultMap="OrgRelationshipResult">
        SELECT
            *
        FROM
            idw_org_relationship1
        WHERE
            is_delete = 0
            AND org_type = '国防单位'
            AND parent_code = #{orgCode}
    </select>

    <resultMap type="com.lirong.organization.common.domain.IdwOrg" id="OrgResult">
        <result property="orgId"    column="org_id"    />
        <result property="orgCode"    column="org_code"    />
        <result property="country"    column="country"    />
        <result property="orgNameCn"    column="org_name_cn"    />
        <result property="orgNameEn"    column="org_name_en"    />
        <result property="industry"    column="industry"    />
        <result property="avatar"    column="avatar"    />
        <result property="establishTime"    column="establish_time"    />
        <result property="address"    column="address"    />
        <result property="addressEn"    column="address_en"    />
        <result property="telephone"    column="telephone"    />
        <result property="email"    column="email"    />
        <result property="profileCn"    column="profile_cn"    />
        <result property="profileEn"    column="profile_en"    />
        <result property="mainFunctions"    column="main_functions"    />
        <result property="area"    column="area"    />
        <result property="founder"    column="founder"    />
        <result property="facilities"    column="facilities"    />
        <result property="officialWebsite"    column="official_website"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="orgType"    column="org_type"    />
        <result property="orgTypeAlias"    column="org_type_alias"    />
        <result property="leader"    column="leader"    />
        <result property="leaderProfile"    column="leader_profile"    />
        <result property="troopsCategory"    column="troops_category"    />
        <result property="peopleCount"    column="people_count"    />
        <result property="baseEstablishmentRatio"    column="base_establishment_ratio"    />
        <result property="reutersKey"    column="reuters_key"    />
        <result property="tags"    column="tags"    />
        <result property="field"    column="field"    />
        <result property="orderNum"    column="order_num"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="source"    column="source"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>
    <!--根据机构编码查询需要迁移的机构-->
    <select id="selectOrgMigrateByOrgCode" resultMap="OrgResult">
        SELECT
            *
        FROM
            idw_org1
        WHERE
            is_delete = 0
            AND org_code = #{orgCode}
    </select>

    <resultMap type="com.lirong.project.member.domain.AllPeople" id="AllPeopleResult">
        <result property="peopleId"    column="PEOPLE_ID"    />
        <result property="firstName"    column="FIRST_NAME"    />
        <result property="middleName"    column="MIDDLE_NAME"    />
        <result property="lastName"    column="LAST_NAME"    />
        <result property="fullName"    column="FULL_NAME"    />
        <result property="usedName"    column="USED_NAME"    />
        <result property="resumeEn"    column="RESUME_EN"    />
        <result property="resumeCn"    column="RESUME_CN"    />
        <result property="resumeLink"    column="RESUME_LINK"    />
        <result property="orgCode"    column="org_code"    />
        <result property="orgNameCn"    column="org_name_cn"    />
        <result property="orgNameEn"    column="org_name_en"    />
        <result property="TAGS"    column="TAGS"    />
        <result property="EMAIL"    column="EMAIL"    />
        <result property="TELEPHONE"    column="TELEPHONE"    />
        <result property="TWITTER"    column="TWITTER"    />
        <result property="FACEBOOK"    column="FACEBOOK"    />
        <result property="LINKEDIN"    column="LINKEDIN"    />
        <result property="SKYPE"    column="SKYPE"    />
        <result property="PHOTO"    column="PHOTO"    />
        <result property="COUNTRY"    column="COUNTRY"    />
        <result property="RACE"    column="RACE"    />
        <result property="GENDER"    column="GENDER"    />
        <result property="birthDate"    column="BIRTH_DATE"    />
        <result property="currentAddress"    column="CURRENT_ADDRESS"    />
        <result property="POST"    column="POST"    />
        <result property="birthPlace"    column="BIRTH_PLACE"    />
        <result property="nationalId"    column="NATIONAL_ID"    />
        <result property="drivingLicenseId"    column="DRIVING_LICENSE_ID"    />
        <result property="passportNo"    column="PASSPORT_NO"    />
        <result property="LANGUAGE"    column="LANGUAGE"    />
        <result property="otherLanguage"    column="OTHER_LANGUAGE"    />
        <result property="RELIGION"    column="RELIGION"    />
        <result property="HEALTH"    column="HEALTH"    />
        <result property="VEHICLE"    column="VEHICLE"    />
        <result property="criminalRecord"    column="CRIMINAL_RECORD"    />
        <result property="attachmentType"    column="ATTACHMENT_TYPE"    />
    </resultMap>
    <!--查询所有-->
    <select id="selectAllPeople" resultMap="AllPeopleResult">
        SELECT
            *
        FROM
            all_people
        WHERE
            is_delete = 0
    </select>


    <resultMap type="com.lirong.organization.staff.domain.IdwOrgStaff" id="IdwOrgStaffResult">
        <result property="staffId"    column="staff_id"    />
        <result property="orgCode"    column="org_code"    />
        <result property="peopleCode"    column="people_code"    />
        <result property="peopleNameCn"    column="people_name_cn"    />
        <result property="peopleType"    column="people_type"    />
        <result property="peopleNameEn"    column="people_name_en"    />
        <result property="avatar"    column="avatar"    />
        <result property="position"    column="position"    />
        <result property="managerStaffId"    column="manager_staff_id"    />
        <result property="managerStaffName"    column="manager_staff_name"    />
        <result property="profileCn"    column="profile_cn"    />
        <result property="profileEn"    column="profile_en"    />
        <result property="status"    column="status"    />
        <result property="source"    column="source"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>
    <!--查询所有people表中相关机构人员-->
    <select id="selectPeopleOrgStaff" resultMap="IdwOrgStaffResult">
        SELECT
            o.org_code,
            '' AS people_code,
        IF
            ( p.name_cn IS NULL OR p.name_cn = '', p.name_en, p.name_cn ) AS people_name_cn,
            p.name_en AS people_name_en,
            p.post AS position,
            p.description AS profile_en,
            '在职' AS STATUS,
        IF
            ( p.source IS NULL OR p.source = '', '待完善', p.source ) AS source,
            0 AS is_delete,
            'admin' AS create_by,
            SYSDATE( )
        FROM
            people p
            JOIN idw_org o ON o.is_delete = 0
        WHERE
            p.org_name_en = o.org_name_en
            AND (
                ( p.name_cn IS NOT NULL AND p.name_cn != '' )
                OR ( p.name_en IS NOT NULL AND p.name_en != '' )
            )
    </select>

    <resultMap type="com.lirong.project.member.domain.NewOrgRel" id="NewOrgRelResult">
        <result property="id"    column="id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="parentName"    column="parent_name"    />
        <result property="orgCode"    column="org_code"    />
        <result property="parentCode"    column="parent_code"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="nameCn"    column="name_cn"    />
        <result property="nameEn"    column="name_en"    />
        <result property="source"    column="source"    />
    </resultMap>

    <!--根据ID查询-->
    <select id="selectNewOrgRelById" resultMap="NewOrgRelResult">
        SELECT
            *
        FROM
            new_org_rel
        WHERE
            id = #{id}
    </select>

    <!--根据名称查询-->
    <select id="selectNewOrgRelByName" resultMap="NewOrgRelResult">
        SELECT
            *
        FROM
            new_org_rel
        WHERE
            name_cn = #{name}
            OR name_en = #{name}
    </select>

    <!--构建新的架构-->
    <select id="buildNewOrgchart" resultType="com.lirong.common.core.domain.StructureChart">
        SELECT
            id as `key`,
            CONCAT( name_cn, ',', name_en ) AS name,
            parent_id AS parent,
            parent_name AS badge,
        IF
            ( org_code IS NOT NULL AND org_code != '', org_code, '' ) AS `code`
        FROM
            new_org_rel
        WHERE
            is_delete = 0
    </select>

    <resultMap type="com.lirong.project.member.domain.NewPeople" id="NewPeopleResult">
        <result property="id"    column="id"    />
        <result property="originalOrgCode"    column="original_org_code"    />
        <result property="orgNameCn"    column="org_name_cn"    />
        <result property="orgNameEn"    column="org_name_en"    />
        <result property="parentName"    column="parent_name"    />
        <result property="peopleNameCn"    column="people_name_cn"    />
        <result property="peopleNameEn"    column="people_name_en"    />
        <result property="gender"    column="gender"    />
        <result property="description"    column="description"    />
        <result property="avatar"    column="avatar"    />
        <result property="troopsCategory"    column="troops_category"    />
        <result property="militaryRank"    column="military_rank"    />
        <result property="post"    column="post"    />
        <result property="workplace"    column="workplace"    />
        <result property="source"    column="source"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <!--查询所有人员-->
    <select id="selectAllNewPeople" resultMap="NewPeopleResult">
        SELECT
            *
        FROM
            new_people
        WHERE
            org_name_en IS NOT NULL
            AND org_name_en != ''
        GROUP BY
            org_name_en,
            parent_name
        ORDER BY
            id
    </select>

    <!--查询所有机构编码为空节点-->
    <select id="selectNewOrgRelByOrgCodeIsNull" resultMap="NewOrgRelResult">
        SELECT
            *
        FROM
            new_org_rel
        WHERE
            org_code IS NULL
            OR org_code = ''
        GROUP BY
            name_en
        ORDER BY
            id
    </select>

    <!--根据机构名称与上级名称查询-->
    <select id="selectNewOrgRelByNameEnAndParentName" resultType="int">
        SELECT
            COUNT( * ) AS count
        FROM
            new_org_rel
        WHERE
            name_en = #{orgNameEn}
            <if test="parentName != null and parentName != ''">
                AND parent_name = #{parentName}
            </if>
    </select>

    <!--查询找不到上级节点-->
    <select id="selectNewOrgRelNotFoundParent" resultMap="NewOrgRelResult">
        SELECT
            *
        FROM
            new_org_rel
        WHERE
            parent_id = -1
    </select>

    <resultMap type="com.lirong.project.member.domain.OldOrgRel" id="OldOrgRelResult">
        <result property="id"    column="id"    />
        <result property="orgCode"    column="org_code"    />
        <result property="nameEn"    column="name_en"    />
        <result property="nameCn"    column="name_cn"    />
        <result property="parentCode"    column="parent_code"    />
        <result property="source"    column="source"    />
    </resultMap>

    <!--查询原始架构-->
    <select id="selectAllOldOrgRel" resultMap="OldOrgRelResult">
        SELECT
            *
        FROM
            old_org_rel
         ORDER BY
            org_code
    </select>

    <!--根据机构编码查询名称-->
    <select id="selectOldOrgRelNameByOrgCode" resultType="java.lang.String">
        SELECT
            name_en
        FROM
            old_org_rel
        WHERE
            org_code = #{orgCode}
            LIMIT 1
    </select>

    <resultMap type="com.lirong.organization.document.domain.IdwOrgDocument" id="IdwOrgDocumentResult">
        <result property="documentId"    column="document_id"    />
        <result property="orgCode"    column="org_code"    />
        <result property="titleCn"    column="title_cn"    />
        <result property="titleEn"    column="title_en"    />
        <result property="thumbnail"    column="thumbnail"    />
        <result property="summaryCn"    column="summary_cn"    />
        <result property="summaryEn"    column="summary_en"    />
        <result property="publishDate"    column="publish_date"    />
        <result property="author"    column="author"    />
        <result property="resourceType"    column="resource_type"    />
        <result property="category"    column="category"    />
        <result property="storagePath"    column="storage_path"    />
        <result property="website"    column="website"    />
        <result property="tags"    column="tags"    />
        <result property="source"    column="source"    />
        <result property="collectTime"    column="collect_time"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!--查询所有机构文献-->
    <select id="selectOrgDocument" resultMap="IdwOrgDocumentResult">
        SELECT
            *
        FROM
            idw_org_document
        WHERE
            is_delete = 0
            AND (
                title_cn IS NULL
                OR title_cn = ''
                OR ( ( summary_cn IS NULL OR summary_cn = '' ) AND summary_en IS NOT NULL AND summary_en != '' )
            )
    </select>

    <resultMap type="com.lirong.organization.project.domain.IdwOrgProject" id="IdwOrgProjectResult">
        <result property="projectId"    column="project_id"    />
        <result property="orgCode"    column="org_code"    />
        <result property="noticeId"    column="notice_id"    />
        <result property="publishedDate"    column="published_date"    />
        <result property="titleCn"    column="title_cn"    />
        <result property="titleEn"    column="title_en"    />
        <result property="department"    column="department"    />
        <result property="departmentEn"    column="department_en"    />
        <result property="subTier"    column="sub_tier"    />
        <result property="majorCommand"    column="major_command"    />
        <result property="subCommand"    column="sub_command"    />
        <result property="office"    column="office"    />
        <result property="contractAwardDate"    column="contract_award_date"    />
        <result property="contractAwardNumber"    column="contract_award_number"    />
        <result property="completeDate"    column="complete_date"    />
        <result property="contractorDuns"    column="contractor_duns"    />
        <result property="contractorName"    column="contractor_name"    />
        <result property="contractorAddress"    column="contractor_address"    />
        <result property="totalContractValue"    column="total_contract_value"    />
        <result property="productServiceCode"    column="product_service_code"    />
        <result property="naicsCode"    column="naics_code"    />
        <result property="tags"    column="tags"    />
        <result property="source"    column="source"    />
        <result property="descriptionCn"    column="description_cn"    />
        <result property="descriptionEn"    column="description_en"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="collectionTime"    column="collection_time"    />
        <result property="createBy"    column="create_by"    />
        <result property="updateBy"    column="update_by"    />
        <result property="archivedUrl"    column="archived_url"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!--查询所有机构项目-->
    <select id="selectOrgProject" resultMap="IdwOrgProjectResult">
        SELECT
            *
        FROM
            idw_org_project
        WHERE
            is_delete = 0
            AND (
                title_cn IS NULL
                OR title_cn = ''
                OR ( ( description_cn IS NULL OR description_cn = '' ) AND description_en IS NOT NULL AND description_en != '' )
            )
    </select>

    <resultMap type="com.lirong.organization.news.domain.IdwOrgNews" id="IdwOrgNewsResult">
        <result property="newsId" column="news_id"/>
        <result property="orgCode" column="org_code"/>
        <result property="titleCn" column="title_cn"/>
        <result property="titleEn" column="title_en"/>
        <result property="subtitleCn" column="subtitle_cn"/>
        <result property="subtitleEn" column="subtitle_en"/>
        <result property="author" column="author"/>
        <result property="publishDate" column="publish_date"/>
        <result property="summaryCn" column="summary_cn"/>
        <result property="summaryEn" column="summary_en"/>
        <result property="thumbnail" column="thumbnail"/>
        <result property="contentCn" column="content_cn"/>
        <result property="contentEn" column="content_en"/>
        <result property="contentHtml" column="content_html"/>
        <result property="tags" column="tags"/>
        <result property="websiteName" column="website_name"/>
        <result property="url" column="url"/>
        <result property="isDelete" column="is_delete"/>
        <result property="collector" column="collector"/>
        <result property="collectionTime" column="collection_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!--查询所有机构新闻-->
    <select id="selectOrgNews" resultMap="IdwOrgNewsResult">
        SELECT
            *
        FROM
            idw_org_news
        WHERE
            is_delete = 0
            AND update_by = '传神翻译'
    </select>
    <!--
            AND (
                title_cn IS NULL
                OR title_cn = ''
                OR ( ( summary_cn IS NULL OR summary_cn = '' ) AND summary_en IS NOT NULL AND summary_en != '' )
            OR ( ( content_cn IS NULL OR content_cn = '' ) AND content_en IS NOT NULL AND content_en != '' )
            )-->

    <!--根据上级ID查询-->
    <select id="selectNewOrgRelByParentIds" resultType="java.lang.Long">
        SELECT
            id
        FROM
            new_org_rel
        WHERE
            is_delete = 0
            AND parent_id IN
        <foreach item="parentId" collection="parentIds" open="(" separator="," close=")">
            #{parentId}
        </foreach>
    </select>

    <!--获取所有简介为pdf数据-->
    <select id="selectOrgStaffProfileIsPdfFile" resultMap="IdwOrgStaffResult">
        SELECT
            *
        FROM
            idw_org_staff
        WHERE
            is_delete = 0
            AND profile_en LIKE CONCAT( '%', '见', '%' )
    </select>

    <resultMap type="com.lirong.personnel.common.domain.IdwPeopleMain" id="IdwPeopleMainResult">
        <result property="peopleId"    column="people_id"    />
        <result property="peopleCode"    column="people_code"    />
        <result property="country"    column="country"    />
        <result property="category"    column="category"    />
        <result property="nameCn"    column="name_cn"    />
        <result property="peopleType"    column="people_type"    />
        <result property="nameEn"    column="name_en"    />
        <result property="gender"    column="gender"    />
        <result property="nativePlace"    column="native_place"    />
        <result property="birthplace"    column="birthplace"    />
        <result property="status"    column="status"    />
        <result property="birthday"    column="birthday"    />
        <result property="formerName"    column="former_name"    />
        <result property="age"    column="age"    />
        <result property="avatar"    column="avatar"    />
        <result property="email"    column="email"    />
        <result property="certificateType"    column="certificate_type"    />
        <result property="telephone"    column="telephone"    />
        <result property="idNumber"    column="id_number"    />
        <result property="graduatedUniversity"    column="graduated_university"    />
        <result property="officePhone"    column="office_phone"    />
        <result property="degree"    column="degree"    />
        <result property="homePhone"    column="home_phone"    />
        <result property="orgCode"    column="org_code"    />
        <result property="faxNumber"    column="fax_number"    />
        <result property="orgName"    column="org_name"    />
        <result property="address"    column="address"    />
        <result property="post"    column="post"    />
        <result property="zipCode"    column="zip_code"    />
        <result property="nationality"    column="nationality"    />
        <result property="appointmentDate"    column="appointment_date"    />
        <result property="occupation"    column="occupation"    />
        <result property="workplace"    column="workplace"    />
        <result property="participantOrg"    column="participant_org"    />
        <result property="workingDate"    column="working_date"    />
        <result property="innerCode"    column="inner_code"    />
        <result property="party"    column="party"    />
        <result property="troopsCategory"    column="troops_category"    />
        <result property="militaryRank"    column="military_rank"    />
        <result property="profileCn"    column="profile_cn"    />
        <result property="profileEn"    column="profile_en"    />
        <result property="education"    column="education"    />
        <result property="hobby"    column="hobby"    />
        <result property="inServiceEduUniversity"    column="in_service_edu_university"    />
        <result property="strengthsWeaknesses"    column="strengths_weaknesses"    />
        <result property="inServiceEduEducation"    column="in_service_edu_education"    />
        <result property="technicalExpertise"    column="technical_expertise"    />
        <result property="inServiceEduDegree"    column="in_service_edu_degree"    />
        <result property="achievement"    column="achievement"    />
        <result property="religiousBelief"    column="religious_belief"    />
        <result property="rewardsPunishments"    column="rewards_punishments"    />
        <result property="race"    column="race"    />
        <result property="physicalCondition"    column="physical_condition"    />
        <result property="policyProposition"    column="policy_proposition"    />
        <result property="extraversion"    column="extraversion"    />
        <result property="emotionalStability"    column="emotional_stability"    />
        <result property="agreeableness"    column="agreeableness"    />
        <result property="politicalOrientation"    column="political_orientation"    />
        <result property="conscientiousness"    column="conscientiousness"    />
        <result property="cppccPosts"    column="cppcc_posts"    />
        <result property="openness"    column="openness"    />
        <result property="mainSocialPost"    column="main_social_post"    />
        <result property="attitudeTowardsChina"    column="attitude_towards_china"    />
        <result property="technicalPost"    column="technical_post"    />
        <result property="orderNum"    column="order_num"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="source"    column="source"    />
        <result property="createBy"    column="create_by"    />
        <result property="joinPartyDate"    column="join_party_date"    />
        <result property="createTime"    column="create_time"    />
        <result property="secondParty"    column="second_party"    />
        <result property="updateBy"    column="update_by"    />
        <result property="joinSecondPartyDate"    column="join_second_party_date"    />
        <result property="politicalPerformance"    column="political_performance"    />
        <result property="updateTime"    column="update_time"    />
        <result property="educationalExperience"    column="educational_experience"    />
        <result property="assignments"    column="assignments"    />
        <result property="peopleCharacter"    column="people_character"    />
        <result property="promotion"    column="promotion"    />
        <result property="socialInfluence"    column="social_influence"    />
        <result property="identityCategory"    column="identity_category"    />
        <result property="level1213"    column="level_1213"    />
        <result property="politicalFaction"    column="political_faction"    />
        <result property="tags"    column="tags"    />
    </resultMap>

    <!--查询所有需要指派人员编码人员-->
    <select id="selectNeedAssignmentPeopleCode" resultMap="IdwPeopleMainResult">
        SELECT
            *
        FROM
            idw_people_main
        WHERE
            is_delete = 0
            AND people_code NOT LIKE CONCAT( '%', 'P', '%' )
            AND people_code NOT LIKE CONCAT( '%', 'JS', '%' )
            AND people_code NOT LIKE CONCAT( '%', 'T', '%' )
    </select>

    <!--根据机构编码查询机构关系数量-->
    <select id="selectOrgRelCountByOrgCode" resultType="java.lang.Integer">
        SELECT
            COUNT( * ) AS count
        FROM
            idw_org_relationship
        WHERE
            is_delete = 0
            AND org_code = #{orgCode}
    </select>

    <!---->
    <select id="selectAllOrg" resultMap="IdwOrgResult">
        SELECT
            *
        FROM
            idw_org
    </select>

    <!--查询所有顶层关系-->
    <select id="selectTopOrgRel" resultMap="IdwOrgRelationshipResult">
        SELECT
            rel.relationship_id,
            rel.org_code,
            org.org_name_en AS org_name,
            rel.parent_code,
            rel.parent_id,
            rel.ancestors,
            rel.org_type,
            rel.level,
            rel.audit_phase,
            rel.assign_user_id,
            rel.is_delete,
            rel.create_by,
            rel.create_time,
            rel.update_by,
            rel.update_time
        FROM
            idw_org_relationship rel
            JOIN idw_org org ON org.is_delete = 0
            AND org.org_code = rel.org_code
        WHERE
            rel.is_delete = 0
            AND rel.parent_code = '0'
            <!--AND rel.org_type = '国防单位'-->
            <!--AND rel.org_code IN ( SELECT org_code FROM idw_org WHERE is_delete = 0 AND country = 'US' )-->
    </select>

    <!--根据机构名称查询上级名称-->
    <select id="selectNewOrgRelParentNameByOrgName" resultType="java.lang.String">
        SELECT DISTINCT
            parent_name
        FROM
            new_org_rel
        WHERE
            name_en = #{orgName}
    </select>

    <!--根据机构编码查询当前机构关系上级机构名称-->
    <select id="selectOrgRelParentOrgNameByOrgCode" resultType="java.lang.String">
        SELECT
            org.org_name_en
        FROM
            idw_org_relationship rel
            LEFT JOIN idw_org org ON org.is_delete = 0
            AND org.org_code = rel.parent_code
        WHERE
            rel.is_delete = 0
            AND rel.org_code = #{orgCode}
    </select>

    <!--查询所有装备规格分类-->
    <select id="selectWeaponrySpecificationsCategory" resultMap="IdwWeaponrySpecificationsResult">
        SELECT
            *
        FROM
            idw_weaponry_specifications
        WHERE
            is_delete = 0
            AND type = '分类'
    </select>

    <!--根据上级ID查询指标数据-->
    <select id="selectWeaponrySpecificationsByParentId" resultMap="IdwWeaponrySpecificationsResult">
        SELECT
            *
        FROM
            idw_weaponry_specifications
        WHERE
            is_delete = 0
            AND type = '指标'
            AND parent_id = #{parentId}
    </select>

    <!--查询人员新闻报道数量-->
    <select id="selectPeopleNewsOrderCount" resultType="com.lirong.personnel.news.domain.IdwPeopleNews">
        SELECT
            people_code AS peopleCode,
            COUNT( * ) AS count
        FROM
            idw_people_news
        WHERE
            is_delete = 0
            AND people_code IN ( SELECT people_code FROM idw_people_social_media WHERE is_delete = 0 )
        GROUP BY
            people_code
        ORDER BY
            count DESC
    </select>

    <!--查询日期-->
    <select id="selectDate" resultType="com.lirong.project.member.domain.FormatDate">
        SELECT
            ${primaryKey} AS keyValue,
            ${pendingField} AS value
        FROM
            ${tableName}
        WHERE
            is_delete = 0
            AND ${pendingField}  IS NOT NULL
            AND ${pendingField}  != ''
    </select>

    <!--查询机构关系-->
    <select id="selectOrgRel" resultMap="IdwOrgRelationshipResult">
        SELECT
            *
        FROM
            idw_org_relationship
        WHERE
            is_delete = 0
            AND org_type = '国防单位'
            AND org_code IN ( SELECT org_code FROM idw_org WHERE is_delete = 0 AND org_type = '国防单位' AND country = 'US' AND create_time &lt; '2021-10-10 00:00:00' )
    </select>

    <!--根据父机构编码查询机构关系-->
    <select id="selectOrgRelByParentCode" resultMap="IdwOrgRelationshipResult">
        SELECT
            *
        FROM
            idw_org_relationship
        WHERE
            is_delete = 0
            AND parent_code = #{parentCode}
    </select>

    <!--查询文献资料-->
    <select id="selectOriginalDocument" resultType="com.lirong.project.member.domain.OriginalDocument">
        SELECT
            *
        FROM
            original_document
        WHERE
            url = '有无人和文献数据清单'
    </select>

    <resultMap type="com.lirong.project.member.domain.PatentOrg" id="PatentOrgResult">
        <result property="id"    column="id"    />
        <result property="patentId"    column="patent_id"    />
        <result property="name"    column="name"    />
    </resultMap>

    <!--查询多专利机构名称-->
    <select id="selectPatentMuchName" resultMap="PatentOrgResult">
        SELECT
            *
        FROM
            patent_org
        WHERE
            name LIKE '%                                            %'
    </select>

    <resultMap type="com.lirong.project.member.domain.RdAwardOrg" id="RdAwardOrgResult">
        <result property="id"    column="id"    />
        <result property="awardId"    column="award_id"    />
        <result property="name"    column="name"    />
    </resultMap>

    <!--查询多发布作品机构名称-->

    <select id="selectAwardMuchName" resultMap="RdAwardOrgResult">
        SELECT
            *
        FROM
            rd_award_org
        WHERE
        name LIKE '%,%'
            AND name NOT IN
        <foreach item="excludeName" collection="excludeNames" open="(" separator="," close=")">
            #{excludeName}
        </foreach>
    </select>

    <resultMap type="com.lirong.project.member.domain.Technosphere" id="TechnosphereResult">
        <result property="id"    column="id"    />
        <result property="nameCn"    column="name_cn"    />
        <result property="nameEn"    column="name_en"    />
        <result property="parentId"    column="parent_id"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="level"    column="level"    />
        <result property="associatedType"    column="associated_type"    />
        <result property="associatedCode"    column="associated_code"    />
        <result property="descCn"    column="desc_cn"    />
        <result property="descEn"    column="desc_en"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!--查询技术领域列表-->
    <select id="selectTechnosphereList" resultMap="TechnosphereResult">
        SELECT
            *
        FROM
            idw_technosphere
        WHERE
            ancestors IS NULL
    </select>

    <!--根据id查询技术领域-->
    <select id="selectTechnosphereById" resultMap="TechnosphereResult">
        SELECT
            *
        FROM
            idw_technosphere
        WHERE
            id = #{id}
    </select>

    <!--根据机构类型查询最大层级-->
    <select id="selectMaxLevelByOrgType" resultType="java.lang.Integer">
        SELECT
            MAX( level ) AS max_level
        FROM
            idw_org_relationship
        WHERE
            is_delete = 0
            AND org_type = #{orgType}
    </select>

    <!--根据机构类型与层级查询机构-->
    <select id="selectOrgCodeByLevelAndOrgType" resultType="java.lang.String">
        SELECT DISTINCT
            r.org_code
        FROM
            idw_org_relationship r
            JOIN idw_org o ON o.is_delete = 0
            AND o.org_code = r.org_code
        WHERE
            r.is_delete = 0
            AND r.org_type = #{orgType}
            AND r.level = #{level}
        ORDER BY
            o.country
    </select>

    <!--根据数据库名与表名查询表属性-->
    <select id="selectColumnNameByTableName" resultType="com.lirong.project.member.domain.DBCloumn">
        SELECT
            column_name AS columnName,
            data_type AS dataType,
            column_comment AS columnComment
        FROM
            information_schema.COLUMNS
        WHERE
            TABLE_SCHEMA = #{tableSchema}
            AND TABLE_NAME = #{tableName}
    </select>

    <!--根据表名与条件查询总数-->
    <select id="selectDataTotalByTableNameAndQuery" resultType="java.lang.Integer">
        SELECT
            count( * ) AS count
        FROM
            ${tableName}
        ${query}
    </select>

    <!--根据表名与属性名查询属性名空值率-->
    <select id="selectVacancyRateByTableNameAndColumnName" resultType="java.lang.String">
        SELECT
            count( * ) / ${total} AS count
        FROM
            ${tableName}
        ${query}
        <choose>
            <when test="isString">
                AND ( ${columnName} IS NULL OR ${columnName} = '' )
            </when>
            <otherwise>
                AND ${columnName} IS NULL
            </otherwise>
        </choose>
    </select>

    <!--查询文件路径-->
    <select id="selectAllAvatarPath" resultType="com.lirong.project.member.domain.FileVo">
         SELECT
            org_id AS id,
            'idw_org' AS type,
            avatar AS path
        FROM
            idw_org
        WHERE
            is_delete = 0
            AND avatar IS NOT NULL
            AND avatar != '' UNION
        SELECT
            staff_id AS id,
            'idw_org_staff' AS type,
            avatar AS path
        FROM
            idw_org_staff
        WHERE
            is_delete = 0
            AND avatar IS NOT NULL
            AND avatar != ''
    </select>


    <resultMap type="com.lirong.project.member.domain.CraftCompany" id="CraftCompanyResult">
        <result property="id"    column="id"    />
        <result property="type"    column="type"    />
        <result property="logo"    column="logo"    />
        <result property="displayName"    column="display_name"    />
        <result property="fullName"    column="full_name"    />
        <result property="uei"    column="uei"    />
        <result property="slug"    column="slug"    />
        <result property="country"    column="country"    />
        <result property="foundedYear"    column="founded_year"    />
        <result property="employeeCount"    column="employee_count"    />
        <result property="hqLocation"    column="hq_location"    />
        <result property="latitude"    column="latitude"    />
        <result property="longitude"    column="longitude"    />
        <result property="sectors"    column="sectors"    />
        <result property="website"    column="website"    />
        <result property="shortDescription"    column="short_description"    />
        <result property="status"    column="status"    />
        <result property="source"    column="source"    />
        <result property="linkedin"    column="linkedin"    />
        <result property="instagram"    column="instagram"    />
        <result property="facebook"    column="facebook"    />
        <result property="youtube"    column="youtube"    />
        <result property="twitter"    column="twitter"    />
        <result property="overview"    column="overview"    />
    </resultMap>

    <!--根据ID查询采集机构-->
    <select id="selectCraftCompanyById" resultMap="CraftCompanyResult">
        SELECT
            id,
            type,
            logo,
            display_name,
            full_name,
            uei,
            slug,
            country,
            founded_year,
            employee_count,
            hq_location,
            latitude,
            longitude,
            sectors,
            website,
            short_description,
            STATUS,
            source,
            linkedin,
            instagram,
            facebook,
            youtube,
            twitter,
            overview
        FROM
            craft_company
        WHERE
            id = #{id}
    </select>

    <resultMap type="com.lirong.project.member.domain.CraftCompanyPeople" id="CraftCompanyPeopleResult">
        <result property="id"    column="id"    />
        <result property="companyId"    column="company_id"    />
        <result property="name"    column="name"    />
        <result property="avatar"    column="avatar"    />
        <result property="position"    column="position"    />
        <result property="profile"    column="profile"    />
        <result property="linkedin"    column="linkedin"    />
        <result property="twitter"    column="twitter"    />
        <result property="facebook"    column="facebook"    />
        <result property="source"    column="source"    />
    </resultMap>

    <!--根据采集机构ID与人员名称查询-->
    <select id="selectCraftCompanyPeople" resultMap="CraftCompanyPeopleResult">
        SELECT
            id,
            company_id,
            name,
            avatar,
            position,
            PROFILE,
            linkedin,
            twitter,
            facebook,
            source
        FROM
            craft_company_people
        WHERE
            company_id = #{companyId}
            and name = #{name}
    </select>

    <!--根据表名与属性名查询-->
    <select id="selectByTableNameAndProperty" resultType="java.lang.String">
        SELECT DISTINCT
            ${property}
        FROM
            ${tableName}
        WHERE
            ${property} IS NOT NULL
            AND ${property} != ''
    </select>

    <resultMap type="com.lirong.project.member.domain.IdwWeaponryShip" id="IdwWeaponryShipResult">
        <result property="weaponryCode"    column="weaponry_code"    />
        <result property="nameCn"    column="name_cn"    />
        <result property="nameEn"    column="name_en"    />
        <result property="shipId"    column="ship_id"    />
        <result property="shipName"    column="ship_name"    />
    </resultMap>

    <!--查询所有装备舰船关系-->
    <select id="selectWeaponryShipList" resultMap="IdwWeaponryShipResult">
        SELECT
            *
        FROM
            idw_weaponry_ship
    </select>

    <!--根据表名与字段名查询文件路径-->
    <select id="selectFilePathList" resultType="java.lang.String">
        SELECT
            ${fieldName}
        FROM
            ${tableName}
        WHERE
            is_delete = 0
            AND ${fieldName} IS NOT NULL
            AND ${fieldName} != ''
    </select>

    <resultMap type="com.lirong.weaponry.ship.domain.Ship" id="ShipResult">
        <result property="id"    column="id"    />
        <result property="weaponryCode"    column="weaponry_code"    />
        <result property="nationalOrigin"    column="national_origin"    />
        <result property="classificationCode"    column="classification_code"    />
        <result property="shipType"    column="ship_type"    />
        <result property="shipTranslateName"    column="ship_translate_name"    />
        <result property="shipName"    column="ship_name"    />
        <result property="badge"    column="badge"    />
        <result property="photo"    column="photo"    />
        <result property="hullNo"    column="hull_no"    />
        <result property="className"    column="class_name"    />
        <result property="classId"    column="class_id"    />
        <result property="awarded"    column="awarded"    />
        <result property="laidDown"    column="laid_down"    />
        <result property="launched"    column="launched"    />
        <result property="commissioned"    column="commissioned"    />
        <result property="decommissioned"    column="decommissioned"    />
        <result property="serviceStatus"    column="service_status"    />
        <result property="homeport"    column="homeport"    />
        <result property="builder"    column="builder"    />
        <result property="orderNum"    column="order_num"    />
        <result property="source"    column="source"    />
        <result property="history"    column="history"    />
        <result property="introductionCn"    column="introduction_cn"    />
        <result property="introductionEn"    column="introduction_en"    />
    </resultMap>

    <!--查询舰船-->
    <select id="selectShipList" resultMap="ShipResult">
        SELECT
            *
        FROM
            ship
        WHERE
            is_delete = 0
    </select>

    <resultMap type="com.lirong.weaponry.basic.domain.IdwWeaponryBasic" id="IdwWeaponryResult">
        <result property="weaponryId"    column="weaponry_id"    />
        <result property="classificationCode"    column="classification_code"    />
        <result property="classificationName"    column="classification_name"    />
        <result property="weaponryCode"    column="weaponry_code"    />
        <result property="country"    column="country"    />
        <result property="nameCn"    column="name_cn"    />
        <result property="nameEn"    column="name_en"    />
        <result property="homeport"    column="homeport"    />
        <result property="nickname"    column="nickname"    />
        <result property="generalTerm"    column="general_term"    />
        <result property="avatar"    column="avatar"    />
        <result property="developmentEndTime"    column="development_end_time"    />
        <result property="developmentStartTime"    column="development_start_time"    />
        <result property="seviceTime"    column="sevice_time"    />
        <result property="finalizationTime"    column="finalization_time"    />
        <result property="firstDeliveryTime"    column="first_delivery_time"    />
        <result property="projectApprovalTime"    column="project_approval_time"    />
        <result property="firstTime"    column="first_time"    />
        <result property="quantity"    column="quantity"    />
        <result property="mainUser"    column="main_user"    />
        <result property="cost"    column="cost"    />
        <result property="introductionCn"    column="introduction_cn"    />
        <result property="introductionEn"    column="introduction_en"    />
        <result property="developmentBackground"    column="development_background"    />
        <result property="constructionHistory"    column="construction_history"    />
        <result property="serviceHistory"    column="service_history"    />
        <result property="structure"    column="structure"    />
        <result property="armament"    column="armament"    />
        <result property="powerSystem"    column="power_system"    />
        <result property="avionicsSystem"    column="avionics_system"    />
        <result property="status"    column="status"    />
        <result property="orderNum"    column="order_num"    />
        <result property="tags"    column="tags"    />
        <result property="contractor"    column="contractor"    />
        <result property="source"    column="source"    />
        <result property="subType"    column="sub_type"    />
        <result property="frontType"    column="front_type"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!--查询所有装备-->
    <select id="selectAllWeaponry" resultMap="IdwWeaponryResult">
        SELECT
            *
        FROM
            idw_weaponry
        WHERE
            is_delete = 0
    </select>

    <!--查询所有舰船-->
    <select id="selectAllShip" resultMap="ShipResult">
        SELECT
            *
        FROM
            ship
        WHERE
            is_delete = 0
        ORDER BY
            id DESC
    </select>

    <!--根据数据库名称查询人员-->
    <select id="selectPeopleByDatabaseName" resultMap="IdwPeopleMainResult">
        SELECT
            *
        FROM
            ${database}idw_people_main
        WHERE
            is_delete = 0
    </select>

    <resultMap type="com.lirong.personnel.education.domain.IdwPeopleEducation" id="IdwPeopleEducationResult">
        <result property="educationId"    column="education_id"    />
        <result property="peopleCode"    column="people_code"    />
        <result property="college"    column="college"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="major"    column="major"    />
        <result property="degree"    column="degree"    />
        <result property="source"    column="source"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!--根据数据库名称与人员编码查询人员教育经历-->
    <select id="selectPeopleEducationByDatabaseNameAndPeopleCode" resultMap="IdwPeopleEducationResult">
        SELECT
            *
        FROM
            ${database}idw_people_education
        WHERE
            is_delete = 0
            AND people_code = #{peopleCode}
    </select>

    <resultMap type="com.lirong.personnel.media.domain.IdwPeopleSocialAccount" id="IdwPeopleSocialAccountResult">
        <result property="accountId"    column="account_id"    />
        <result property="peopleCode"    column="people_code"    />
        <result property="mediaType"    column="media_type"    />
        <result property="account"    column="account"    />
        <result property="name"    column="name"    />
        <result property="visitAddress"    column="visit_address"    />
        <result property="avatar"    column="avatar"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!--根据数据库名称与人员编码查询人员社交媒体账号-->
    <select id="selectPeopleSocialAccountByDatabaseNameAndPeopleCode" resultMap="IdwPeopleSocialAccountResult">
        SELECT
            *
        FROM
            ${database}idw_people_social_account
        WHERE
            is_delete = 0
            AND people_code = #{peopleCode}
    </select>

    <resultMap type="com.lirong.personnel.media.domain.IdwPeopleSocialMedia" id="IdwPeopleSocialMediaResult">
        <result property="mediaId"    column="media_id"    />
        <result property="peopleCode"    column="people_code"    />
        <result property="mediaType"    column="media_type"    />
        <result property="account"    column="account"    />
        <result property="publishDate"    column="publish_date"    />
        <result property="content"    column="content"    />
        <result property="collectionDate"    column="collection_date"    />
        <result property="politicalOrientation"    column="political_orientation"    />
        <result property="attitudeTowardsChina"    column="attitude_towards_china"    />
        <result property="url"    column="url"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!--根据数据库名称与人员编码查询人员社交媒体-->
    <select id="selectPeopleSocialMediaByDatabaseNameAndPeopleCode" resultMap="IdwPeopleSocialMediaResult">
        SELECT
            *
        FROM
            ${database}idw_people_social_media
        WHERE
            is_delete = 0
            AND people_code = #{peopleCode}
    </select>

    <resultMap type="com.lirong.personnel.news.domain.IdwPeopleNews" id="IdwPeopleNewsResult">
        <result property="newsId"    column="news_id"    />
        <result property="peopleCode"    column="people_code"    />
        <result property="type"    column="type"    />
        <result property="title"    column="title"    />
        <result property="subtitle"    column="subtitle"    />
        <result property="author"    column="author"    />
        <result property="publishDate"    column="publish_date"    />
        <result property="summary"    column="summary"    />
        <result property="thumbnail"    column="thumbnail"    />
        <result property="content"    column="content"    />
        <result property="tags"    column="tags"    />
        <result property="websiteName"    column="website_name"    />
        <result property="url"    column="url"    />
        <result property="category"    column="category"    />
        <result property="collectionTime"    column="collection_time"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!--根据数据库名称与人员编码查询人员新闻-->
    <select id="selectPeopleNewsByDatabaseNameAndPeopleCode" resultMap="IdwPeopleNewsResult">
        SELECT
            *
        FROM
            ${database}idw_people_news
        WHERE
            is_delete = 0
            AND people_code = #{peopleCode}
    </select>

    <resultMap type="com.lirong.personnel.work.domain.IdwPeopleWorkExperience" id="IdwPeopleWorkExperienceResult">
        <result property="experienceId"    column="experience_id"    />
        <result property="peopleCode"    column="people_code"    />
        <result property="orgCode"    column="org_code"    />
        <result property="companyName"    column="company_name"    />
        <result property="location"    column="location"    />
        <result property="title"    column="title"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="workDescCn"    column="work_desc_cn"    />
        <result property="workDescEn"    column="work_desc_en"    />
        <result property="remark"    column="remark"    />
        <result property="source"    column="source"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!--根据数据库名称与人员编码查询人员工作经历-->
    <select id="selectPeopleWorkExperienceByDatabaseNameAndPeopleCode" resultMap="IdwPeopleWorkExperienceResult">
        SELECT
            *
        FROM
            ${database}idw_people_work_experience
        WHERE
            is_delete = 0
            AND people_code = #{peopleCode}
    </select>

    <resultMap type="com.lirong.personnel.honor.domain.IdwPeopleHonor" id="IdwPeopleHonorResult">
        <result property="honorId"    column="honor_id"    />
        <result property="peopleCode"    column="people_code"    />
        <result property="honorType"    column="honor_type"    />
        <result property="title"    column="title"    />
        <result property="issueDate"    column="issue_date"    />
        <result property="issuer"    column="issuer"    />
        <result property="description"    column="description"    />
        <result property="source"    column="source"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="issuedTo"    column="issued_to"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!--根据数据库名称与人员编码查询人员荣誉奖项-->
    <select id="selectPeopleHonorByDatabaseNameAndPeopleCode" resultMap="IdwPeopleHonorResult">
        SELECT
            *
        FROM
            ${database}idw_people_honor
        WHERE
            is_delete = 0
            AND people_code = #{peopleCode}
    </select>

    <resultMap type="com.lirong.personnel.publishcation.domain.IdwPeoplePublishedWorks" id="IdwPeoplePublishedWorksResult">
        <result property="worksId"    column="works_id"    />
        <result property="peopleCode"    column="people_code"    />
        <result property="titleCn"    column="title_cn"    />
        <result property="titleEn"    column="title_en"    />
        <result property="summary"    column="summary"    />
        <result property="type"    column="type"    />
        <result property="publicationDate"    column="publication_date"    />
        <result property="chapter"    column="chapter"    />
        <result property="language"    column="language"    />
        <result property="keyWords"    column="key_words"    />
        <result property="author"    column="author"    />
        <result property="linkUrl"    column="link_url"    />
        <result property="remark"    column="remark"    />
        <result property="doi"    column="doi"    />
        <result property="source"    column="source"    />
        <result property="journalName"    column="journal_name"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="journalType"    column="journal_type"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="publishOrg"    column="publish_org"    />
        <result property="updateTime"    column="update_time"    />
        <result property="impactFactor"    column="impact_factor"    />
        <result property="citationNum"    column="citation_num"    />
        <result property="authorRanking"    column="author_ranking"    />
        <result property="fileName"    column="file_name"    />
        <result property="orderNum"    column="order_num"    />
    </resultMap>

    <!--根据数据库名称与人员编码查询人员发表作品-->
    <select id="selectPeoplePublishedWorksByDatabaseNameAndPeopleCode" resultMap="IdwPeoplePublishedWorksResult">
        SELECT
            *
        FROM
            ${database}idw_people_published_works
        WHERE
            is_delete = 0
            AND people_code = #{peopleCode}
    </select>

    <resultMap type="com.lirong.personnel.publishcation.domain.IdwPeoplePublishedWorksAuthor" id="IdwPeoplePublishedWorksAuthorResult">
        <result property="authorId"    column="author_id"    />
        <result property="worksId"    column="works_id"    />
        <result property="peopleCode"    column="people_code"    />
        <result property="peopleName"    column="people_name"    />
        <result property="avatar"    column="avatar"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!--根据数据库名称与人员发表作品ID查询人员发表作品作者-->
    <select id="selectPeoplePublishedWorksAuthorByDatabaseNameAndWorksId" resultMap="IdwPeoplePublishedWorksAuthorResult">
        SELECT
            *
        FROM
            ${database}idw_people_published_works_author
        WHERE
            is_delete = 0
            AND works_id = #{worksId}
    </select>

    <resultMap type="com.lirong.personnel.patent.domain.IdwPeoplePatent" id="IdwPeoplePatentResult">
        <result property="patentId"    column="patent_id"    />
        <result property="peopleCode"    column="people_code"    />
        <result property="patentTitleCn"    column="patent_title_cn"    />
        <result property="patentTitleEn"    column="patent_title_en"    />
        <result property="patentOffice"    column="patent_office"    />
        <result property="patentType"    column="patent_type"    />
        <result property="patentNumber"    column="patent_number"    />
        <result property="status"    column="status"    />
        <result property="issuedDate"    column="issued_date"    />
        <result property="filingDate"    column="filing_date"    />
        <result property="applyNumber"    column="apply_number"    />
        <result property="patentUrl"    column="patent_url"    />
        <result property="mainClassificationNumber"    column="main_classification_number"    />
        <result property="description"    column="description"    />
        <result property="classificationNumber"    column="classification_number"    />
        <result property="source"    column="source"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="applicant"    column="applicant"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="fileName"    column="file_name"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!--根据数据库名称与人员编码查询人员专利发明-->
    <select id="selectPeoplePatentByDatabaseNameAndPeopleCode" resultMap="IdwPeoplePatentResult">
        SELECT
            *
        FROM
            ${database}idw_people_patent
        WHERE
            is_delete = 0
            AND people_code = #{peopleCode}
    </select>

    <resultMap type="com.lirong.personnel.patent.domain.IdwPeoplePatentInventor" id="IdwPeoplePatentInventorResult">
        <result property="inventorId"    column="inventor_id"    />
        <result property="patentId"    column="patent_id"    />
        <result property="peopleCode"    column="people_code"    />
        <result property="peopleName"    column="people_name"    />
        <result property="avatar"    column="avatar"    />
        <result property="isDelete"    column="is_delete"    />
    </resultMap>

    <!--根据数据库名称与人员专利发明ID查询人员专利发明人-->
    <select id="selectPeoplePatentInventorByDatabaseNameAndPatentId" resultMap="IdwPeoplePatentInventorResult">
        SELECT
            *
        FROM
            ${database}idw_people_patent_inventor
        WHERE
            is_delete = 0
            AND patent_id = #{patentId}
    </select>

    <resultMap type="com.lirong.personnel.relationship.domain.IdwPeopleRelationship" id="IdwPeopleRelationshipResult">
        <result property="relationshipId"    column="relationship_id"    />
        <result property="peopleCode"    column="people_code"    />
        <result property="relatedPeopleName"    column="related_people_name"    />
        <result property="relatedPeopleCode"    column="related_people_code"    />
        <result property="relatedPeopleAvatar"    column="related_people_avatar"    />
        <result property="relationship"    column="relationship"    />
        <result property="remark"    column="remark"    />
        <result property="source"    column="source"    />
        <result property="birthDate"    column="birth_date"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="politicalStatus"    column="political_status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!--根据数据库名称与人员编码查询人员关系-->
    <select id="selectPeopleRelationshipByDatabaseNameAndPeopleCode" resultMap="IdwPeopleRelationshipResult">
        SELECT
            *
        FROM
            ${database}idw_people_relationship
        WHERE
            is_delete = 0
            AND people_code = #{peopleCode}
    </select>

    <resultMap type="com.lirong.personnel.domain.IdwPeopleAchievement" id="IdwPeopleAchievementResult">
        <result property="achievementId"    column="achievement_id"    />
        <result property="peopleCode"    column="people_code"    />
        <result property="achievementType"    column="achievement_type"    />
        <result property="name"    column="name"    />
        <result property="nameEn"    column="name_en"    />
        <result property="participant"    column="participant"    />
        <result property="duty"    column="duty"    />
        <result property="achievementDate"    column="achievement_date"    />
        <result property="projectResource"    column="project_resource"    />
        <result property="submittingDepartment"    column="submitting_department"    />
        <result property="level"    column="level"    />
        <result property="contentCn"    column="content_cn"    />
        <result property="contentEn"    column="content_en"    />
        <result property="fileName"    column="file_name"    />
        <result property="storagePath"    column="storage_path"    />
        <result property="source"    column="source"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!--根据数据库名称与人员编码查询人员工作成果-->
    <select id="selectPeopleAchievementByDatabaseNameAndPeopleCode" resultMap="IdwPeopleAchievementResult">
        SELECT
            *
        FROM
            ${database}idw_people_achievement
        WHERE
            is_delete = 0
            AND people_code = #{peopleCode}
    </select>

    <resultMap type="com.lirong.multimedia.domain.IdwMultimedia" id="IdwMultimediaResult">
        <result property="mediaId"    column="media_id"    />
        <result property="mediaType"    column="media_type"    />
        <result property="businessType"    column="business_type"    />
        <result property="associationId"    column="association_id"    />
        <result property="title"    column="title"    />
        <result property="subtitle"    column="subtitle"    />
        <result property="thumbnail"    column="thumbnail"    />
        <result property="storagePath"    column="storage_path"    />
        <result property="releaseDate"    column="release_date"    />
        <result property="introduction"    column="introduction"    />
        <result property="source"    column="source"    />
        <result property="md5"    column="md5"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="websiteName"    column="website_name"    />
        <result property="updateTime"    column="update_time"    />
        <result property="tag"    column="tag"    />
    </resultMap>

    <!--根据数据库名称与关联类型关联编码查询多媒体-->
    <select id="selectMultimediaByDatabaseNameAndBusinessTypeAndAssociationId" resultMap="IdwMultimediaResult">
        SELECT
            *
        FROM
            ${database}idw_multimedia
        WHERE
            is_delete = 0
            AND business_type = #{businessType}
            AND association_id = #{associationId}
    </select>

    <!--根据数据库名称与关联类型关联编码与标题查询多媒体-->
    <select id="selectMultimediaByDatabaseNameAndBusinessTypeAndAssociatioIdAndTitle" resultMap="IdwMultimediaResult">
        SELECT
            *
        FROM
            ${database}idw_multimedia
        WHERE
            is_delete = 0
            AND business_type = #{businessType}
            AND association_id = #{associationId}
            AND title = #{title}
    </select>

    <resultMap type="com.lirong.project.member.domain.DocDocument" id="DocDocumentResult">
        <result property="id"    column="id"    />
        <result property="title"    column="title"    />
        <result property="translatedTitle"    column="translated_title"    />
        <result property="publisher"    column="publisher"    />
        <result property="publishDate"    column="publish_date"    />
        <result property="authors"    column="authors"    />
        <result property="thumbnail"    column="thumbnail"    />
        <result property="summary"    column="summary"    />
        <result property="type"    column="type"    />
        <result property="area"    column="area"    />
        <result property="category"    column="category"    />
        <result property="topics"    column="topics"    />
        <result property="content"    column="content"    />
        <result property="filePath"    column="file_path"    />
        <result property="download"    column="download"    />
        <result property="price"    column="price"    />
        <result property="url"    column="url"    />
        <result property="collectTime"    column="collect_time"    />
        <result property="post"    column="post"    />
        <result property="page"    column="page"    />
        <result property="creator"    column="creator"    />
        <result property="createTime"    column="create_time"    />
        <result property="updater"    column="updater"    />
        <result property="updateTime"    column="update_time"    />
        <result property="deleted"    column="deleted"    />
    </resultMap>

    <!--查询所有文档-->
    <select id="selectDocDocument" resultMap="DocDocumentResult">
        SELECT
            *
        FROM
            doc_document
        WHERE
            file_path LIKE '%\llzz_deploy%'
            AND page IS NULL
    </select>

    <!--查询机构关系 根据层级排序-->
    <select id="selectOrgRelationshipOrderByLevel" resultMap="IdwOrgRelationshipResult">
        SELECT
            *
        FROM
            idw_org_relationship
        WHERE
            is_delete = 0
        ORDER BY
            level
    </select>

    <resultMap type="com.lirong.project.member.domain.EquipmentCharacteristic" id="EquipmentCharacteristicResult">
        <result property="id"    column="id"    />
        <result property="equipmentId"    column="equipment_id"    />
        <result property="type"    column="type"    />
        <result property="indexName"    column="index_name"    />
        <result property="indexValue"    column="index_value"    />
    </resultMap>

    <!--根据采集装备ID查询装备战技指标-->
    <select id="selectEquipmentCharacteristicByEquipmentIds" resultMap="EquipmentCharacteristicResult">
        SELECT
            *
        FROM
            equipment_specification
        WHERE
            equipment_id IN
        <foreach item="equipmentId" collection="equipmentIds" open="(" separator="," close=")">
            #{equipmentId}
        </foreach>
        ORDER BY
            equipment_id
    </select>

    <!--根据采集时间查询机构文献-->
    <select id="selectOrgDocumentByCollectTime" resultMap="IdwOrgDocumentResult">
        SELECT
            *
        FROM
            idw_org_document
        WHERE
            is_delete = 0
            AND collect_time > #{collectTime}
    </select>

    <!--查询顶层机构-->
    <select id="selectTopOrg" resultMap="IdwOrgRelationshipResult">
        SELECT
            *
        FROM
            idw_org_relationship
        WHERE
            is_delete = 0
            AND parent_id = -1
            AND org_type != '军事基地'
    </select>

    <!--根据上级编码查询机构关系-->
    <select id="selectOrgRelationshipByParentCode" resultMap="IdwOrgRelationshipResult">
        SELECT
            *
        FROM
            idw_org_relationship
        WHERE
            is_delete = 0
            AND parent_code = #{parentCode}
    </select>

    <!--<resultMap type="com.lirong.organization.structure.domain.IdwOrgStructure" id="IdwOrgStructureResult">
        <result property="structureId"    column="structure_id"    />
        <result property="orgCode"    column="org_code"    />
        <result property="parentId"    column="parent_id"    />
        <result property="level"    column="level"    />
        <result property="nameCn"    column="name_cn"    />
        <result property="nameEn"    column="name_en"    />
        <result property="structureOrgCode"    column="structure_org_code"    />
        <result property="avatar"    column="avatar"    />
        <result property="type"    column="type"    />
        <result property="profileCn"    column="profile_cn"    />
        <result property="profileEn"    column="profile_en"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="source"    column="source"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    &lt;!&ndash;根据机构编码与结构中文名称查询&ndash;&gt;
    <select id="selectOrgStructureByOrgCodeAndOrgNameCn" resultMap="IdwOrgStructureResult">
        SELECT
            *
        FROM
            idw_org_structure
        WHERE
            is_delete = 0
            AND org_code = #{orgCode}
            AND name_cn = #{nameCn}
    </select>-->

    <resultMap type="com.lirong.organization.product.domain.IdwOrgProduct" id="IdwOrgProductResult">
        <result property="productId" column="product_id"/>
        <result property="orgCode" column="org_code"/>
        <result property="weaponryCode" column="weaponry_code"/>
        <result property="field" column="field"/>
        <result property="picture" column="picture"/>
        <result property="fileUrl" column="file_url"/>
        <result property="productNameCn" column="product_name_cn"/>
        <result property="productNameEn" column="product_name_en"/>
        <result property="introductionCn" column="introduction_cn"/>
        <result property="introductionEn" column="introduction_en"/>
        <result property="technology" column="technology"/>
        <result property="tags" column="tags"/>
        <result property="source" column="source"/>
        <result property="isDelete" column="is_delete"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!--查询机构产品-->
    <select id="selectOrgProduct" resultMap="IdwOrgProductResult">
        SELECT
            *
        FROM
            idw_org_product
        WHERE
            is_delete = 0
            AND update_by = '更新'
            AND picture IS NOT NULL
            AND picture != ''
    </select>

    <!--根据上级ID查询机构关系-->
    <!--<select id="selectRelationshipByParentId" resultMap="IdwOrgStructureResult">
        SELECT DISTINCT
            r.relationship_id AS structure_id,
            r.parent_id,
            level - 1 AS level,
            o.org_name_cn AS name_cn,
            o.org_name_en AS name_en,
            o.org_code AS structure_org_code,
            o.avatar,
            o.org_type AS type,
            o.profile_cn,
            o.profile_en,
            o.longitude,
            o.latitude,
            o.source 
        FROM
            idw_org_relationship r
            JOIN idw_org o ON o.is_delete = 0 
            AND o.org_code = r.org_code 
        WHERE
            r.is_delete = 0 
            AND r.parent_id = #{parentId}
    </select>

    &lt;!&ndash;根据ID查询机构架构&ndash;&gt;
    <select id="selectOrgStructureByStructureId" resultMap="IdwOrgStructureResult">
        SELECT
            *
        FROM
            idw_org_structure
        WHERE
            is_delete = 0
            AND structure_id = #{structureId}
    </select>

    &lt;!&ndash;返回机构架构对象&ndash;&gt;
    <select id="selectTopOrgReturnStructure" resultMap="IdwOrgStructureResult">
        SELECT
            relationship_id AS structure_id,
            org_code,
            parent_id
        FROM
            idw_org_relationship
        WHERE
            is_delete = 0
            AND parent_id = - 1
            AND org_type != '军事基地'
    </select>-->

    <resultMap type="com.lirong.technosphere.domain.IdwTechnosphere" id="IdwTechnosphereResult">
        <result property="id"    column="id"    />
        <result property="nameCn"    column="name_cn"    />
        <result property="nameEn"    column="name_en"    />
        <result property="parentId"    column="parent_id"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="avatar"    column="avatar"    />
        <result property="level"    column="level"    />
        <result property="associatedType"    column="associated_type"    />
        <result property="associatedCode"    column="associated_code"    />
        <result property="descCn"    column="desc_cn"    />
        <result property="descEn"    column="desc_en"    />
        <result property="isTreeLeaf"    column="is_tree_leaf"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="parentName" column="parent_name" />
    </resultMap>

    <!--根据上级ID查询技术领域-->
    <select id="selectTechnosphereByParentId" resultMap="IdwTechnosphereResult">
        SELECT
            *
        FROM
            idw_technosphere
        WHERE
            is_delete = 0
            AND parent_id = #{parentId}
    </select>

    <!--查询没有架构机构-->
    <select id="selectOrgStructureNotData" resultMap="OrgResult">
        SELECT
            *
        FROM
            idw_org
        WHERE
            is_delete = 0
            AND org_code NOT IN ( SELECT org_code FROM idw_org_structure WHERE is_delete = 0 GROUP BY org_code )
    </select>

    <!--查询采集两千装备相关战技指标-->
    <select id="selectCollectWeaponrySpecifications" resultMap="EquipmentCharacteristicResult">
        SELECT
            *
        FROM
            equipment_specification
        WHERE
            equipment_id IN ( 	435138504238831191,435138504238831192,435138504238831193,435138504238831194,435474377002193595,435138504238831195,435471699484349117,435138504238831196,435471699765367483,435138504238831198,435470299085935291,435464364309353147,435139752375621207,435940909452891602,435138504243025495,435140979926767191,435138504243025496,435138504243025497,435940909473863122,435473549361156795,435138504243025498,435474379623633595,435940909478057424,435138504243025499,435138504243025500,434759642527043903,435138504243025501,435140980136482391,435464364334518971,435140980140676696,435138504243025502,435138504243025503,435474382123438779,435470300134511291,435142064129185367,435140980346197592,435138504243025504,435473197370970811,435139752933463647,435456945567044456,435461420704863945,435457261347804008,435140475612042840,435140728104949336,434757297713975626,435138504243025506,435142213370910295,435474384736490171,435140728524379736,435457265969927016,435138504243025508,435138504247219799,435138504247219800,435138504247219801,435138504247219802,435138504247219803,435461423015925447,435138504247219805,435474387295015611,435470301879341755,435138504247219807,435139753545832025,435473382188782267,435140984368535129,434759643068109124,435471714348962491,435474388167430843,434759643068109125,434759643072303423,435474388586861243,435464364355490493,435471715154268859,435471715154268862,435470302500098747,435470302500098748,435142065425225303 )
    </select>

    <!--根据装备编码与上级ID查询最大排序号-->
    <select id="selectWeaponrySpecificationsMaxOrderNumByWeaponryCodeAndParentId" resultType="java.lang.Integer">
        SELECT
            IFNULL( MAX( order_num ), 0 ) AS max_order_num
        FROM
            idw_weaponry_specifications
        WHERE
            is_delete = 0
            AND weaponry_code = #{weaponryCode}
            AND parent_id = #{parentId}
    </select>

    <!--根据装备编码与英文名称查询装备战技指标ID-->
    <select id="selectWeaponrySpecificationsIdByWeaponryCodeAndNameEn" resultMap="IdwWeaponrySpecificationsResult">
        SELECT
            *
        FROM
            idw_weaponry_specifications
        WHERE
            is_delete = 0
            AND weaponry_code = #{weaponryCode}
            AND name_en = #{nameEn}
    </select>

    <!--根据装备编码、指标名称、上级ID查询-->
    <select id="selectWeaponrySpecificationsByWeaponryCodeAndNameEnAndParentId" resultMap="IdwWeaponrySpecificationsResult">
        SELECT
            *
        FROM
            idw_weaponry_specifications
        WHERE
            is_delete = 0
            AND weaponry_code = #{weaponryCode}
            AND name_en = #{name}
            AND parent_id = #{parentId}
    </select>

    <!--查询资源库列表-->
    <select id="selectResourceList" resultMap="IdwResourceResult">
        SELECT
            *
        FROM
            idw_resource
        WHERE
            is_delete = 0
    </select>

    <!--根据资源库ID查询资源库所属类别名称-->
    <select id="selectResourceCategoryByResourceId" resultType="java.lang.String">
        SELECT
            d.category_name
        FROM
            idw_resource_dict_category d
            JOIN ( SELECT * FROM idw_resource_dict_category WHERE is_delete = 0 AND category_id IN ( SELECT category_id FROM idw_resource_category WHERE is_delete = 0 AND resource_id = #{resourceId} ) ) c ON FIND_IN_SET( d.category_id, c.ancestors )
        WHERE
            d.is_delete = 0
    </select>

    <!--查询联合作战资源库-->
    <select id="selectTDocument" resultMap="IdwResourceResult">
        SELECT
            'US' AS country,
            titleCn AS name_cn,
            titleEn AS name_en,
            REPLACE ( REPLACE ( REPLACE ( picturePreviewPath, '\\\\', '\\' ), '\\', '/' ), 'D:/llzz_deploy/upload/', '/profile/lhzz/' ) AS thumbnail,
            publishOrg AS publisher,
            CONCAT( publishYear, '-', publishMonth, '-', publishDay ) AS publish_date,
            author,
            summary AS summary_cn,
            summaryEn AS summary_en,
            keyword,
            REPLACE ( REPLACE ( REPLACE ( filePath, '\\\\', '\\' ), '\\', '/' ), 'D:/llzz_deploy/upload/', '/profile/lhzz/' ) AS storage_path,
        IF
            ( url IS NULL OR url = '', '联合作战', url ) AS source,
            '联合作战' AS create_by,
            SYSDATE( ) AS create_time,
            id AS update_by
        FROM
            t_document
    </select>

    <resultMap type="com.lirong.resource.domain.IdwResourceDictCategory" id="IdwResourceDictCategoryResult">
        <result property="categoryId"    column="category_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="field"    column="field"    />
        <result property="parentId"    column="parent_id"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="parentName" column="parent_name" />
    </resultMap>

    <!--查询联合作战资源库类别-->
    <select id="selectTDocumentType" resultMap="IdwResourceDictCategoryResult">
        SELECT
            name AS category_name,
            15 AS parent_id,
            '联合作战' AS create_by,
            SYSDATE( ) AS create_time,
        IF
            ( rootCategoryId IS NOT NULL AND rootCategoryId != '', CONCAT( id, ',', rootCategoryId ), id ) AS update_by
        FROM
            t_document_type
    </select>

    <!--根据文献类别ID查询自己-->
    <select id="selectTDocumentTypeSubsetByTCategoryIds" resultMap="IdwResourceDictCategoryResult">
        SELECT
            categoryName AS category_name,
            '联合作战' AS create_by,
            SYSDATE( ) AS create_time,
            id AS update_by
        FROM
            t_category
        WHERE
            parentid = #{tCategoryId}
    </select>

    <!--查询关联文献分类上级ID-->
    <select id="selectRelevanceTCatyegoryParentId" resultType="java.lang.String">
        SELECT
            parentId
        FROM
            t_category
        WHERE
            id IN ( SELECT categoryid FROM t_document_category )
    </select>

    <!--根据文献类别ID校验上级是否存在-->
    <select id="selectParentIdIsExistenceById" resultType="java.lang.String">
        SELECT
            parentId
        FROM
            t_category
        WHERE
            id NOT IN ( SELECT rootCategoryId FROM t_document_type WHERE rootCategoryId IS NOT NULL AND rootCategoryId != '' )
            AND id NOT IN ( SELECT id FROM t_category WHERE parentId IN ( SELECT rootCategoryId FROM t_document_type WHERE rootCategoryId IS NOT NULL AND rootCategoryId != '' ) )
            AND id = #{id}
    </select>

    <resultMap type="com.lirong.resource.domain.IdwResourceCategory" id="IdwResourceCategoryResult">
        <result property="id"    column="id"    />
        <result property="resourceId"    column="resource_id"    />
        <result property="categoryId"    column="category_id"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!--查询联合作战资源库关系-->
    <select id="selectTDocumentCategory" resultMap="IdwResourceCategoryResult">
        SELECT
            docId AS create_by,
            categoryId AS update_by
        FROM
            t_document_category
    </select>

    <!--根据联合作战文献ID查询资源库ID-->
    <select id="selectResourceIdByTDocumentId" resultType="java.lang.Long">
        SELECT
            resource_id
        FROM
            idw_resource
        WHERE
            is_delete = 0
            AND update_by = #{documentId}
    </select>

    <!--根据联合作战文献类别ID查询资源库l类别ID-->
    <select id="selectCategoryIdByTCategoryId" resultType="java.lang.Long">
        SELECT
            category_id
        FROM
            idw_resource_dict_category
        WHERE
            is_delete = 0
            AND FIND_IN_SET( #{categoryId}, update_by )
    </select>

    <!--根据作战文献ID获取文献类型后直接查询资源库类别ID-->
    <select id="selectResourceCategoryIdByTDocumentId" resultType="java.lang.Long">
        SELECT
            category_id
        FROM
            idw_resource_dict_category
        WHERE
            is_delete = 0
            AND create_by = '联合作战'
            AND category_name = ( SELECT NAME FROM t_document_type WHERE type = ( SELECT type FROM t_document WHERE id = #{documentId} ) )
    </select>

    <resultMap type="com.lirong.project.member.domain.InstallationVo" id="InstallationResult">
        <result property="id"    column="id"    />
        <result property="installationCode"    column="installation_code"    />
        <result property="nameCn"    column="name_cn"    />
        <result property="fullName"    column="full_name"    />
        <result property="shortName"    column="short_name"    />
        <result property="address"    column="address"    />
        <result property="state"    column="state"    />
        <result property="country"    column="country"    />
        <result property="latitude"    column="latitude"    />
        <result property="longitude"    column="longitude"    />
        <result property="nearestCity"    column="nearest_city"    />
        <result property="phone"    column="phone"    />
        <result property="area"    column="area"    />
        <result property="troopsCategory"    column="troops_category"    />
        <result property="civilian"    column="civilian"    />
        <result property="military"    column="military"    />
        <result property="usafEnlisted"    column="usaf_enlisted"    />
        <result property="usafOfficer"    column="usaf_officer"    />
        <result property="controlledBy"    column="controlled_by"    />
        <result property="controlledOrgCode"    column="controlled_org_code"    />
        <result property="website"    column="website"    />
        <result property="keyFacilities"    column="key_facilities"    />
        <result property="descriptionCn"    column="description_cn"    />
        <result property="descriptionEn"    column="description_en"    />
        <result property="post"    column="post"    />
        <result property="source"    column="source"    />
        <result property="showHome"    column="show_home"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!--查询基地-->
    <select id="selectBase" resultMap="InstallationResult">
        SELECT
            org_code AS installation_code,
            org_name_cn AS name_cn,
            org_name_en AS full_name,
            short_name,
            address,
            country,
            latitude,
            longitude,
            telephone AS phone,
            area,
            troops_category,
            official_website AS website,
            facilities AS key_facilities,
            profile_cn AS description_cn,
            profile_en AS description_en,
            1 AS post,
            source,
            show_home,
            create_by,
            create_time,
            '迁移' AS update_by
        FROM
            idw_org
        WHERE
            is_delete = 0
            AND org_type = '军事基地'
    </select>

    <!--根据军事设施英文名称查询-->
    <select id="selectInstallationByFullName" resultMap="InstallationResult">
        SELECT
            *
        FROM
            installation
        WHERE
            full_name = #{fullName}
    </select>

    <!--根据机构类型查询机构-->
    <select id="selectOrgByOrgTypes" resultMap="IdwOrgResult">
        SELECT
            *
        FROM
            idw_org
        WHERE
            is_delete = 0
            AND org_type IN
        <foreach item="orgType" collection="orgTypes" open="(" separator="," close=")">
            #{orgType}
        </foreach>
    </select>

    <!--查询机构人员-->
    <select id="selectOrgStaff" resultMap="IdwOrgStaffResult">
        SELECT
            *
        FROM
            idw_org_staff
        WHERE
            is_delete = 0
            AND org_code = #{orgCode}
            AND people_name_cn = #{peopleNameCn}
            AND people_name_en = #{peopleNameEn}
            AND position = #{position}
    </select>

    <resultMap type="com.lirong.system.architecture.domain.IdwOrgSystemArchitecture" id="IdwOrgSystemArchitectureResult">
        <result property="id"    column="id"    />
        <result property="systemId"    column="system_id"    />
        <result property="type"    column="type"    />
        <result property="orgCode"    column="org_code"    />
        <result property="name"    column="name"    />
        <result property="parentId"    column="parent_id"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="color"    column="color"    />
        <result property="isShow"    column="is_show"    />
        <result property="profile"    column="profile"    />
        <result property="orderNum"    column="order_num"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!--根据机构编码与上级ID查询国防企业架构-->
    <select id="selectOrgStructure" resultMap="IdwOrgSystemArchitectureResult">
        SELECT
            structure_id AS id,
            598601300888653824 AS system_id,
            'organization' AS type,
            structure_org_code AS org_code,
            name_cn AS name,
            parent_id,
            ancestors,
            '#ffffff' AS color,
            1 AS is_show,
        IF
            ( profile_cn IS NOT NULL AND profile_cn != '', profile_cn, profile_en ) AS profile
        FROM
            idw_org_structure
        WHERE
            is_delete = 0
            AND type = '国防单位'
            AND org_code = #{orgCode}
            AND parent_id = #{parentId}
    </select>

    <!--查询机构体系架构-->
    <select id="selectOrgSystemArchitecture" resultMap="IdwOrgSystemArchitectureResult">
        SELECT
            *
        FROM
            idw_org_system_architecture
        WHERE
            is_delete = 0
    </select>

    <!--查询所有采集文件路径-->
    <select id="selectCollectionDocumentFilePath" resultType="java.lang.String">
        SELECT
            file_path
        FROM
            `data_collection`.matching_document
    </select>

    <!--根据资源库ID查询文件路径-->
    <select id="selectResourceFilePathByIds" resultType="java.lang.String">
        SELECT
            storage_path
        FROM
            idw_resource
        WHERE
            is_delete = 0
            AND resource_id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!--查询大系统所有机构-->
    <select id="selectOrg" resultMap="OrgResult">
        SELECT
            o.org_code,
        IF
            ( g.TARGET_GROUP_NAME_CN IS NOT NULL AND g.TARGET_GROUP_NAME_CN != '', g.TARGET_GROUP_NAME_CN, o.org_name_cn ) AS org_name_cn,
        IF
            ( g.TARGET_GROUP_NAME_EN IS NOT NULL AND g.TARGET_GROUP_NAME_EN != '', g.TARGET_GROUP_NAME_EN, o.org_name_en ) AS org_name_en,
        IF
            ( g.DESCRIPTION IS NOT NULL AND g.DESCRIPTION != '', g.DESCRIPTION, o.org_desc_cn ) AS profile_cn,
            o.org_desc_en AS profile_en,
            o.org_address AS address,
        IF
            ( g.COUNTRY IS NOT NULL AND g.COUNTRY != '', g.COUNTRY, o.org_country ) AS country,
        IF
            ( g.TAGS IS NOT NULL AND g.TAGS != '', g.TAGS, o.org_tags ) AS field,
        IF
            ( g.TYPES IS NOT NULL AND g.TYPES != '', g.TYPES, o.org_types ) AS org_type,
        IF
            ( g.WEBSITE IS NOT NULL AND g.WEBSITE != '', g.WEBSITE, o.org_internal_link ) AS official_website,
            g.EMAILDOMAIN AS email,
            g.LATITUDE,
            g.LONGITUDE,
            '大系统' AS source
        FROM
            idw_o_org o
            LEFT JOIN idw_targets_group_org g ON g.DELETE_FLAG = 1
            AND g.IDW_UNIQUE_CODE = o.org_code
        WHERE
            o.DELETE_FLAG = 1
            AND o.org_parent_path NOT LIKE '%|WH-9700|%'
            AND o.org_code NOT IN ( SELECT org_code FROM idw_org )
        ORDER BY
            o.org_parent_path
    </select>

    <resultMap type="com.lirong.project.member.domain.OrgArchitecture" id="IdwOrgArchitectureResult">
        <result property="architectureId"    column="architecture_id"    />
        <result property="parentId"    column="parent_id"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="level"    column="level"    />
        <result property="parentName"    column="parent_name"    />
        <result property="orgCode"    column="org_code"    />
        <result property="nodeOrgCode"    column="node_org_code"    />
        <result property="parentCode"    column="parent_code"    />
        <result property="nameCn"    column="name_cn"    />
        <result property="nameEn"    column="name_en"    />
        <result property="function"    column="function"    />
        <result property="type"    column="type"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <!--根据机构编码查询大系统组织架构-->
    <select id="selectOrgArchitectureByOrgCode" resultMap="IdwOrgArchitectureResult">
        SELECT
            #{orgCode} AS org_code,
            org_code AS node_org_code,
            org_parent_code AS parent_code,
            org_name_cn AS name_cn,
            org_name_en AS name_en,
            '部门' AS type
        FROM
            idw_o_org
        WHERE
            DELETE_FLAG = 1
          AND org_parent_path LIKE CONCAT( '%', #{orgCode}, '%' )
    </select>

    <!--根据人员ID查询人员头像-->
    <select id="selectPeopleAvatarByPeopleIds" resultType="java.lang.String">
        SELECT
            avatar
        FROM
            idw_people_main
        WHERE
            is_delete = 0
            AND avatar IS NOT NULL
            AND avatar != ''
            AND people_id IN
        <foreach item="peopleId" collection="peopleIds" open="(" separator="," close=")">
            #{peopleId}
        </foreach>
    </select>

    <!--根据ID查询人员-->
    <select id="selectPeopleByPeopleId" resultMap="IdwPeopleMainResult">
        SELECT
            *
        FROM
            idw_people_main
        WHERE
            is_delete = 0
          AND people_id = #{id}
    </select>

    <!--根据关键词查询新闻ID-->
    <select id="selectNewsIdByKeyword" resultType="java.lang.String">
        SELECT
            id
        FROM
            `data_collection`.news
        WHERE
            <if test="keywordEn != null and keywordEn != ''">
                content LIKE CONCAT( '%', #{keywordEn}, '%' )
                OR news_title LIKE CONCAT( '%', #{keywordEn}, '%' )
            </if>
            <if test="keywordCn != null and keywordCn != ''">
                OR content LIKE CONCAT( '%', #{keywordCn}, '%' )
                OR news_title LIKE CONCAT( '%', #{keywordCn}, '%' )
            </if>
    </select>

    <!--根据装备名称查询采集装备ID-->
    <select id="selectEquipmentIdByName" resultType="java.lang.Long">
        SELECT
            id
        FROM
            equipment
        WHERE
            name = #{equipmentName}
    </select>

    <!--根据机构编码更新排序号-->
    <update id="updateOrgOrderNumByOrgCode">
        UPDATE idw_org
        SET order_num = #{orderNum}
        WHERE
            org_code = #{orgCode}
    </update>

    <!--根据人员编码更新人员排序号-->
    <update id="updatePeopleOrderNumByPeopleCode">
        UPDATE idw_people_main
        SET order_num = #{orderNum}
        WHERE
            people_code = #{peopleCode}
    </update>

    <!--根据ID更新level-->
    <update id="updateOrgRelationshipLevelById">
        UPDATE idw_org_relationship
        SET level = #{level}
        WHERE
            relationship_id = #{relationshipId}
    </update>

    <!--根据多媒体ID更新文件md5-->
    <update id="updateMultimediaMd5ById">
        UPDATE idw_multimedia
        SET md5 = #{md5}
        WHERE
            media_id = #{mediaId}
    </update>

    <!--根据ID更新文档路径-->
    <update id="updateStoragePathById">
        UPDATE idw_multimedia
        SET storage_path = #{storagePath}
        WHERE
            media_id = #{mediaId}
    </update>

    <!--根据主键更新机构关系表父关系ID-->
    <update id="updateOrgRelationshipParentIdById">
        UPDATE idw_org_relationship
        SET parent_id = #{parentId}
        WHERE
            relationship_id = #{relationshipId}
    </update>

    <!--根据机构ID修改机构数据来源-->
    <update id="updateOrgSourceByOrgId">
        UPDATE idw_org
        SET source = #{source}
        WHERE
            org_id = #{orgId}
    </update>

    <!--新增发表作品作者-->
    <insert id="insertWorksAuthor">
        insert into idw_people_published_works_author
        <trim prefix="(" suffix=")" suffixOverrides=",">
            is_delete, works_id, people_name, create_by, create_time,
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            0, #{worksId}, #{peopleName}, '系统', sysdate(),
        </trim>
    </insert>

    <!--根据发表作品ID删除-->
    <update id="deleteWorksAuthorByAuthorId">
        UPDATE idw_people_published_works_author
        SET is_delete = 0,
        update_by = '系统',
        update_time = sysdate()
        WHERE
            author_id = #{authorId}
    </update>

    <!--根据装备规格ID修改排序号-->
    <update id="updateWeaponrySpecificationsOrderNumBySpecificationsId">
        UPDATE idw_weaponry_specifications
        SET order_num = #{orderNum}
        WHERE
            specifications_id = #{specificationsId}
    </update>

   <!--修改资源库文件路径-->
    <update id="updateResourceStoragePath">
        UPDATE idw_resource
        SET storage_path = #{storagePath}
        WHERE
            resource_id = #{resourceId}
    </update>

   <!--修改资源库缩略图路径-->
    <update id="updateResourceThumbnail">
        UPDATE idw_resource
        SET thumbnail = #{thumbnail}
        WHERE
            resource_id = #{resourceId}
    </update>

    <!--根据机构关系ID修改祖籍列表-->
    <update id="updateOrgRelationshipAncestorsByRelationshipId">
        UPDATE idw_org_relationship
        SET ancestors = #{ancestors}
        WHERE
            relationship_id = #{relationshipId}
    </update>

   <!--根据机构编码更新机构类型别名-->
    <update id="updateOrgTypeAlias">
        UPDATE idw_org
        SET org_type_alias = #{orgTypeAlias}
        WHERE
            org_code IN
        <foreach item="orgCode" collection="orgCodes" open="(" separator="," close=")">
            #{orgCode}
        </foreach>
    </update>

    <!--根据机构新闻ID修改发布日期-->
    <update id="updatePublishDateByNewsId">
        UPDATE idw_org_news
        SET publish_date = #{publishDate}
        WHERE
            news_id = #{newsId}
    </update>

    <!--根据机构采办项目ID更新发布日期-->
    <update id="updatePublishDateByProjectId">
        UPDATE idw_org_project
        SET published_date = #{publishedDate}
        WHERE
            project_id = #{projectId}
    </update>

    <!--根据机构文献ID更新机构文献发布日期-->
    <update id="updatePublishDateByDocumentId">
        UPDATE idw_org_document
        SET publish_date = #{publishDate}
        WHERE
            document_id = #{documentId}
    </update>

    <!--根据机构编码更新机构名称-->
    <update id="updateOrgNameByOrgCode">
        UPDATE idw_org
        SET org_name_cn = #{orgNameCn},
        org_name_en = #{orgNameEn}
        WHERE
            is_delete = 0
            AND org_code = #{orgCode}
    </update>

    <!--根据ID更新parentId-->
    <update id="updateNewOrgRelParentIdById">
        UPDATE new_org_rel
        SET parent_id = #{parentId}
        WHERE
            id = #{id}
    </update>

    <!--根据英文名称更新机构编码-->
    <update id="updateNewOrgRelOrgCodeByNameEn">
        UPDATE new_org_rel
        SET org_code = #{orgCode}
        WHERE
            name_en = #{nameEn}
            AND ( org_code IS NULL OR org_code = '' )
    </update>

    <!--根据ID修改简介-->
    <update id="updateOrgStaffProfileEn">
        UPDATE idw_org_staff
        SET profile_en = #{profileEn}
        WHERE
            staff_id = #{staffId}
    </update>

    <!--根据人员ID更新人员编码与排序号-->
    <update id="updatePeopleCodeAndOrderNumByPeopleId">
        UPDATE idw_people_main
        SET people_code = #{peopleCode},
        order_num = #{orderNum}
        WHERE
            is_delete = 0
            AND people_id = #{peopleId}
    </update>

    <!--架构迁移-->
    <update id="updateOrgRelByOrgCode">
        UPDATE idw_org_relationship 
        SET org_code = REPLACE ( org_code, #{targetOrgCode}, #{sourceOrgCode} ),
        parent_code = REPLACE ( parent_code, #{targetOrgCode}, #{sourceOrgCode} ),
        ancestors = LEFT (
            REPLACE ( CONCAT( ancestors, ',' ), CONCAT( ',', #{targetOrgCode}, ',' ), CONCAT( ',', #{sourceOrgCode}, ',' ) ),
            LENGTH(
                REPLACE ( CONCAT( ancestors, ',' ), CONCAT( ',', #{targetOrgCode}, ',' ), CONCAT( ',', #{sourceOrgCode}, ',' ) ) 
            ) - 1 
        ),
        update_by = 'admin',
        update_time = SYSDATE( ) 
        WHERE
            is_delete = 0 
            AND ( org_code = #{targetOrgCode} OR FIND_IN_SET( #{targetOrgCode}, ancestors ) )
    </update>

    <!--修改机构人员机构编码-->
    <update id="updateOrgStaffOrgCode">
        UPDATE idw_org_staff
        SET org_code = #{sourceOrgCode},
        update_by = 'admin',
        update_time = SYSDATE( )
        WHERE
            is_delete = 0
            AND org_code = #{targetOrgCode}
    </update>

    <!--根据机构编码与祖籍列表修改子集机构关系-->
    <update id="updateChildrenAncestorsByOrgCodeAndAncestors">
        UPDATE idw_org_relationship
        SET ancestors = REPLACE ( ancestors, #{oldAncestors}, CONCAT( #{ancestors}, ',', #{orgCode} ) )
        WHERE
            is_delete = 0
            AND FIND_IN_SET( #{orgCode}, ancestors )
            AND ancestors LIKE CONCAT( #{oldAncestors}, ',' )
    </update>

    <!--根据ID修改机构关系修改用户-->
    <update id="updateOrgRelUpdateByById">
        UPDATE idw_org_relationship
        <choose>
            <when test="relationshipId != null">
                SET update_by = CONCAT( update_by, ',', #{parentName} )
                WHERE
                is_delete = 0
                AND relationship_id = #{relationshipId}
            </when>
            <otherwise>
                SET update_by = ''
                WHERE
                is_delete = 0
            </otherwise>
        </choose>
    </update>

    <!--根据机构编码与祖籍列表修改下一级机构关系上级ID与祖籍列表-->
    <update id="updateChildrenParentIdByByOrgCodeAndAncestors">
        UPDATE idw_org_relationship
        SET ancestors = REPLACE ( ancestors, #{oldAncestors}, CONCAT( #{ancestors}, ',', #{orgCode} ) )
        WHERE
            is_delete = 0
            AND parent_code = #{orgCode}
            AND ancestors = #{oldAncestors}
    </update>

    <!--修改机构新闻-->
    <!--<if test="orgCode != null">org_code = #{orgCode},</if>
    <if test="titleCn != null">title_cn = #{titleCn},</if>
    <if test="titleEn != null">title_en = #{titleEn},</if>
    <if test="subtitleEn != null">subtitle_en = #{subtitleEn},</if>
    <if test="author != null">author = #{author},</if>
    <if test="publishDate != null">publish_date = #{publishDate},</if>
    <if test="summaryEn != null">summary_en = #{summaryEn},</if>
    <if test="thumbnail != null">thumbnail = #{thumbnail},</if>
    <if test="contentEn != null">content_en = #{contentEn},</if>
    <if test="contentHtml != null">content_html = #{contentHtml},</if>
    <if test="tags != null">tags = #{tags},</if>
    <if test="url != null">url = #{url},</if>
    <if test="isDelete != null">is_delete = #{isDelete},</if>
    <if test="collector != null">collector = #{collector},</if>
    <if test="collectionTime != null">collection_time = #{collectionTime},</if>
    <if test="websiteName != null">website_name = #{websiteName},</if>-->
    <update id="updateOrgNews" parameterType="com.lirong.organization.news.domain.IdwOrgNews">
        update idw_org_news set
            <if test="subtitleCn != null">subtitle_cn = #{subtitleCn},</if>
            <if test="summaryCn != null">summary_cn = #{summaryCn},</if>
            <if test="contentCn != null">content_cn = #{contentCn},</if>
            update_by = #{updateBy},
            update_time = #{updateTime}
        where news_id = #{newsId}
    </update>

    <!--修改-->
    <update id="updateDate">
        UPDATE ${tableName}
        SET ${pendingField} = #{date}
        WHERE
            is_delete = 0
            AND ${primaryKey} = #{keyValue}
    </update>

    <!--根据ID更新专利机构-->
    <update id="updatePatentOrgById">
        UPDATE patent_org
        SET name = #{name}
        WHERE
            id = #{id}
    </update>

    <!--根据ID更新发布作品-->
    <update id="updateAwardById">
        UPDATE rd_award_org
        SET name = #{name}
        WHERE
            id = #{id}
    </update>

    <!--更新技术领域-->
    <update id="updateTechnosphere" parameterType="com.lirong.project.member.domain.Technosphere">
        update idw_technosphere
        <trim prefix="SET" suffixOverrides=",">
            <if test="nameCn != null">name_cn = #{nameCn},</if>
            <if test="nameEn != null">name_en = #{nameEn},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="level != null">level = #{level},</if>
            <if test="associatedType != null">associated_type = #{associatedType},</if>
            <if test="associatedCode != null">associated_code = #{associatedCode},</if>
            <if test="descCn != null">desc_cn = #{descCn},</if>
            <if test="descEn != null">desc_en = #{descEn},</if>
            <if test="isDelete != null">is_delete = #{isDelete},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

   <!--更新采集机构-->
    <update id="updateCraftCompany" parameterType="com.lirong.project.member.domain.CraftCompany">
        update craft_company
        <trim prefix="SET" suffixOverrides=",">
            <if test="type != null">type = #{type},</if>
            <if test="logo != null">logo = #{logo},</if>
            <if test="displayName != null">display_name = #{displayName},</if>
            <if test="fullName != null">full_name = #{fullName},</if>
            <if test="uei != null">uei = #{uei},</if>
            <if test="slug != null">slug = #{slug},</if>
            <if test="country != null">country = #{country},</if>
            <if test="foundedYear != null">founded_year = #{foundedYear},</if>
            <if test="employeeCount != null">employee_count = #{employeeCount},</if>
            <if test="hqLocation != null">hq_location = #{hqLocation},</if>
            <if test="latitude != null and latitude != ''">latitude = #{latitude},</if>
            <if test="longitude != null and longitude != ''">longitude = #{longitude},</if>
            <if test="sectors != null">sectors = #{sectors},</if>
            <if test="website != null">website = #{website},</if>
            <if test="shortDescription != null">short_description = #{shortDescription},</if>
            <if test="status != null">status = #{status},</if>
            <if test="source != null and source != ''">source = #{source},</if>
            <if test="linkedin != null">linkedin = #{linkedin},</if>
            <if test="instagram != null">instagram = #{instagram},</if>
            <if test="facebook != null">facebook = #{facebook},</if>
            <if test="youtube != null">youtube = #{youtube},</if>
            <if test="twitter != null">twitter = #{twitter},</if>
            <if test="overview != null">overview = #{overview},</if>
        </trim>
        where id = #{id}
    </update>

    <!--修改采集机构人员-->
    <update id="updateCraftCompanyPeople" parameterType="com.lirong.project.member.domain.CraftCompanyPeople">
        update craft_company_people
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyId != null">company_id = #{companyId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="position != null">position = #{position},</if>
            <if test="profile != null">profile = #{profile},</if>
            <if test="linkedin != null">linkedin = #{linkedin},</if>
            <if test="twitter != null">twitter = #{twitter},</if>
            <if test="facebook != null">facebook = #{facebook},</if>
            <if test="source != null">source = #{source},</if>
        </trim>
        where id = #{id}
    </update>

    <!--根据表名与属性名更新-->
    <update id="updateTaableByTableNameAndProperty">
        UPDATE ${tableName}
        SET ${property} = #{propertyValue}
        WHERE
            ${property} = #{propertyOldValue}
    </update>

    <!--修改装备规格装备编码-->
    <update id="updateWeaponrySpecificationsWeaponryCode">
        UPDATE idw_weaponry_specifications
        SET weaponry_code = #{newWeaponryCode},
        update_by = '关联舰船',
        update_time = SYSDATE( )
        WHERE
            weaponry_code = #{weaponryCode}
    </update>

    <!--修改舰船列表装备编码-->
    <update id="updateWeaponryFleetlistWeaponryCode">
        UPDATE idw_weaponry_fleetlist
        SET weaponry_code = #{newWeaponryCode},
        update_by = '关联舰船',
        update_time = SYSDATE( )
        WHERE
            weaponry_code = #{weaponryCode}
    </update>

    <!--修改舰船列表舰船装备编码-->
    <update id="updateWeaponryFleetlistRelatedWeaponryCode">
        UPDATE idw_weaponry_fleetlist
        SET related_weaponry_code = #{newWeaponryCode},
        update_by = '关联舰船',
        update_time = SYSDATE( )
        WHERE
            related_weaponry_code = #{weaponryCode}
    </update>

    <!--修改装备运营商装备编码-->
    <update id="updateWeaponryOperatorWeaponryCode">
        UPDATE idw_weaponry_operator
        SET weaponry_code = #{newWeaponryCode},
        update_by = '关联舰船',
        update_time = SYSDATE( )
        WHERE
            weaponry_code = #{weaponryCode}
    </update>

    <!--修改装备结构装备编码-->
    <update id="updateWeaponryStructureWeaponryCode">
        UPDATE idw_weaponry_structure
        SET weaponry_code = #{newWeaponryCode},
        update_by = '关联舰船',
        update_time = SYSDATE( )
        WHERE
            weaponry_code = #{weaponryCode}
    </update>

    <!--修改装备主题装备编码-->
    <update id="updateWeaponryAffiliationThemeWeaponryCode">
        UPDATE idw_weaponry_affiliation_theme
        SET weaponry_code = #{newWeaponryCode},
        update_by = '关联舰船',
        update_time = SYSDATE( )
        WHERE
            weaponry_code = #{weaponryCode}
    </update>

    <!--修改装备承包商装备编码-->
    <update id="updateWeaponryContractorWeaponryCode">
        UPDATE idw_weaponry_contractor
        SET weaponry_code = #{newWeaponryCode},
        update_by = '关联舰船',
        update_time = SYSDATE( )
        WHERE
            weaponry_code = #{weaponryCode}
    </update>

    <!--根据舰船装备编码修改简介-->
    <update id="updateShipIntroductionByWeaponryCode">
        UPDATE ship
        SET introduction_cn = #{introductionCn},
        introduction_en = #{introductionEn}
        WHERE
            hull_no = #{weaponryCode}
    </update>

    <!--根据ID修改舰船徽章-->
    <update id="updateShipBadgeById">
        UPDATE ship
        SET badge = #{badge}
        WHERE
            id = #{id}
    </update>

    <!--根据ID修改舰船图片-->
    <update id="updateShipPhotoById">
        UPDATE ship
        SET photo = #{photo}
        WHERE
            id = #{id}
    </update>

    <!--根据装备编码更新装备排序号-->
    <update id="updateWeaponryOrderNumByWeaponryCode">
        UPDATE idw_weaponry
        SET order_num = #{orderNum}
        WHERE
            is_delete = 0
            AND weaponry_code = #{weaponryCode}
    </update>

    <!--根据ID更新舰船排序号-->
    <update id="updateShipOrderNumById">
        UPDATE ship
        SET order_num = #{orderNum}
        WHERE
            is_delete = 0
            AND id = #{id}
    </update>

    <!--根据文档ID修改文档页数与文件大小-->
    <update id="updateDocDocumentPageById">
        UPDATE doc_document
        SET page = #{page},
        size = #{length},
        updater = '更新page',
        update_time = SYSDATE( )
        WHERE
            id = #{id}
    </update>

    <!--更新技术领域-->
    <update id="updateIdwTechnosphere" parameterType="com.lirong.technosphere.domain.IdwTechnosphere">
        update idw_technosphere
        <trim prefix="SET" suffixOverrides=",">
            <if test="nameCn != null">name_cn = #{nameCn},</if>
            <if test="nameEn != null">name_en = #{nameEn},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="level != null">level = #{level},</if>
            <if test="associatedType != null">associated_type = #{associatedType},</if>
            <if test="associatedCode != null">associated_code = #{associatedCode},</if>
            <if test="descCn != null">desc_cn = #{descCn},</if>
            <if test="descEn != null">desc_en = #{descEn},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <!--修改武器装备战技指标ID-->
    <update id="updateWeaponrySpecificationsId">
        UPDATE idw_weaponry_specifications
        SET specifications_id = #{id}
        WHERE
            specifications_id = #{specificationsId}
    </update>

    <!--修改武器装备战技指标上级ID-->
    <update id="updateWeaponrySpecificationsParentId">
        UPDATE idw_weaponry_specifications
        SET parent_id = #{id}
        WHERE
            parent_id = #{parentId}
    </update>

    <!--更新机构编码-->
    <update id="updateOrgOrgCode">
        UPDATE idw_org
        SET org_code = #{newOrgCode}
        WHERE
            org_code = #{orgCode}
    </update>

    <!--更新机构发展历程机构编码-->
    <update id="updateOrgDevelopmentHistoryOrgCode">
        UPDATE idw_org_development_history
        SET org_code = #{newOrgCode}
        WHERE
            org_code = #{orgCode}
    </update>

    <!--更新机构文献机构编码-->
    <update id="updateOrgDocumentOrgCode">
        UPDATE idw_org_document
        SET org_code = #{newOrgCode}
        WHERE
            org_code = #{orgCode}
    </update>

    <!--更新军事设施机构编码-->
    <update id="updateOrgFacilitiesOrgCode">
        UPDATE idw_org_facilities
        SET org_code = #{newOrgCode}
        WHERE
            org_code = #{orgCode}
    </update>

    <!--更新机构财务报告机构编码-->
    <update id="updateOrgFinancialOrgCode">
        UPDATE idw_org_financial
        SET org_code = #{newOrgCode}
        WHERE
            org_code = #{orgCode}
    </update>

    <!--更新机构新闻机构编码-->
    <update id="updateOrgNewsOrgCode">
        UPDATE idw_org_news
        SET org_code = #{newOrgCode}
        WHERE
            org_code = #{orgCode}
    </update>

    <!--更新机构产品机构编码-->
    <update id="updateOrgProductOrgCode">
        UPDATE idw_org_product
        SET org_code = #{newOrgCode}
        WHERE
            org_code = #{orgCode}
    </update>

    <!--更新多媒体机构编码-->
    <update id="updateMultimediaOrgCode">
        UPDATE idw_multimedia
        SET association_id = #{newOrgCode}
        WHERE
            business_type = 'organization'
            AND association_id = #{orgCode}
    </update>

    <!--更新机构项目机构编码-->
    <update id="updateOrgProjectOrgCode">
        UPDATE idw_org_project
        SET org_code = #{newOrgCode}
        WHERE
            org_code = #{orgCode}
    </update>

    <!--更新机构关系机构编码-->
    <update id="updateOrgRelationshipOrgCode">
        UPDATE idw_org_relationship
        SET org_code = #{newOrgCode}
        WHERE
            org_code = #{orgCode}
    </update>

    <!--更新机构关系上级机构编码-->
    <update id="updateOrgRelationshipParentCode">
        UPDATE idw_org_relationship
        SET parent_code = #{newOrgCode}
        WHERE
            parent_code = #{orgCode}
    </update>

    <!--更新机构关系祖籍列表-->
    <update id="updateOrgRelationshipAncestors">
        UPDATE idw_org_relationship
        SET ancestors = SUBSTRING(
            REPLACE ( CONCAT( ancestors, ',' ), CONCAT( ',', #{orgCode}, ',' ), CONCAT( ',', #{newOrgCode}, ',' ) ),
            1,
            LENGTH(
                REPLACE ( CONCAT( ancestors, ',' ), CONCAT( ',', #{orgCode}, ',' ), CONCAT( ',', #{newOrgCode}, ',' ) )
            ) - 1
        )
        WHERE
            FIND_IN_SET( #{orgCode}, ancestors )
    </update>

    <!--更新机构人员机构编码-->
    <update id="amendOrgStaffOrgCode">
        UPDATE idw_org_staff
        SET org_code = #{newOrgCode}
        WHERE
            org_code = #{orgCode}
    </update>

    <!--修改机构关系ID-->
    <update id="updateRelationshipId">
        UPDATE idw_org_relationship
        SET relationship_id = #{newRelationshipId}
        WHERE
            relationship_id = #{relationshipId}
    </update>

    <!--修改机构关系上级ID-->
    <update id="updateRelationshipParentId">
        UPDATE idw_org_relationship
        SET parent_id = #{newParentId}
        WHERE
            parent_id = #{parentId}
    </update>

    <!--根据资源库ID修改资源库标签-->
    <update id="updateResourceTagsByResourceId">
        UPDATE idw_resource
        SET tags = #{tags}
        WHERE
            resource_id = #{resourceId}
    </update>

    <!--更新军事设施-->
    <update id="updateInstallation" parameterType="com.lirong.project.member.domain.InstallationVo">
        update installation
        <trim prefix="SET" suffixOverrides=",">
            <if test="installationCode != null">installation_code = #{installationCode},</if>
            <if test="nameCn != null and nameCn != ''">name_cn = #{nameCn},</if>
            <if test="fullName != null and fullName != ''">full_name = #{fullName},</if>
            <if test="shortName != null and shortName != ''">short_name = #{shortName},</if>
            <if test="address != null and address != ''">address = #{address},</if>
            <if test="state != null and state != ''">state = #{state},</if>
            <if test="country != null and country != ''">country = #{country},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="nearestCity != null and nearestCity != ''">nearest_city = #{nearestCity},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="area != null and area != ''">area = #{area},</if>
            <if test="troopsCategory != null and troopsCategory != ''">troops_category = #{troopsCategory},</if>
            <if test="civilian != null and civilian != ''">civilian = #{civilian},</if>
            <if test="military != null and military != ''">military = #{military},</if>
            <if test="usafEnlisted != null and usafEnlisted != ''">usaf_enlisted = #{usafEnlisted},</if>
            <if test="usafOfficer != null and usafOfficer != ''">usaf_officer = #{usafOfficer},</if>
            <if test="controlledBy != null and controlledBy != ''">controlled_by = #{controlledBy},</if>
            <if test="controlledOrgCode != null and controlledOrgCode != ''">controlled_org_code = #{controlledOrgCode},</if>
            <if test="website != null and website != ''">website = #{website},</if>
            <if test="keyFacilities != null and keyFacilities != ''">key_facilities = #{keyFacilities},</if>
            <if test="descriptionCn != null and descriptionCn != ''">description_cn = #{descriptionCn},</if>
            <if test="descriptionEn != null and descriptionEn != ''">description_en = #{descriptionEn},</if>
            <if test="post != null and post != ''">post = #{post},</if>
            <if test="source != null and source != ''">source = #{source},</if>
            <if test="showHome != null and showHome != ''">show_home = #{showHome},</if>
            <if test="createBy != null and createBy != ''">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <!--根据军事设施ID更新军事设施驻扎单位军事设施编码-->
    <update id="updateInstallationUnitInstallationCodeByInstallationId">
        UPDATE installation_unit
        SET installation_code = #{installationCode}
        WHERE
            installation_id = #{installationId}
    </update>

    <!--更新机构人员职位-->
    <update id="updateOrgStaffPosition">
        UPDATE idw_org_staff
        SET position = #{position}
        WHERE
            org_code = #{orgCode}
            AND people_name_cn = #{peopleNameCn}
    </update>

    <!--更新机构人员状态-->
    <update id="updateOrgStaffStatus">
        UPDATE idw_org_staff
        SET status = #{status}
        WHERE
            org_code = #{orgCode}
            AND people_name_cn = #{peopleNameCn}
            AND position = #{position}
    </update>

    <!--修改机构体系结构机构编码-->
    <update id="updateOrgSystemArchitectureOrgCode">
        UPDATE idw_org_system_architecture
        SET org_code = #{newOrgCode},
        update_by = CONCAT( #{orgCode}, '重复机构' ),
        update_time = SYSDATE( )
        WHERE
            org_code = #{orgCode}
    </update>

    <!--新增人员-->
    <insert id="insertPeople" parameterType="com.lirong.project.member.domain.People" useGeneratedKeys="true" keyProperty="peopleId">
        insert into people
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="peopleCode != null">people_code,</if>
            <if test="country != null">country,</if>
            <if test="nameCn != null and nameCn != ''">name_cn,</if>
            <if test="nameEn != null and nameEn != ''">name_en,</if>
            <if test="avatar != null and avatar != ''">avatar,</if>
            <if test="gender != null">gender,</if>
            <if test="birthplace != null">birthplace,</if>
            <if test="orgCode != null">org_code,</if>
            <if test="parentOrgName != null">parent_org_name,</if>
            <if test="orgName != null">org_name,</if>
            <if test="orgNameEn != null">org_name_en,</if>
            <if test="post != null and post != ''">post,</if>
            <if test="occupation != null and occupation != ''">occupation,</if>
            <if test="appointmentDate != null and appointmentDate != ''">appointment_date,</if>
            <if test="workplace != null and workplace != ''">workplace,</if>
            <if test="graduatedUniversity != null">graduated_university,</if>
            <if test="education != null">education,</if>
            <if test="troopsCategory != null and troopsCategory != ''">troops_category,</if>
            <if test="militaryRank != null and militaryRank != ''">military_rank,</if>
            <if test="profileCn != null and profileCn != ''">profile_cn,</if>
            <if test="profileEn != null and profileEn != ''">profile_en,</if>
            <if test="source != null">source,</if>
            <if test="educationalExperience != null and educationalExperience != ''">educational_experience,</if>
            <if test="assignments != null and assignments != ''">assignments,</if>
            <if test="rewardsPunishments != null and rewardsPunishments != ''">rewards_punishments,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="peopleCode != null">#{peopleCode},</if>
            <if test="country != null">#{country},</if>
            <if test="nameCn != null and nameCn != ''">#{nameCn},</if>
            <if test="nameEn != null and nameEn != ''">#{nameEn},</if>
            <if test="avatar != null and avatar != ''">#{avatar},</if>
            <if test="gender != null">#{gender},</if>
            <if test="birthplace != null">#{birthplace},</if>
            <if test="orgCode != null">#{orgCode},</if>
            <if test="parentOrgName != null">#{parentOrgName},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="orgNameEn != null">#{orgNameEn},</if>
            <if test="post != null and post != ''">#{post},</if>
            <if test="occupation != null and occupation != ''">#{occupation},</if>
            <if test="appointmentDate != null and appointmentDate != ''">#{appointmentDate},</if>
            <if test="workplace != null and workplace != ''">#{workplace},</if>
            <if test="graduatedUniversity != null">#{graduatedUniversity},</if>
            <if test="education != null">#{education},</if>
            <if test="troopsCategory != null and troopsCategory != ''">#{troopsCategory},</if>
            <if test="militaryRank != null and militaryRank != ''">#{militaryRank},</if>
            <if test="profileCn != null and profileCn != ''">#{profileCn},</if>
            <if test="profileEn != null and profileEn != ''">#{profileEn},</if>
            <if test="source != null">#{source},</if>
            <if test="educationalExperience != null and educationalExperience != ''">#{educationalExperience},</if>
            <if test="assignments != null and assignments != ''">#{assignments},</if>
            <if test="rewardsPunishments != null and rewardsPunishments != ''">#{rewardsPunishments},</if>
        </trim>
    </insert>

    <!--新增-->
    <insert id="insertAllPeopleTable">
        INSERT INTO all_people(name, people_code)
        VALUES
        ( #{name}, #{peopleCode} )
    </insert>

    <!--新增新的机构关系-->
    <insert id="insertNewOrgRel" parameterType="com.lirong.project.member.domain.NewOrgRel" useGeneratedKeys="true" keyProperty="id">
        insert into new_org_rel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="parentName != null">parent_name,</if>
            <if test="orgCode != null">org_code,</if>
            <if test="parentCode != null">parent_code,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="nameCn != null">name_cn,</if>
            <if test="nameEn != null">name_en,</if>
            <if test="source != null">source,</if>
            is_delete
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="parentName != null">#{parentName},</if>
            <if test="orgCode != null">#{orgCode},</if>
            <if test="parentCode != null">#{parentCode},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="nameCn != null">#{nameCn},</if>
            <if test="nameEn != null">#{nameEn},</if>
            <if test="source != null">#{source},</if>
            0
        </trim>
    </insert>

    <!--新增人员-->
    <insert id="insertNewPeople" parameterType="com.lirong.project.member.domain.NewPeople" useGeneratedKeys="true" keyProperty="id">
        insert into new_people
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="originalOrgCode != null">original_org_code,</if>
            <if test="orgNameCn != null">org_name_cn,</if>
            <if test="orgNameEn != null">org_name_en,</if>
            <if test="parentName != null">parent_name,</if>
            <if test="peopleNameCn != null">people_name_cn,</if>
            <if test="peopleNameEn != null">people_name_en,</if>
            <if test="gender != null">gender,</if>
            <if test="description != null">description,</if>
            <if test="avatar != null">avatar,</if>
            <if test="troopsCategory != null">troops_category,</if>
            <if test="militaryRank != null">military_rank,</if>
            <if test="post != null">post,</if>
            <if test="workplace != null">workplace,</if>
            <if test="source != null">source,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="originalOrgCode != null">#{originalOrgCode},</if>
            <if test="orgNameCn != null">#{orgNameCn},</if>
            <if test="orgNameEn != null">#{orgNameEn},</if>
            <if test="parentName != null">#{parentName},</if>
            <if test="peopleNameCn != null">#{peopleNameCn},</if>
            <if test="peopleNameEn != null">#{peopleNameEn},</if>
            <if test="gender != null">#{gender},</if>
            <if test="description != null">#{description},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="troopsCategory != null">#{troopsCategory},</if>
            <if test="militaryRank != null">#{militaryRank},</if>
            <if test="post != null">#{post},</if>
            <if test="workplace != null">#{workplace},</if>
            <if test="source != null">#{source},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <!--新增-->
    <insert id="insertOldOrgRel" parameterType="com.lirong.project.member.domain.OldOrgRel">
        insert into old_org_rel
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="orgCode != null">org_code,</if>
            <if test="nameEn != null">name_en,</if>
            <if test="nameCn != null">name_cn,</if>
            <if test="parentCode != null">parent_code,</if>
            <if test="source != null">source,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="orgCode != null">#{orgCode},</if>
            <if test="nameEn != null">#{nameEn},</if>
            <if test="nameCn != null">#{nameCn},</if>
            <if test="parentCode != null">#{parentCode},</if>
            <if test="source != null">#{source},</if>
        </trim>
    </insert>

    <insert id="insertOriginalDocument" parameterType="com.lirong.project.member.domain.OriginalDocument" useGeneratedKeys="true" keyProperty="id">
        insert into original_document
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="subtitle != null">subtitle,</if>
            <if test="publisher != null">publisher,</if>
            <if test="publishDate != null">publish_date,</if>
            <if test="authors != null">authors,</if>
            <if test="thumbnail != null">thumbnail,</if>
            <if test="summary != null">summary,</if>
            <if test="type != null">type,</if>
            <if test="area != null">area,</if>
            <if test="category != null">category,</if>
            <if test="topics != null">topics,</if>
            <if test="content != null">content,</if>
            <if test="filePath != null">file_path,</if>
            <if test="url != null and url != ''">url,</if>
            <if test="collectTime != null">collect_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="subtitle != null">#{subtitle},</if>
            <if test="publisher != null">#{publisher},</if>
            <if test="publishDate != null">#{publishDate},</if>
            <if test="authors != null">#{authors},</if>
            <if test="thumbnail != null">#{thumbnail},</if>
            <if test="summary != null">#{summary},</if>
            <if test="type != null">#{type},</if>
            <if test="area != null">#{area},</if>
            <if test="category != null">#{category},</if>
            <if test="topics != null">#{topics},</if>
            <if test="content != null">#{content},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="url != null and url != ''">#{url},</if>
            <if test="collectTime != null">#{collectTime},</if>
        </trim>
    </insert>

    <!--新增专利机构-->
    <insert id="insertPatentOrg">
        insert into patent_org
        <trim prefix="(" suffix=")" suffixOverrides=",">
            patent_id, name
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{patentId}, #{name}
        </trim>
    </insert>

    <!--新增发布作品机构-->
    <insert id="insertAward">
        insert into rd_award_org
        <trim prefix="(" suffix=")" suffixOverrides=",">
            award_id, name
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            #{awardId}, #{name}
        </trim>
    </insert>

    <!--新增采集机构-->
    <insert id="insertCraftCompany" parameterType="com.lirong.project.member.domain.CraftCompany">
        insert into craft_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="type != null">type,</if>
            <if test="logo != null">logo,</if>
            <if test="displayName != null">display_name,</if>
            <if test="fullName != null">full_name,</if>
            <if test="uei != null">uei,</if>
            <if test="slug != null">slug,</if>
            <if test="country != null">country,</if>
            <if test="foundedYear != null">founded_year,</if>
            <if test="employeeCount != null">employee_count,</if>
            <if test="hqLocation != null">hq_location,</if>
            <if test="latitude != null and latitude != ''">latitude,</if>
            <if test="longitude != null and longitude != ''">longitude,</if>
            <if test="sectors != null">sectors,</if>
            <if test="website != null">website,</if>
            <if test="shortDescription != null">short_description,</if>
            <if test="status != null">status,</if>
            <if test="source != null and source != ''">source,</if>
            <if test="linkedin != null">linkedin,</if>
            <if test="instagram != null">instagram,</if>
            <if test="facebook != null">facebook,</if>
            <if test="youtube != null">youtube,</if>
            <if test="twitter != null">twitter,</if>
            <if test="overview != null">overview,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="type != null">#{type},</if>
            <if test="logo != null">#{logo},</if>
            <if test="displayName != null">#{displayName},</if>
            <if test="fullName != null">#{fullName},</if>
            <if test="uei != null">#{uei},</if>
            <if test="slug != null">#{slug},</if>
            <if test="country != null">#{country},</if>
            <if test="foundedYear != null">#{foundedYear},</if>
            <if test="employeeCount != null">#{employeeCount},</if>
            <if test="hqLocation != null">#{hqLocation},</if>
             <if test="latitude != null and latitude != ''">#{latitude},</if>
             <if test="longitude != null and longitude != ''">#{longitude},</if>
            <if test="sectors != null">#{sectors},</if>
            <if test="website != null">#{website},</if>
            <if test="shortDescription != null">#{shortDescription},</if>
            <if test="status != null">#{status},</if>
            <if test="source != null and source != ''">#{source},</if>
            <if test="linkedin != null">#{linkedin},</if>
            <if test="instagram != null">#{instagram},</if>
            <if test="facebook != null">#{facebook},</if>
            <if test="youtube != null">#{youtube},</if>
            <if test="twitter != null">#{twitter},</if>
            <if test="overview != null">#{overview},</if>
        </trim>
    </insert>

    <!--新增采集机构人员-->
    <insert id="insertCraftCompanyPeople" parameterType="com.lirong.project.member.domain.CraftCompanyPeople">
        insert into craft_company_people
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="companyId != null">company_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="avatar != null">avatar,</if>
            <if test="position != null">position,</if>
            <if test="profile != null">profile,</if>
            <if test="linkedin != null">linkedin,</if>
            <if test="twitter != null">twitter,</if>
            <if test="facebook != null">facebook,</if>
            <if test="source != null">source,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="companyId != null">#{companyId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="position != null">#{position},</if>
            <if test="profile != null">#{profile},</if>
            <if test="linkedin != null">#{linkedin},</if>
            <if test="twitter != null">#{twitter},</if>
            <if test="facebook != null">#{facebook},</if>
            <if test="source != null">#{source},</if>
        </trim>
    </insert>

    <!--新增军机数据-->
    <insert id="insertMilitaryAircraft" parameterType="com.lirong.project.member.domain.IdwMilitaryAircraft">
        insert into idw_military_aircraft
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="hexId != null">hex_id,</if>
            <if test="country != null">country,</if>
            <if test="callsign != null">callsign,</if>
            <if test="registration != null">registration,</if>
            <if test="type != null">type,</if>
            <if test="typeDesc != null">type_desc,</if>
            <if test="typeSupplement != null">type_supplement,</if>
            <if test="altitude != null">altitude,</if>
            <if test="spd != null">spd,</if>
            <if test="vrate != null">vrate,</if>
            <if test="track != null">track,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="reporttime != null">reporttime,</if>
            <if test="rssi != null">rssi,</if>
            <if test="messages != null">messages,</if>
            <if test="seen != null">seen,</if>
            <if test="squawk != null">squawk,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="hexId != null">#{hexId},</if>
            <if test="country != null">#{country},</if>
            <if test="callsign != null">#{callsign},</if>
            <if test="registration != null">#{registration},</if>
            <if test="type != null">#{type},</if>
            <if test="typeDesc != null">#{typeDesc},</if>
            <if test="typeSupplement != null">#{typeSupplement},</if>
            <if test="altitude != null">#{altitude},</if>
            <if test="spd != null">#{spd},</if>
            <if test="vrate != null">#{vrate},</if>
            <if test="track != null">#{track},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="reporttime != null">#{reporttime},</if>
            <if test="rssi != null">#{rssi},</if>
            <if test="messages != null">#{messages},</if>
            <if test="seen != null">#{seen},</if>
            <if test="squawk != null">#{squawk},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <!--新增军舰数据-->
    <insert id="insertWarship" parameterType="com.lirong.project.member.domain.IdwWarship">
        insert into idw_warship
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="vesselName != null">vessel_name,</if>
            <if test="shipType != null">ship_type,</if>
            <if test="flag != null">flag,</if>
            <if test="mmsi != null">mmsi,</if>
            <if test="imo != null">imo,</if>
            <if test="callsign != null">callsign,</if>
            <if test="yearOfBuilt != null">year_of_built,</if>
            <if test="length != null">length,</if>
            <if test="beam != null">beam,</if>
            <if test="course != null">course,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="received != null">received,</if>
            <if test="type != null">type,</if>
            <if test="grossTonnage != null">gross_tonnage,</if>
            <if test="summerDeadweight != null">summer_deadweight,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="vesselName != null">#{vesselName},</if>
            <if test="shipType != null">#{shipType},</if>
            <if test="flag != null">#{flag},</if>
            <if test="mmsi != null">#{mmsi},</if>
            <if test="imo != null">#{imo},</if>
            <if test="callsign != null">#{callsign},</if>
            <if test="yearOfBuilt != null">#{yearOfBuilt},</if>
            <if test="length != null">#{length},</if>
            <if test="beam != null">#{beam},</if>
            <if test="course != null">#{course},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="received != null">#{received},</if>
            <if test="type != null">#{type},</if>
            <if test="grossTonnage != null">#{grossTonnage},</if>
            <if test="summerDeadweight != null">#{summerDeadweight},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <!--根据数据库名称导入人员教育经历-->
    <insert id="insertPeopleEducationByDatabaseNameAndPeopleCode">
        INSERT INTO idw_people_education
        SELECT
            *
        FROM
            ${database}idw_people_education
        WHERE
            is_delete = 0
            AND people_code = #{peopleCode}
    </insert>

    <!--根据数据库名称导入人员社交媒体账号-->
    <insert id="insertPeopleSocialAccountByDatabaseNameAndPeopleCode">
        INSERT INTO idw_people_social_account
        SELECT
            *
        FROM
            ${database}idw_people_social_account
        WHERE
            is_delete = 0
            AND people_code = #{peopleCode}
    </insert>

<!--    根据数据库名称导入人员社交媒体-->
    <insert id="insertPeopleSocialMediaByDatabaseNameAndPeopleCode">
        INSERT INTO idw_people_social_media
        SELECT
            *
        FROM
            ${database}idw_people_social_media
        WHERE
            is_delete = 0
            AND people_code = #{peopleCode}
    </insert>

<!--    根据数据库名称导入人员新闻-->
    <insert id="insertPeopleNewsByDatabaseNameAndPeopleCode">
        INSERT INTO idw_people_news
        SELECT
            *
        FROM
            ${database}idw_people_news
        WHERE
            is_delete = 0
            AND people_code = #{peopleCode}
    </insert>

    <!--根据数据库名称导入人员工作经历-->
    <insert id="insertPeopleWorkExperienceByDatabaseNameAndPeopleCode">
        INSERT INTO idw_people_work_experience
        SELECT
            *
        FROM
            ${database}idw_people_work_experience
        WHERE
            is_delete = 0
            AND people_code = #{peopleCode}
    </insert>

    <!--根据数据库名称导入人员荣誉奖-->
    <insert id="insertPeopleHonorByDatabaseNameAndPeopleCode">
        INSERT INTO idw_people_honor
        SELECT
            *
        FROM
            ${database}idw_people_honor
        WHERE
            is_delete = 0
            AND people_code = #{peopleCode}
    </insert>

    <!--根据数据库名称导入人员发表作品-->
    <insert id="insertPeoplePublishedWorksByDatabaseNameAndPeopleCode">
        INSERT INTO idw_people_published_works
        SELECT
            *
        FROM
            ${database}idw_people_published_works
        WHERE
            is_delete = 0
            AND people_code = #{peopleCode}
    </insert>

    <!--根据数据库名称导入人员发表作品作者-->
    <insert id="insertPeoplePublishedWorksAuthorByDatabaseNameAndPeopleCode">
        INSERT INTO idw_people_published_works_author
        SELECT
            *
        FROM
            ${database}idw_people_published_works_author
        WHERE
            is_delete = 0
            AND works_id IN ( SELECT works_id FROM ${database}idw_people_published_works WHERE people_code = #{peopleCode} )
    </insert>

    <!--根据数据库名称与人员编码导入人员专利发明-->
    <insert id="insertPeoplePatentByDatabaseNameAndPeopleCode">
        INSERT INTO idw_people_patent
        SELECT
            *
        FROM
            ${database}idw_people_patent
        WHERE
            is_delete = 0
            AND people_code = #{peopleCode}
    </insert>

    <!--根据数据库名称与人员编码导入人员专利发明人-->
    <insert id="insertPeoplePatentInventorByDatabaseNameAndPeopleCode">
        INSERT INTO idw_people_patent_inventor
        SELECT
            *
        FROM
            ${database}idw_people_patent_inventor
        WHERE
            is_delete = 0
            AND patent_id IN ( SELECT patent_id FROM ${database}idw_people_patent WHERE people_code = #{peopleCode} )
    </insert>

    <!--根据数据库名称与人员编码导入人员关系-->
    <insert id="insertPeopleRelationshipByDatabaseNameAndPeopleCode">
        INSERT INTO idw_people_relationship
        SELECT
            *
        FROM
            ${database}idw_people_relationship
        WHERE
            is_delete = 0
            AND people_code = #{peopleCode}
    </insert>

    <!--根据数据库名称与人员编码导入人员工作成果-->
    <insert id="insertPeopleAchievementByDatabaseNameAndPeopleCode">
        INSERT INTO idw_people_achievement
        SELECT
            *
        FROM
            ${database}idw_people_achievement
        WHERE
            is_delete = 0
            AND people_code = #{peopleCode}
    </insert>

    <!--根据数据库名称与人员编码导入人员图片视频-->
    <insert id="insertPeopleMultimediaByDatabaseNameAndPeopleCode">
        INSERT INTO idw_multimedia
        SELECT
            *
        FROM
            ${database}idw_multimedia
        WHERE
            is_delete = 0
            AND business_type = 'personnel'
            AND association_id = #{peopleCode}
    </insert>

    <!--新增机构架构-->
    <!--<insert id="insertOrgStructure" parameterType="com.lirong.organization.structure.domain.IdwOrgStructure" useGeneratedKeys="true" keyProperty="structureId">
        insert into idw_org_structure
        <trim prefix="(" suffix=")" suffixOverrides=",">
            is_delete,
            <if test="structureId != null">structure_id,</if>
            <if test="orgCode != null and orgCode != ''">org_code,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="ancestors != null">ancestors,</if>
            <if test="level != null">level,</if>
            <if test="nameCn != null and nameCn != ''">name_cn,</if>
            <if test="nameEn != null">name_en,</if>
            <if test="structureOrgCode != null">structure_org_code,</if>
            <if test="avatar != null">avatar,</if>
            <if test="type != null">type,</if>
            <if test="profileCn != null">profile_cn,</if>
            <if test="profileEn != null">profile_en,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="source != null and source != ''">source,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            0,
            <if test="structureId != null">#{structureId},</if>
            <if test="orgCode != null and orgCode != ''">#{orgCode},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="ancestors != null">#{ancestors},</if>
            <if test="level != null">#{level},</if>
            <if test="nameCn != null and nameCn != ''">#{nameCn},</if>
            <if test="nameEn != null">#{nameEn},</if>
            <if test="structureOrgCode != null">#{structureOrgCode},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="type != null">#{type},</if>
            <if test="profileCn != null">#{profileCn},</if>
            <if test="profileEn != null">#{profileEn},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="source != null and source != ''">#{source},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>-->

    <!--新增军事设施-->
    <insert id="insertInstallation" parameterType="com.lirong.project.member.domain.InstallationVo">
        insert into installation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="installationCode != null">installation_code,</if>
            <if test="nameCn != null">name_cn,</if>
            <if test="fullName != null">full_name,</if>
            <if test="shortName != null">short_name,</if>
            <if test="address != null">address,</if>
            <if test="state != null">state,</if>
            <if test="country != null">country,</if>
            <if test="latitude != null">latitude,</if>
            <if test="longitude != null">longitude,</if>
            <if test="nearestCity != null">nearest_city,</if>
            <if test="phone != null">phone,</if>
            <if test="area != null">area,</if>
            <if test="troopsCategory != null">troops_category,</if>
            <if test="civilian != null">civilian,</if>
            <if test="military != null">military,</if>
            <if test="usafEnlisted != null">usaf_enlisted,</if>
            <if test="usafOfficer != null">usaf_officer,</if>
            <if test="controlledBy != null">controlled_by,</if>
            <if test="controlledOrgCode != null">controlled_org_code,</if>
            <if test="website != null">website,</if>
            <if test="keyFacilities != null">key_facilities,</if>
            <if test="descriptionCn != null">description_cn,</if>
            <if test="descriptionEn != null">description_en,</if>
            <if test="post != null">post,</if>
            <if test="source != null">source,</if>
            <if test="showHome != null">show_home,</if>
            <if test="isDelete != null">is_delete,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="installationCode != null">#{installationCode},</if>
            <if test="nameCn != null">#{nameCn},</if>
            <if test="fullName != null">#{fullName},</if>
            <if test="shortName != null">#{shortName},</if>
            <if test="address != null">#{address},</if>
            <if test="state != null">#{state},</if>
            <if test="country != null">#{country},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="nearestCity != null">#{nearestCity},</if>
            <if test="phone != null">#{phone},</if>
            <if test="area != null">#{area},</if>
            <if test="troopsCategory != null">#{troopsCategory},</if>
            <if test="civilian != null">#{civilian},</if>
            <if test="military != null">#{military},</if>
            <if test="usafEnlisted != null">#{usafEnlisted},</if>
            <if test="usafOfficer != null">#{usafOfficer},</if>
            <if test="controlledBy != null">#{controlledBy},</if>
            <if test="controlledOrgCode != null">#{controlledOrgCode},</if>
            <if test="website != null">#{website},</if>
            <if test="keyFacilities != null">#{keyFacilities},</if>
            <if test="descriptionCn != null">#{descriptionCn},</if>
            <if test="descriptionEn != null">#{descriptionEn},</if>
            <if test="post != null">#{post},</if>
            <if test="source != null">#{source},</if>
            <if test="showHome != null">#{showHome},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <!--新增驻扎单位-->
    <insert id="insertInstallationUnit" parameterType="com.lirong.project.member.domain.InstallationUnitVo">
        insert into installation_unit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            is_delete,
            <if test="id != null">id,</if>
            <if test="installationId != null">installation_id,</if>
            <if test="installationCode != null">installation_code,</if>
            <if test="country != null">country,</if>
            <if test="unitNameCn != null">unit_name_cn,</if>
            <if test="unitNameEn != null">unit_name_en,</if>
            <if test="startDate != null">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="mission != null">mission,</if>
            <if test="troopsNum != null">troops_num,</if>
            <if test="source != null and source != ''">source,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            0,
            <if test="id != null">#{id},</if>
            <if test="installationId != null">#{installationId},</if>
            <if test="installationCode != null">#{installationCode},</if>
            <if test="country != null">#{country},</if>
            <if test="unitNameCn != null">#{unitNameCn},</if>
            <if test="unitNameEn != null">#{unitNameEn},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="mission != null">#{mission},</if>
            <if test="troopsNum != null">#{troopsNum},</if>
            <if test="source != null and source != ''">#{source},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <!--新增机构架构-->
    <insert id="insertOrgArchitecture" parameterType="com.lirong.project.member.domain.OrgArchitecture">
        insert into idw_org_architecture
        <trim prefix="(" suffix=")" suffixOverrides=",">
            is_delete,
            <if test="architectureId != null">architecture_id,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="ancestors != null and ancestors != ''">ancestors,</if>
            <if test="level != null">level,</if>
            <if test="orgCode != null and orgCode != ''">org_code,</if>
            <if test="nodeOrgCode != null">node_org_code,</if>
            <if test="nameCn != null and nameCn != ''">name_cn,</if>
            <if test="nameEn != null">name_en,</if>
            <if test="function != null">function,</if>
            <if test="type != null and type != ''">type,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            0,
            <if test="architectureId != null">#{architectureId},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="ancestors != null and ancestors != ''">#{ancestors},</if>
            <if test="level != null">#{level},</if>
            <if test="orgCode != null and orgCode != ''">#{orgCode},</if>
            <if test="nodeOrgCode != null">#{nodeOrgCode},</if>
            <if test="nameCn != null and nameCn != ''">#{nameCn},</if>
            <if test="nameEn != null">#{nameEn},</if>
            <if test="function != null">#{function},</if>
            <if test="type != null and type != ''">#{type},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

</mapper>