package com.lirong.system.architecture.service.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import com.lirong.common.core.domain.Ztree;
import com.lirong.common.utils.DateUtils;
import com.lirong.common.utils.ShiroUtils;
import com.lirong.common.utils.SnowIdUtils;
import com.lirong.common.utils.StringUtils;
import com.lirong.organization.common.domain.IdwOrg;
import com.lirong.organization.common.mapper.IdwOrgMapper;
import com.lirong.system.architecture.domain.IdwOrgSystem;
import com.lirong.system.architecture.mapper.IdwOrgSystemMapper;
import com.lirong.system.architecture.vo.OrganizationVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lirong.system.architecture.mapper.IdwOrgSystemArchitectureMapper;
import com.lirong.system.architecture.domain.IdwOrgSystemArchitecture;
import com.lirong.system.architecture.service.IdwOrgSystemArchitectureService;
import com.lirong.common.core.text.Convert;

/**
 * 体系结构Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-24
 */
@Service
public class IdwOrgSystemArchitectureServiceImpl implements IdwOrgSystemArchitectureService {
    @Autowired
    private IdwOrgSystemArchitectureMapper idwOrgSystemArchitectureMapper;
    @Autowired
    private IdwOrgSystemMapper idwOrgSystemMapper;
    @Autowired
    private IdwOrgMapper idwOrgMapper;

    /**
     * 查询体系结构
     *
     * @param architectureId 体系结构ID
     * @return 体系结构
     */
    @Override
    public IdwOrgSystemArchitecture selectOrgSystemArchitectureById(Long architectureId) {
        return idwOrgSystemArchitectureMapper.selectOrgSystemArchitectureById(architectureId);
    }

    /**
     * 新增体系结构
     *
     * @param idwOrgSystemArchitecture 体系结构
     * @return 结果
     */
    @Override
    public int insertOrgArchitecture(IdwOrgSystemArchitecture idwOrgSystemArchitecture) {
        if ("0".equals(idwOrgSystemArchitecture.getParentId().toString())) {
            idwOrgSystemArchitecture.setParentId((long) 0);
            idwOrgSystemArchitecture.setAncestors("0");
            idwOrgSystemArchitecture.setLevel(1);
        } else {
            IdwOrgSystemArchitecture parentTheme = idwOrgSystemArchitectureMapper.selectOrgSystemArchitectureById(idwOrgSystemArchitecture.getParentId());
            idwOrgSystemArchitecture.setAncestors(parentTheme.getAncestors() + "," + idwOrgSystemArchitecture.getParentId());
            idwOrgSystemArchitecture.setLevel(idwOrgSystemArchitecture.getAncestors().split(",").length);
        }
        idwOrgSystemArchitecture.setCreateBy(ShiroUtils.getUserName());
        idwOrgSystemArchitecture.setCreateTime(DateUtils.getNowDate());
        return idwOrgSystemArchitectureMapper.insertOrgArchitecture(idwOrgSystemArchitecture);
    }

    /**
     * 修改体系结构
     *
     * @param idwOrgSystemArchitecture 体系结构
     * @return 结果
     */
    @Override
    public int updateIdwOrgTheme(IdwOrgSystemArchitecture idwOrgSystemArchitecture) {
        String userName = ShiroUtils.getUserName();
        Date nowDate = DateUtils.getNowDate();
        idwOrgSystemArchitecture.setUpdateBy(userName);
        idwOrgSystemArchitecture.setUpdateTime(nowDate);
        //校验父级是否修改
        IdwOrgSystemArchitecture oldOrgSystemArchitecture = idwOrgSystemArchitectureMapper.selectOrgSystemArchitectureById(idwOrgSystemArchitecture.getId());
        if (StringUtils.isNotNull(oldOrgSystemArchitecture) && !idwOrgSystemArchitecture.getParentId().equals(oldOrgSystemArchitecture.getParentId())) {
            idwOrgSystemArchitecture.setLevel(idwOrgSystemArchitecture.getAncestors().split(",").length);
            //上级改变 同步修改子集
            IdwOrgSystemArchitecture parentTheme = idwOrgSystemArchitectureMapper.selectOrgSystemArchitectureById(idwOrgSystemArchitecture.getParentId());
            String oldAncestors = idwOrgSystemArchitecture.getAncestors();
            String newAncestors = parentTheme.getAncestors() + "," + idwOrgSystemArchitecture.getParentId();
            idwOrgSystemArchitecture.setAncestors(newAncestors);
            int addLevel = oldAncestors.split(",").length - newAncestors.split(",").length;
            idwOrgSystemArchitectureMapper.updateChildrenAncestors(idwOrgSystemArchitecture.getId(), oldAncestors, newAncestors, userName, addLevel);
        }
        if (idwOrgSystemArchitecture.getSynchronizationColor()) {
            //根据ID修改子集颜色
            idwOrgSystemArchitectureMapper.synchronizationChildrenColor(idwOrgSystemArchitecture.getId(), idwOrgSystemArchitecture.getColor(), userName);
        }
        return idwOrgSystemArchitectureMapper.updateIdwOrgTheme(idwOrgSystemArchitecture);
    }

    /**
     * 删除体系结构信息
     *
     * @param id 体系结构ID
     * @return 结果
     */
    @Override
    public int deleteOrgSystemArchitectureById(Long id) {
        String userName = ShiroUtils.getUserName();
        return idwOrgSystemArchitectureMapper.deleteOrgSystemArchitectureById(id, userName);
    }

    /**
     * 构建体系结构树
     *
     * @param systemName     体系名称
     * @param systemId       体系ID
     * @param architectureId 排除当前ID
     * @return 结果
     */
    @Override
    public List<Ztree> buildZtree(String systemName, Long systemId, Long architectureId) {
        List<Ztree> ztreeList = idwOrgSystemArchitectureMapper.buildZtree(systemId);
        Ztree top = new Ztree();
        top.setId("0");
        top.setpId("-1");
        top.setName(systemName);
        top.setTitle(systemName);
        ztreeList.add(top);
        return StringUtils.isNull(architectureId) ? ztreeList : ztreeList.stream().filter(z -> !z.getId().equals(architectureId) && !(("," + z.getAncestors() + ",").contains("," + architectureId + ","))).collect(Collectors.toList());
    }

    /**
     * 关联机构
     *
     * @param architectureId 体系结构ID
     * @param insertOrgCodes 新增机构编码
     * @param insertOrgNames 新增机构名称
     * @param deleteOrgCodes 删除机构编码
     * @return 结果
     */
    @Override
    public String relevancyOrganizationSave(Long architectureId, String[] insertOrgCodes, String[] insertOrgNames, String[] deleteOrgCodes) {
        String userName = ShiroUtils.getUserName();
        Date nowDate = DateUtils.getNowDate();
        Integer maxOrderNum = idwOrgSystemArchitectureMapper.selectMaxOrderNumByParentId(architectureId);
        if (StringUtils.isNotNull(insertOrgCodes) && insertOrgCodes.length > 0) {
            IdwOrgSystemArchitecture parentOrgTheme = idwOrgSystemArchitectureMapper.selectOrgSystemArchitectureById(architectureId);
            //新增关联机构
            List<IdwOrgSystemArchitecture> idwOrgSystemArchitectureList = new ArrayList<>();
            for (int i = 0; i < insertOrgCodes.length; i++) {
                maxOrderNum++;
                String orgCode = insertOrgCodes[i];
                if (StringUtils.isBlank(orgCode)) {
                    continue;
                }
                IdwOrgSystemArchitecture idwOrgSystemArchitecture = new IdwOrgSystemArchitecture();
                idwOrgSystemArchitecture.setType("organization");
                idwOrgSystemArchitecture.setOrgCode(orgCode);
                idwOrgSystemArchitecture.setName(insertOrgNames[i].replaceAll("@@@@", ","));
                idwOrgSystemArchitecture.setParentId(architectureId);
                idwOrgSystemArchitecture.setAncestors(parentOrgTheme.getAncestors() + "," + architectureId);
                idwOrgSystemArchitecture.setColor(parentOrgTheme.getColor());
                idwOrgSystemArchitecture.setIsShow(parentOrgTheme.getIsShow());
                idwOrgSystemArchitecture.setOrderNum(maxOrderNum);
                idwOrgSystemArchitecture.setCreateBy(userName);
                idwOrgSystemArchitecture.setCreateTime(nowDate);
                idwOrgSystemArchitectureList.add(idwOrgSystemArchitecture);
            }
            if (idwOrgSystemArchitectureList.size() > 0) {
                /* idwOrgSystemArchitectureMapper.batchInsert(idwOrgSystemArchitectureList);*/
            }
        }
        return "关联成功";
    }

    /**
     * 根据上级ID查询同意上级中最大的排序号
     *
     * @param parentId 上级ID
     * @return 结果
     */
    @Override
    public Integer selectMaxOrderNumByParentId(Long parentId) {
        return idwOrgSystemArchitectureMapper.selectMaxOrderNumByParentId(parentId);
    }

    /**
     * 查询国防单位列表
     *
     * @param organizationVo 国防单位
     * @return 结果
     */
    @Override
    public List<OrganizationVo> selectArchitectureOrganizationList(OrganizationVo organizationVo) {
        return idwOrgSystemArchitectureMapper.selectArchitectureOrganizationList(organizationVo);
    }

    /**
     * 根据体系ID机构编码与上级ID校验体系结构是否存在
     *
     * @param systemId 体系ID
     * @param parentId 上级ID
     * @param orgCode  机构编码
     * @return 结果
     */
    @Override
    public boolean architectureIsExistByOrgCode(Long systemId, Long parentId, String orgCode) {
        return idwOrgSystemArchitectureMapper.architectureIsExistByOrgCode(systemId, StringUtils.isNull(parentId) ? 0 : parentId, orgCode);
    }

    /**
     * 根据体系ID上级ID与机构编码新增体系结构
     *
     * @param systemId 体系ID
     * @param parentId 上级ID
     * @param orgCode  机构编码
     * @return 结果
     */
    @Override
    public int addOrgArchitecture(Long systemId, Long parentId, String orgCode) {
        boolean isExist = idwOrgSystemArchitectureMapper.architectureIsExistByOrgCode(systemId, parentId, orgCode);
        if (!isExist) {
            IdwOrgSystemArchitecture orgSystemArchitecture = new IdwOrgSystemArchitecture();
            Integer maxOrderNum = idwOrgSystemArchitectureMapper.selectMaxOrderNumByParentId(parentId);
            orgSystemArchitecture.setOrderNum(maxOrderNum + 1);
            if (StringUtils.isNull(parentId) || "0".equals(parentId.toString())) {
                orgSystemArchitecture.setParentId((long) 0);
                orgSystemArchitecture.setAncestors("0");
                orgSystemArchitecture.setLevel(1);
            } else {
                IdwOrgSystemArchitecture parentOrgSystemArchitecture = idwOrgSystemArchitectureMapper.selectOrgSystemArchitectureById(parentId);
                orgSystemArchitecture.setParentId(parentId);
                orgSystemArchitecture.setAncestors(parentOrgSystemArchitecture.getAncestors() + "," + orgSystemArchitecture.getParentId());
                orgSystemArchitecture.setLevel(orgSystemArchitecture.getAncestors().split(",").length);
            }
            orgSystemArchitecture.setOrgCode(orgCode);
            orgSystemArchitecture.setSystemId(systemId);
            orgSystemArchitecture.setType("organization");
            IdwOrg org = idwOrgMapper.selectOrgByOrgCode(orgCode);
            orgSystemArchitecture.setName(StringUtils.isNotBlank(org.getOrgNameCn()) ? org.getOrgNameCn() : org.getOrgNameEn());
            orgSystemArchitecture.setColor("#ffffff");
            orgSystemArchitecture.setIsShow("1");
            orgSystemArchitecture.setProfile(StringUtils.isNotBlank(org.getProfileCn()) ? org.getProfileCn() : org.getProfileEn());
            orgSystemArchitecture.setCreateBy(ShiroUtils.getUserName());
            orgSystemArchitecture.setCreateTime(DateUtils.getNowDate());
            return idwOrgSystemArchitectureMapper.insertOrgArchitecture(orgSystemArchitecture);
        }
        return 0;
    }

    /**
     * 根据体系ID上级ID与机构编码批量新增体系结构
     *
     * @param systemId 体系ID
     * @param parentId 上面级ID
     * @param orgCodes 机构编码
     * @return 结果
     */
    @Override
    public int addOrgArchitectures(Long systemId, Long parentId, String orgCodes) {
        for (String orgCode : orgCodes.split(",")) {
            if (StringUtils.isNotBlank(orgCode)) {
                boolean isExist = idwOrgSystemArchitectureMapper.architectureIsExistByOrgCode(systemId, parentId, orgCode);
                if (!isExist) {
                    addOrgArchitecture(systemId, parentId, orgCode);
                }
            }
        }
        return 0;
    }

    /**
     * 构建体系结构树
     *
     * @param systemId       体系ID
     * @param architectureId 结构ID
     * @return 结果
     */
    @Override
    public List<Ztree> architectureTree(Long systemId, Long architectureId) {
        List<Ztree> ztreeList = idwOrgSystemArchitectureMapper.selectTreeBySystemIdExcludeArchitectureId(systemId, architectureId);
        IdwOrgSystem system = idwOrgSystemMapper.selectIdwOrgSystemById(systemId);
        Ztree top = new Ztree();
        top.setId("0");
        top.setpId("-1");
        top.setName(system.getName());
        top.setTitle(system.getName());
        ztreeList.add(top);
        return ztreeList;
    }

    /**
     * 根据体系ID体系结构名称与上级ID校验体系结构是否存在
     *
     * @param systemId 体系ID
     * @param parentId 上级ID
     * @param name     体系结构名称
     * @return 结果
     */
    @Override
    public boolean architectureIsExistByName(Long systemId, Long parentId, String name) {
        return idwOrgSystemArchitectureMapper.architectureIsExistByName(systemId, parentId, name);
    }

    /**
     * 根据机构编码构建体系结构树-只返回机构极其架构
     *
     * @param systemId              体系ID
     * @param orgCode               机构编码
     * @param excludeArchitectureId 排除体系结构ID
     * @return 结果
     */
    @Override
    public List<Ztree> architectureTreeByOrgCode(Long systemId, String orgCode, Long excludeArchitectureId) {
        return idwOrgSystemArchitectureMapper.architectureTreeByOrgCode(orgCode, systemId, excludeArchitectureId);
    }

    String userName;
    Date nowDate;
    Long newSystemId;
    List<IdwOrgSystemArchitecture> orgSystemArchitectureList;

    /**
     * 新增体系结构-将childId架构复制到architectureId下
     *
     * @param systemId       体系ID
     * @param architectureId 体系结构ID
     * @param childId        结构ID
     * @return 结果
     */
    @Override
    public int addArchitecture(Long systemId, Long architectureId, Long childId) {
        newSystemId = systemId;
        userName = ShiroUtils.getUserName();
        nowDate = DateUtils.getNowDate();
        String parentAncestors = "0";
        if (!"0".equals(architectureId.toString())) {
            IdwOrgSystemArchitecture parent = idwOrgSystemArchitectureMapper.selectOrgSystemArchitectureById(architectureId);
            parentAncestors = parent.getAncestors();
        }
        Integer maxOrderNum = idwOrgSystemArchitectureMapper.selectMaxOrderNumByParentId(architectureId);
        IdwOrgSystemArchitecture orgSystemArchitecture = idwOrgSystemArchitectureMapper.selectOrgSystemArchitectureById(childId);
        String oldAncestors = orgSystemArchitecture.getAncestors() + "," + orgSystemArchitecture.getId();
        Long oldParentId = orgSystemArchitecture.getId();
        long id = SnowIdUtils.uniqueLong();// 获取雪花id
        orgSystemArchitecture.setId(id);
        orgSystemArchitecture.setSystemId(newSystemId);
        orgSystemArchitecture.setCreateBy(userName);
        orgSystemArchitecture.setCreateTime(nowDate);
        orgSystemArchitecture.setOrderNum(maxOrderNum + 1);
        //赋值上级相关字段
        orgSystemArchitecture.setParentId(architectureId);
        orgSystemArchitecture.setAncestors(parentAncestors);
        String ancestors = orgSystemArchitecture.getAncestors() + "," + orgSystemArchitecture.getId();
        orgSystemArchitecture.setLevel(orgSystemArchitecture.getAncestors().split(",").length);
        idwOrgSystemArchitectureMapper.insertOrgArchitecture(orgSystemArchitecture);
        orgSystemArchitectureList = idwOrgSystemArchitectureMapper.selectOrgSystemArchitectureChildrenById(childId);
        copyArchitecture(oldParentId, id, oldAncestors, ancestors);
        return 0;
    }

    /**
     * 拷贝架构
     *
     * @param oldParnetId  原上级ID
     * @param newParentId  新上级ID
     * @param oldAncestors 原祖籍列表
     * @param newAncestors 新祖籍列表
     */
    public void copyArchitecture(Long oldParnetId, Long newParentId, String oldAncestors, String newAncestors) {
        //获取下一级
        List<IdwOrgSystemArchitecture> list = orgSystemArchitectureList.stream().filter(idwOrgSystemArchitecture -> idwOrgSystemArchitecture.getParentId().equals(oldParnetId)).collect(Collectors.toList());
        for (IdwOrgSystemArchitecture orgSystemArchitecture : list) {
            Long oldId = orgSystemArchitecture.getId();
            String oldParentAncestors = orgSystemArchitecture.getAncestors() + "," + orgSystemArchitecture.getId();
            long id = SnowIdUtils.uniqueLong();// 获取雪花id
            orgSystemArchitecture.setSystemId(newSystemId);
            orgSystemArchitecture.setId(id);
            orgSystemArchitecture.setCreateBy(userName);
            orgSystemArchitecture.setCreateTime(nowDate);
            //赋值上级相关字段
            orgSystemArchitecture.setParentId(newParentId);
            orgSystemArchitecture.setAncestors(orgSystemArchitecture.getAncestors().replace(oldAncestors, newAncestors));
            orgSystemArchitecture.setLevel(orgSystemArchitecture.getAncestors().split(",").length);
            idwOrgSystemArchitectureMapper.insertOrgArchitecture(orgSystemArchitecture);
            String newParentAncestors = orgSystemArchitecture.getAncestors() + "," + orgSystemArchitecture.getId();
            List<IdwOrgSystemArchitecture> children = orgSystemArchitectureList.stream().filter(idwOrgSystemArchitecture -> idwOrgSystemArchitecture.getParentId().equals(oldId)).collect(Collectors.toList());
            //还存在子集
            if (children.size() > 0) {
                copyArchitecture(oldId, id, oldParentAncestors, newParentAncestors);
            }
        }
    }
}
