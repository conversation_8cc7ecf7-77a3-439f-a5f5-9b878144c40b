<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.lirong</groupId>
    <artifactId>webdp</artifactId>
    <version>1.0.0</version>

    <name>webdp</name>
    <url>http://www.touchwisdom.com</url>
    <description>后台管理</description>
    
    <properties>
        <webdp.version>1.0.0</webdp.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven-jar-plugin.version>3.1.1</maven-jar-plugin.version>
        <shiro.version>1.8.0</shiro.version>
        <thymeleaf.extras.shiro.version>2.1.0</thymeleaf.extras.shiro.version>
        <druid.version>1.2.8</druid.version>
        <bitwalker.version>1.21</bitwalker.version>
        <kaptcha.version>2.3.2</kaptcha.version>
        <swagger.version>3.0.0</swagger.version>
        <mybatis-spring-boot.version>2.2.0</mybatis-spring-boot.version>
        <pagehelper.boot.version>1.4.0</pagehelper.boot.version>
        <fastjson.version>1.2.78</fastjson.version>
        <oshi.version>5.8.2</oshi.version>
        <jna.version>5.9.0</jna.version>
        <commons.io.version>2.11.0</commons.io.version>
        <commons.fileupload.version>1.4</commons.fileupload.version>
        <poi.version>4.1.2</poi.version>
        <velocity.version>1.7</velocity.version>
        <easypoi.version>3.3.0</easypoi.version>
        <jwt.version>3.4.0</jwt.version>
        <hutool.version>5.4.5</hutool.version>
        <minio.version>8.2.0</minio.version>
    </properties>

    <!-- 依赖声明 -->
    <dependencyManagement>
        <dependencies>

            <!-- SpringBoot的依赖配置-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>2.5.6</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <!-- 图形数据库Neo4j 官方支持的neo4j依赖包 -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-data-neo4j</artifactId>
                <version>2.5.3</version>
            </dependency>

            <dependency>
                <groupId>org.neo4j</groupId>
                <artifactId>neo4j-ogm-core</artifactId>
                <version>3.2.24</version>
            </dependency>
            
            <!--阿里数据库连接池 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid.version}</version>
            </dependency>
            
            <!--验证码 -->
            <dependency>
                <groupId>com.github.penggle</groupId>
                <artifactId>kaptcha</artifactId>
                <version>${kaptcha.version}</version>
            </dependency>

            <!--Shiro核心框架 -->
            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-core</artifactId>
                <version>${shiro.version}</version>
            </dependency>

            <!-- Shiro使用Spring框架 -->
            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-spring</artifactId>
                <version>${shiro.version}</version>
            </dependency>

            <!-- Shiro使用EhCache缓存框架 -->
            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-ehcache</artifactId>
                <version>${shiro.version}</version>
            </dependency>
    
            <!-- thymeleaf模板引擎和shiro框架的整合 -->
            <dependency>
                <groupId>com.github.theborakompanioni</groupId>
                <artifactId>thymeleaf-extras-shiro</artifactId>
                <version>${thymeleaf.extras.shiro.version}</version>
            </dependency>

            <!-- 解析客户端操作系统、浏览器等 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${bitwalker.version}</version>
            </dependency>

            <!-- SpringBoot集成mybatis框架 -->
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis-spring-boot.version}</version>
            </dependency>

            <!-- pagehelper 分页插件 -->
            <dependency>
                <groupId>com.github.pagehelper</groupId>
                <artifactId>pagehelper-spring-boot-starter</artifactId>
                <version>${pagehelper.boot.version}</version>
            </dependency>

            <!-- 获取系统信息 -->
            <dependency>
                <groupId>com.github.oshi</groupId>
                <artifactId>oshi-core</artifactId>
                <version>${oshi.version}</version>
            </dependency>

            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna</artifactId>
                <version>${jna.version}</version>
            </dependency>

            <dependency>
                <groupId>net.java.dev.jna</groupId>
                <artifactId>jna-platform</artifactId>
                <version>${jna.version}</version>
            </dependency>

            <!-- hutool工具类 -->
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool.version}</version>
            </dependency>

            <!-- swagger2-->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>${swagger.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-annotations</artifactId>
                    </exclusion>
                    <exclusion>
                        <groupId>io.swagger</groupId>
                        <artifactId>swagger-models</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- swagger2-UI-->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>${swagger.version}</version>
            </dependency>
            
            <!--io常用工具类 -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <!--文件上传工具类 -->
            <dependency>
                <groupId>commons-fileupload</groupId>
                <artifactId>commons-fileupload</artifactId>
                <version>${commons.fileupload.version}</version>
            </dependency>

            <!-- excel工具 -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi.version}</version>
            </dependency>

            <!--velocity代码生成使用模板 -->
            <dependency>
                <groupId>org.apache.velocity</groupId>
                <artifactId>velocity</artifactId>
                <version>${velocity.version}</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-collections</groupId>
                        <artifactId>commons-collections</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- minio -->
<!--            <dependency>-->
<!--                <groupId>io.minio</groupId>-->
<!--                <artifactId>minio</artifactId>-->
<!--                <version>${minio.version}</version>-->
<!--            </dependency>-->

            <!-- jwt jar-->
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>${jwt.version}</version>
            </dependency>

            <!-- 阿里JSON解析器 -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>
            
            <!-- 定时任务-->
            <dependency>
                <groupId>com.lirong</groupId>
                <artifactId>webdp-quartz</artifactId>
                <version>${webdp.version}</version>
            </dependency>

            <!-- 代码生成-->
            <dependency>
                <groupId>com.lirong</groupId>
                <artifactId>webdp-generator</artifactId>
                <version>${webdp.version}</version>
            </dependency>

            <!-- 核心模块-->
            <dependency>
                <groupId>com.lirong</groupId>
                <artifactId>webdp-framework</artifactId>
                <version>${webdp.version}</version>
            </dependency>

            <!-- 系统模块-->
            <dependency>
                <groupId>com.lirong</groupId>
                <artifactId>webdp-system</artifactId>
                <version>${webdp.version}</version>
            </dependency>

            <!-- 通用工具-->
            <dependency>
                <groupId>com.lirong</groupId>
                <artifactId>webdp-common</artifactId>
                <version>${webdp.version}</version>
            </dependency>

            <!-- 人员模块-->
            <dependency>
                <groupId>com.lirong</groupId>
                <artifactId>webdp-personnel</artifactId>
                <version>${webdp.version}</version>
            </dependency>



        </dependencies>
    </dependencyManagement>

    <modules>
        <module>webdp-admin</module>
        <module>webdp-framework</module>
        <module>webdp-system</module>
        <module>webdp-quartz</module>
        <module>webdp-generator</module>
        <module>webdp-common</module>
        <module>webdp-weaponry</module>
        <module>webdp-organization</module>
        <module>webdp-personnel</module>
        <module>webdp-resource</module>
        <module>webdp-macro-data</module>
        <module>webdp-model</module>
        <module>webdp-multimedia</module>
<!--        <module>webdp-oss</module>-->
        <module>webdp-project</module>
        <module>webdp-contract-dod</module>
        <module>webdp-kg</module>
        <module>webdp-nlp</module>
        <module>webdp-contract</module>
        <module>webdp-event</module>
        <module>webdp-technosphere</module>
        <module>webdp-patent</module>
        <module>webdp-award</module>
        <module>update-database</module>
        <module>webdp-installation</module>
        <module>webdp-system-architecture</module>
        <module>webdp-weaponry/weaponry-formation</module>
        <!--<module>webdp-redis</module>-->
    </modules>
    <packaging>pom</packaging>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>public</id>
            <name>aliyun nexus</name>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
            <releases>
                <enabled>true</enabled>
            </releases>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>