<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>webdp</artifactId>
        <groupId>com.lirong</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>webdp-contract-dod</artifactId>


    <dependencies>
        <!-- 通用工具-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>webdp-common</artifactId>
        </dependency>

        <!--组织机构-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>organization-common</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>organization-staff</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>personnel-common</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--武器装备-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>weaponry-basic</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.11.2</version>
        </dependency>

        <!-- stanfordNLP -->
        <dependency>
            <groupId>edu.stanford.nlp</groupId>
            <artifactId>stanford-corenlp</artifactId>
            <version>4.4.0</version>
        </dependency>

        <dependency>
            <groupId>edu.stanford.nlp</groupId>
            <artifactId>stanford-corenlp</artifactId>
            <version>4.4.0</version>
            <classifier>models</classifier>
        </dependency>

        <dependency>
            <groupId>org.glassfish.jaxb</groupId>
            <artifactId>jaxb-runtime</artifactId>
            <version>2.3.6</version>
        </dependency>
        <!-- 排除旧版冲突 -->
        <dependency>
            <groupId>de.jollyday</groupId>
            <artifactId>jollyday</artifactId>
            <version>0.5.10</version> <!-- 确保使用较新版本 -->
            <exclusions>
                <exclusion>
                    <groupId>com.sun.xml.bind</groupId>
                    <artifactId>jaxb-impl</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--舰船-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>weaponry-ship</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--        <dependency>-->
<!--            <groupId>edu.stanford.nlp</groupId>-->
<!--            <artifactId>stanford-segmenter</artifactId>-->
<!--            <version>4.2.0</version>-->
<!--        </dependency>-->
    </dependencies>
</project>