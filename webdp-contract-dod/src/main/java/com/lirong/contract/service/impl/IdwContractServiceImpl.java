package com.lirong.contract.service.impl;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import com.lirong.common.utils.DateUtils;
import com.lirong.common.utils.ShiroUtils;
import com.lirong.common.utils.StringUtils;
import com.lirong.contract.domain.OriginalContract;
import com.lirong.organization.common.mapper.IdwOrgMapper;
import com.lirong.weaponry.basic.mapper.IdwWeaponryBasicMapper;
import com.lirong.weaponry.ship.domain.Ship;
import com.lirong.weaponry.ship.mapper.ShipMapper;
import edu.stanford.nlp.pipeline.CoreDocument;
import edu.stanford.nlp.pipeline.CoreEntityMention;
import edu.stanford.nlp.pipeline.CoreSentence;
import edu.stanford.nlp.pipeline.StanfordCoreNLP;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lirong.contract.mapper.IdwContractMapper;
import com.lirong.contract.domain.IdwContract;
import com.lirong.contract.service.IdwContractService;
import com.lirong.common.core.text.Convert;

import javax.annotation.PostConstruct;

/**
 * 合同信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-09-14
 */
@Service
public class IdwContractServiceImpl implements IdwContractService {
    @Autowired
    private IdwContractMapper idwContractMapper;
    @Autowired//组织机构
    private IdwOrgMapper idwOrgMapper;
    @Autowired//武器装备
    private IdwWeaponryBasicMapper idwWeaponryMapper;
    @Autowired//舰船
    private ShipMapper shipMapper;

    private static StanfordCoreNLP pipeline;

    @Override
    public IdwContract parseContract(OriginalContract originalContract) {
        IdwContract contract = null;
        if (null != originalContract) {
            // 获取合同原文
            String content = originalContract.getRawData();
            // 解析合同
            contract = parseContract(content);
            // 设置国家
            contract.setCountry(originalContract.getCountry());
        }
        return contract;
    }

    @Override
    public IdwContract parseContract(String content) {
        if (pipeline == null) {
            // set up pipeline properties
            Properties props = new Properties();
            // set the list of annotators to run
            props.setProperty("annotators", "tokenize,ssplit,pos,lemma,ner");
            // set a property for an annotator, in this case the coref annotator is being set to use the neural algorithm
            props.setProperty("coref.algorithm", "neural");
            // build pipeline
            pipeline = new StanfordCoreNLP(props);
        }
        IdwContract contract = new IdwContract();
        CoreDocument document = new CoreDocument(content);
//        // annotate the document
        pipeline.annotate(document);

        int mentionIdx = 0;
        for (CoreEntityMention em : document.entityMentions()) {
            mentionIdx++;

            // 获取合同乙方
            if (em.entityType().equals("ORGANIZATION") && mentionIdx == 1) {
                String partB = em.text();
                contract.setContractorName(em.sentence().text().substring(0, em.sentence().text().indexOf(partB) + partB.length()));
            }

            // 获取合同金额
            if (em.entityType().equals("MONEY") && null == contract.getAmount()) {
                // 获取内容上下文。
                String context = em.sentence().text();
                if (context.contains("awarded")) {
                    contract.setCurrencyUnit(em.text().substring(0, 1));
                    contract.setAmount(new BigDecimal(em.text().split(" ")[0].substring(1).replaceAll(",", "")));
                }
            }

            // 提取合同截至日期
            if (em.entityType().equals("DATE") && StringUtils.isEmpty(contract.getYear())) {
                // 获取句子
                String sentence = em.sentence().text();
                String expirationDate = em.text();
                // 判断日期是否出现completed、completion关键字
                if (sentence.contains("completed") || sentence.contains("completion") || sentence.contains(" expected to ") || sentence.contains(" end date")) {
                    try {
                        Map<String, Integer> dateMap = parseDate(expirationDate);
                        contract.setYear(dateMap.get("year") == null ? null : dateMap.get("year").toString());
                        contract.setMonth(lpad(dateMap.get("month") == null ? null : dateMap.get("month").toString(), 2));
                        contract.setDay(lpad(dateMap.get("day") == null ? null : dateMap.get("day").toString(), 2));
                    } catch (ParseException e) {
                        e.printStackTrace();
                    }
                }
            }

            // 合同签订日期
            if (em.entityType().equals("DATE") && em.sentence().text().contains("Awarded")) {
                try {
                    Map<String, Integer> dateMap = parseDate(em.text());
                    contract.setSigningDate(dateMap.get("year") + "-" + dateMap.get("month") + "-" + dateMap.get("day"));
                } catch (ParseException e) {
                    e.printStackTrace();
                }

            }
        }

        // 从第一句中提取合同编号
        CoreSentence firstSentence = document.sentences().get(0);
        contract.setContractNo(extractContractNO(firstSentence));

        // 获取最后一句话
        CoreSentence sentence = document.sentences().get(document.sentences().size() - 1);

        // 从最后一句中提取合同编码
        // 如果合同编码为空并且以)、).结尾，则提取合同编码
        if ((null == contract.getContractNo()) && (sentence.text().endsWith(").") || sentence.text().endsWith(")"))) {
            int lastLeftIndex = sentence.text().lastIndexOf("(");
            int lastRightIndex = sentence.text().lastIndexOf(")");
            String contractNO = sentence.text().substring(lastLeftIndex + 1, lastRightIndex);
            contract.setContractNo(contractNO);
        }

        // 如果最后一句话以）结尾，则从倒数第二句话中提取甲方
        if (document.text().trim().endsWith(")")) {
            sentence = document.sentences().get(document.sentences().size() - 2);
        }

        // 提取数据
        String partA = sentence.text().split(",")[0].replaceAll("contracting activity is the ", "");
        contract.setOwnerName(partA);

        // 设置签订日期
        // contract.setSigningDate(originalContract.getPublishDate());
        return contract;
    }

    /**
     * 获取修改合同编号
     *
     * @param sentence
     * @return
     */
    private String getModificationNO(CoreSentence sentence) {
        // 判断是否存在修改编号
        String modificationNO = "";
        if (sentence.text().indexOf("modification (") > 0) {    // 修改合同
            int idx = sentence.text().indexOf("modification (");
            String other = sentence.text().substring(idx + "modification (".length()).trim();
            // 获取变更编号
            modificationNO = other.substring(0, other.indexOf(")")).trim();
            // 返回数据
            return StringUtils.isNotEmpty(modificationNO) ? "/" + modificationNO : "";
        }
        return "";
    }

    /**
     * 从句子中抽取合同编号
     *
     * @param sentence
     * @return
     */
    private String extractContractNO(CoreSentence sentence) {
        String keyword = "awarded contract";
        int idx = sentence.text().indexOf(keyword);
        if (idx != -1) {
            String rest = sentence.text().substring(idx + keyword.length()).trim();
            String no = rest.split(" ")[0].replace(".", "").replace("(", "").replace(")", "").trim();

            if (no.split(" ").length == 1) {
                return no + getModificationNO(sentence);
            }
        } else if (sentence.text().indexOf("contract (") > 0) {
            idx = sentence.text().indexOf("contract (");
            String rest = sentence.text().substring(idx + "contract (".length()).trim();
            String no = rest.substring(0, rest.indexOf(")")).trim();

            return no + getModificationNO(sentence);
        } else if (sentence.text().indexOf("delivery order") > 0) {
            idx = sentence.text().indexOf("delivery order");
            String rest = sentence.text().substring(idx + "delivery order".length()).trim();
            // 获取合同编号
            String no = rest.split(" ")[0];
            return no + getModificationNO(sentence);
        } else if (sentence.text().indexOf("task order") > 0) {
            idx = sentence.text().indexOf("task order");
            String rest = sentence.text().substring(idx + "task order".length()).trim();
            // 获取合同编号
            String no = rest.split(" ")[0];
            return no + getModificationNO(sentence);
        } else if (sentence.text().indexOf("modification (") > 0) {    // 修改合同
            // 获取原始合同编号
            int keywordLength = 0;
            int toContractInx = sentence.text().indexOf("to contract");
            keywordLength = "to contract".length();
            if (toContractInx == -1) {
                toContractInx = sentence.text().indexOf(" contract ");
                keywordLength = " contract ".length();
            }
            String originalNOContent = sentence.text().substring(toContractInx + keywordLength).trim();
            String originalNO = originalNOContent.split(" |,")[0];
            return originalNO + "/" + getModificationNO(sentence);
        } else {
            // 从STATE_OR_PROVINCE后获取合同编号
            for (CoreEntityMention em : sentence.entityMentions()) {
                if (em.entityType().equals("STATE_OR_PROVINCE")) {
                    idx = sentence.text().indexOf(em.text());
                    String rest = sentence.text().substring(idx + em.text().length()).trim();
                    if (rest.startsWith("(")) {
                        String no = rest.substring(1, rest.indexOf(")"));
                        return no;
                    }
                }
            }
        }
        return null;
    }

    private String lpad(Object word, int length) {
        if (null == word) {
            return "";
        }
        while (word.toString().length() < length) {
            word = "0" + word;
        }
        return word.toString();
    }

    /**
     * 解析日期，将英文日期格式处理为数字
     *
     * @param date
     * @return
     * @throws ParseException
     */
    private Map<String, Integer> parseDate(String date) throws ParseException {
        Map<String, Integer> result = new HashMap<>();
        SimpleDateFormat sdf = null;

        if (date.indexOf(",") > 0 && date.indexOf(".") > 0) {
            sdf = new SimpleDateFormat("MMM. dd, yyyy", Locale.UK); // 月.日,年
            sdf.parse(date.replace("Sept.", "Sep."));
            result.put("year", sdf.getCalendar().get(Calendar.YEAR));
            result.put("month", sdf.getCalendar().get(Calendar.MONTH) + 1);
            result.put("day", sdf.getCalendar().get(Calendar.DAY_OF_MONTH));
        } else if (date.indexOf(",") > 0) {
            sdf = new SimpleDateFormat("MMM dd, yyyy", Locale.UK); // 月日,年
            sdf.parse(date);
            result.put("year", sdf.getCalendar().get(Calendar.YEAR));
            result.put("month", sdf.getCalendar().get(Calendar.MONTH) + 1);
            result.put("day", sdf.getCalendar().get(Calendar.DAY_OF_MONTH));
        } else if (date.length() > 4) {
            sdf = new SimpleDateFormat("MMM yyyy", Locale.UK); // 月年
            sdf.parse(date);
            result.put("year", sdf.getCalendar().get(Calendar.YEAR));
            result.put("month", sdf.getCalendar().get(Calendar.MONTH) + 1);
        } else if (date.length() == 4) {
            sdf = new SimpleDateFormat("yyyy", Locale.UK); // 年
            sdf.parse(date);
            result.put("year", sdf.getCalendar().get(Calendar.YEAR));
        }

        return result;
    }

    /**
     * 查询合同信息
     *
     * @param contractId 合同信息ID
     * @return 合同信息
     */
    @Override
    public IdwContract selectIdwContractById(Long contractId) {
        IdwContract contract = idwContractMapper.selectIdwContractById(contractId);
        if (StringUtils.isNotBlank(contract.getExpirationDate())) {
            String[] issueDate = contract.getExpirationDate().split("-");
            for (int i = 0; i < issueDate.length; i++) {
                String date = issueDate[i];
                if (i == 0) {
                    contract.setYear(date);
                }
                if (i == 1) {
                    contract.setMonth(date);
                }
                if (i == 2) {
                    contract.setDay(date);
                }
            }
        }
        return contract;
    }

    /**
     * 查询合同信息列表
     *
     * @param idwContract 合同信息
     * @return 合同信息
     */
    @Override
    public List<IdwContract> selectIdwContractList(IdwContract idwContract) {
        return idwContractMapper.selectIdwContractList(idwContract);
    }

    /**
     * 新增合同信息
     *
     * @param idwContract 合同信息
     * @return 结果
     */
    @Override
    public int insertIdwContract(IdwContract idwContract) {
        if (StringUtils.isNotEmpty(idwContract.getYear())) {
            idwContract.setExpirationDate(idwContract.getYear());
            if (StringUtils.isNotEmpty(idwContract.getMonth())) {
                idwContract.setExpirationDate(idwContract.getYear() + "-" + idwContract.getMonth());
                if (StringUtils.isNotEmpty(idwContract.getDay())) {
                    idwContract.setExpirationDate(idwContract.getYear() + "-" + idwContract.getMonth() + "-" + idwContract.getDay());
                }
            }
        }
        //合同甲方关联机构
        List<String> ownerOrgCode = idwOrgMapper.selectOrgCodetByTags(idwContract.getOwnerName());
        if (StringUtils.isNotNull(ownerOrgCode) && ownerOrgCode.size() == 1 && StringUtils.isNotBlank(ownerOrgCode.get(0))) {
            idwContract.setOwnerCode(ownerOrgCode.get(0));
        }
        //承包商关联机构
        List<String> contractorOrgCode = idwOrgMapper.selectOrgCodetByTags(idwContract.getContractorName());
        if (StringUtils.isNotNull(contractorOrgCode) && contractorOrgCode.size() == 1 && StringUtils.isNotBlank(contractorOrgCode.get(0))) {
            idwContract.setContractorCode(contractorOrgCode.get(0));
        }
        //装备名称关联武器装备
        List<String> weaponryCode = idwWeaponryMapper.selectWeaponryCodeByTags(idwContract.getWeaponryName());
        if (StringUtils.isNotNull(weaponryCode) && weaponryCode.size() == 1 && StringUtils.isNotBlank(weaponryCode.get(0))) {
            idwContract.setWeaponryCode(weaponryCode.get(0));
        }
        idwContract.setCreateBy(ShiroUtils.getUserName());
        idwContract.setCreateTime(DateUtils.getNowDate());
        return idwContractMapper.insertIdwContract(idwContract);
    }

    /**
     * 修改合同信息
     *
     * @param idwContract 合同信息
     * @return 结果
     */
    @Override
    public int updateIdwContract(IdwContract idwContract) {
        if (StringUtils.isNotEmpty(idwContract.getYear())) {
            idwContract.setExpirationDate(idwContract.getYear());
            if (StringUtils.isNotEmpty(idwContract.getMonth())) {
                idwContract.setExpirationDate(idwContract.getYear() + "-" + idwContract.getMonth());
                if (StringUtils.isNotEmpty(idwContract.getDay())) {
                    idwContract.setExpirationDate(idwContract.getYear() + "-" + idwContract.getMonth() + "-" + idwContract.getDay());
                }
            }
        }
        //合同甲方关联机构
        List<String> ownerOrgCode = idwOrgMapper.selectOrgCodetByTags(idwContract.getOwnerName());
        if (StringUtils.isNotNull(ownerOrgCode) && ownerOrgCode.size() == 1 && StringUtils.isNotBlank(ownerOrgCode.get(0))) {
            idwContract.setOwnerCode(ownerOrgCode.get(0));
        }
        //承包商关联机构
        List<String> contractorOrgCode = idwOrgMapper.selectOrgCodetByTags(idwContract.getContractorName());
        if (StringUtils.isNotNull(contractorOrgCode) && contractorOrgCode.size() == 1 && StringUtils.isNotBlank(contractorOrgCode.get(0))) {
            idwContract.setContractorCode(contractorOrgCode.get(0));
        }
        //装备名称关联武器装备
        List<String> weaponryCode = idwWeaponryMapper.selectWeaponryCodeByTags(idwContract.getWeaponryName());
        if (StringUtils.isNotNull(weaponryCode) && weaponryCode.size() == 1 && StringUtils.isNotBlank(weaponryCode.get(0))) {
            idwContract.setWeaponryCode(weaponryCode.get(0));
        }
        idwContract.setUpdateBy(ShiroUtils.getUserName());
        idwContract.setUpdateTime(DateUtils.getNowDate());
        return idwContractMapper.updateIdwContract(idwContract);
    }

    /**
     * 删除合同信息对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteIdwContractByIds(String ids) {
        String userName = ShiroUtils.getUserName();
        return idwContractMapper.deleteIdwContractByIds(Convert.toStrArray(ids), userName);
    }

    /**
     * 根据名称关联甲方/承包商/装备
     *
     * @return 关联的条目数
     */
    @Override
    public String automaticRelevancy() {
        //查询所有甲方/承包商/装备未关联数据
        List<IdwContract> contractList = idwContractMapper.selectAllNotRelevancy();
        int ownerCount = 0;
        int contractorCount = 0;
        int weaponryCount = 0;
        if (StringUtils.isNotNull(contractList) && contractList.size() > 0) {
            for (IdwContract contract : contractList) {
                if (StringUtils.isNotBlank(contract.getOwnerName())) {
                    //甲方未关联
                    List<String> ownerList = idwOrgMapper.selectOrgCodetByTags(contract.getOwnerName());
                    if (StringUtils.isNotNull(ownerList) && ownerList.size() == 1 && StringUtils.isNotBlank(ownerList.get(0))) {
                        ownerCount++;
                        idwContractMapper.updateCodeByContractIdAndCodeType(contract.getContractId(), ownerList.get(0), "owner");
                    }
                }
                if (StringUtils.isNotBlank(contract.getContractorName())) {
                    //承包商未关联
                    List<String> contractorList = idwOrgMapper.selectOrgCodetByTags(contract.getContractorName());
                    if (StringUtils.isNotNull(contractorList) && contractorList.size() == 1 && StringUtils.isNotBlank(contractorList.get(0))) {
                        contractorCount++;
                        idwContractMapper.updateCodeByContractIdAndCodeType(contract.getContractId(), contractorList.get(0), "contractor");
                    }
                }
                if (StringUtils.isNotBlank(contract.getWeaponryName())) {
                    //装备未关联
                    List<String> weaponryCodeList = idwWeaponryMapper.selectWeaponryCodeByTags(contract.getWeaponryName());
                    if (StringUtils.isNotNull(weaponryCodeList) && weaponryCodeList.size() == 1 && StringUtils.isNotBlank(weaponryCodeList.get(0))) {
                        weaponryCount++;
                        idwContractMapper.updateCodeByContractIdAndCodeType(contract.getContractId(), weaponryCodeList.get(0), "weaponry");
                    }
                }
            }
        }
        return "关联完成,共关联" + (ownerCount + contractorCount + weaponryCount) + "条记录 " +
                "<br/>" + "甲方：" + ownerCount + "条" +
                "<br/>" + "承包商：" + contractorCount + "条" +
                "<br/>" + "装备：" + weaponryCount + "条";
    }

    /**
     * 根据合同ID数组查询
     *
     * @param contractIds 合同ID
     * @return 结果
     */
    @Override
    public List<IdwContract> selectByContractIds(Long[] contractIds) {
        return idwContractMapper.selectByContractIds(contractIds);
    }

    /**
     * 导入合同信息
     *
     * @param list          合同信息列表
     * @param updateSupport 是否更新
     * @return 结果
     */
    @Override
    public String importContract(List<IdwContract> list, boolean updateSupport) {
        if (StringUtils.isNull(list) || list.size() < 1) {
            return "合同数据为空！";
        }
        String userName = ShiroUtils.getUserName();
        Date nowDate = DateUtils.getNowDate();
        long insertCount = 0;
        long updateCount = 0;
        for (IdwContract contract : list) {
            IdwContract oldContract = idwContractMapper.selectByContractAwardUniqueKey(contract.getContractAwardUniqueKey());
            if (StringUtils.isBlank(contract.getCountry())) {
                contract.setCountry("US");
            }
            contract.setCurrencyUnit("$");
            if (StringUtils.isNull(oldContract)) {
                insertCount++;
                contract.setRawId((long) 0);
                contract.setCreateBy(userName);
                contract.setCreateTime(nowDate);
                idwContractMapper.insertIdwContract(contract);
            } else if (updateSupport) {
                updateCount++;
                contract.setContractId(oldContract.getContractId());
                contract.setUpdateBy(userName);
                contract.setUpdateTime(nowDate);
                idwContractMapper.updateIdwContract(contract);
            }
        }
        List<String> weaponryCodes = list.stream().map(IdwContract::getWeaponryCode).collect(Collectors.toList()).stream().distinct().collect(Collectors.toList());
        return "合同信息共：" + list.size() + "条" + weaponryCodes.size() + "个装备,新增：" + insertCount + "条" + ",修改：" + updateCount + "条";
    }
}
