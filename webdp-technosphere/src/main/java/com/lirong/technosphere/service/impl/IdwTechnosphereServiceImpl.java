package com.lirong.technosphere.service.impl;

import java.util.List;

import com.lirong.common.core.domain.StructureChart;
import com.lirong.common.core.domain.Ztree;
import com.lirong.common.utils.DateUtils;
import com.lirong.common.utils.ShiroUtils;
import com.lirong.common.utils.StringUtils;
import com.lirong.organization.common.domain.IdwOrg;
import com.lirong.organization.common.mapper.IdwOrgMapper;
import com.lirong.weaponry.basic.domain.IdwWeaponryBasic;
import com.lirong.weaponry.basic.mapper.IdwWeaponryBasicMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lirong.technosphere.mapper.IdwTechnosphereMapper;
import com.lirong.technosphere.domain.IdwTechnosphere;
import com.lirong.technosphere.service.IdwTechnosphereService;

/**
 * 技术领域Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-12-15
 */
@Service
public class IdwTechnosphereServiceImpl implements IdwTechnosphereService {
    @Autowired
    private IdwTechnosphereMapper idwTechnosphereMapper;
    @Autowired
    private IdwOrgMapper idwOrgMapper;
    @Autowired
    private IdwWeaponryBasicMapper idwWeaponryBasicMapper;

    /**
     * 查询技术领域
     *
     * @param id 技术领域ID
     * @return 技术领域
     */
    @Override
    public IdwTechnosphere selectIdwTechnosphereById(Long id) {
        IdwTechnosphere technosphere = idwTechnosphereMapper.selectIdwTechnosphereById(id);
        if (StringUtils.isNotBlank(technosphere.getAssociatedCode())) {
            if ("organization".equals(technosphere.getAssociatedType())) {
                IdwOrg org = idwOrgMapper.selectOrgByOrgCode(technosphere.getAssociatedCode());
                technosphere.setAssociatedName(StringUtils.isNoneBlank(org.getOrgNameCn()) ? org.getOrgNameCn() : org.getOrgNameEn());
            } else {
                IdwWeaponryBasic weaponry = idwWeaponryBasicMapper.selectByWeaponryCode(technosphere.getAssociatedCode());
                technosphere.setAssociatedName(StringUtils.isNoneBlank(weaponry.getNameCn()) ? weaponry.getNameCn() : weaponry.getNameEn());
            }
        }
        return technosphere;
    }

    /**
     * 查询技术领域列表
     *
     * @param idwTechnosphere 技术领域
     * @return 技术领域
     */
    @Override
    public List<IdwTechnosphere> selectIdwTechnosphereList(IdwTechnosphere idwTechnosphere) {
        return idwTechnosphereMapper.selectIdwTechnosphereList(idwTechnosphere);
    }

    /**
     * 新增技术领域
     *
     * @param idwTechnosphere 技术领域
     * @return 结果
     */
    @Override
    public int insertIdwTechnosphere(IdwTechnosphere idwTechnosphere) {
        idwTechnosphere.setCreateBy(ShiroUtils.getUserName());
        idwTechnosphere.setCreateTime(DateUtils.getNowDate());
        if (StringUtils.isNotNull(idwTechnosphere.getParentId()) && idwTechnosphere.getParentId() != 0) {
            IdwTechnosphere parentTechnosphere = idwTechnosphereMapper.selectIdwTechnosphereById(idwTechnosphere.getParentId());
            idwTechnosphere.setLevel(parentTechnosphere.getLevel() + 1);
            idwTechnosphere.setAncestors(parentTechnosphere.getAncestors() + "," + idwTechnosphere.getParentId());
        } else {
            idwTechnosphere.setLevel(1);
            idwTechnosphere.setAncestors("0");
        }
        return idwTechnosphereMapper.insertIdwTechnosphere(idwTechnosphere);
    }

    /**
     * 修改技术领域
     *
     * @param idwTechnosphere 技术领域
     * @return 结果
     */
    @Override
    public int updateIdwTechnosphere(IdwTechnosphere idwTechnosphere) {
        idwTechnosphere.setUpdateBy(ShiroUtils.getUserName());
        idwTechnosphere.setUpdateTime(DateUtils.getNowDate());
        if (StringUtils.isNotNull(idwTechnosphere.getParentId()) && idwTechnosphere.getParentId() != 0) {
            IdwTechnosphere parentTechnosphere = idwTechnosphereMapper.selectIdwTechnosphereById(idwTechnosphere.getParentId());
            idwTechnosphere.setLevel(parentTechnosphere.getLevel() + 1);
            idwTechnosphere.setAncestors(parentTechnosphere.getAncestors() + "," + idwTechnosphere.getParentId());
        } else {
            idwTechnosphere.setLevel(1);
            idwTechnosphere.setAncestors("0");
        }
        return idwTechnosphereMapper.updateIdwTechnosphere(idwTechnosphere);
    }

    /**
     * 删除技术领域信息
     *
     * @param id 技术领域ID
     * @return 结果
     */
    @Override
    public int deleteIdwTechnosphereById(Long id) {
        return idwTechnosphereMapper.deleteIdwTechnosphereById(id, ShiroUtils.getUserName());
    }

    /**
     * 查询技术领域树列表
     *
     * @param id 技术领域ID
     * @return 技术领域树
     */
    @Override
    public List<Ztree> selectIdwTechnosphereTree(Long id) {
        return idwTechnosphereMapper.selectTechnosphereListTreeList(id);
    }

    /**
     * 根据ID构建图谱
     *
     * @param id ID
     * @return 结果
     */
    @Override
    public List<StructureChart> buildChartById(Long id) {
        return idwTechnosphereMapper.buildChartById(id);
    }

    /**
     * 根据ID校验技术领域是否存在子集
     *
     * @param id ID
     * @return 结果
     */
    @Override
    public boolean isExistChildById(Long id) {
        return idwTechnosphereMapper.isExistChildById(id);
    }
}
