package com.lirong.kg.service;

import com.lirong.kg.domain.TaskLog;

import java.util.List;

/**
 * Author：<PERSON><PERSON><PERSON><PERSON><PERSON>
 * Date：2022/3/20
 * Description：<说明>
 */
public interface ITaskLogService {

    /**
     * 添加日志
     * @param taskLog
     * @return
     */
    int addTaskLog(TaskLog taskLog);

    /**
     * 查询任务执行日志
     *
     * @param logId 任务执行日志ID
     * @return 任务执行日志
     */
    public TaskLog selectTaskLogById(Long logId);

    /**
     * 查询任务执行日志列表
     *
     * @param taskLog 任务执行日志
     * @return 任务执行日志集合
     */
    public List<TaskLog> selectTaskLogList(TaskLog taskLog);

    /**
     * 修改任务执行日志
     *
     * @param taskLog 任务执行日志
     * @return 结果
     */
    public int updateTaskLog(TaskLog taskLog);

    /**
     * 批量删除任务执行日志
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteTaskLogByIds(String ids);

    /**
     * 删除任务执行日志信息
     *
     * @param logId 任务执行日志ID
     * @return 结果
     */
    public int deleteTaskLogById(Long logId);

}
