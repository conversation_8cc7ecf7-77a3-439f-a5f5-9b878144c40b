package com.lirong.personnel.common.service;

import com.lirong.common.service.FileDownloadService;
import com.lirong.common.service.HomeStatisticalService;
import com.lirong.common.vo.StatisticsVO;
import com.lirong.personnel.common.domain.IdwPeopleMain;
import com.lirong.personnel.common.vo.SimplePeople;

import java.util.List;
import java.util.Map;

/**
 * 人员信息Service接口
 *
 * <AUTHOR>
 * @date 2020-12-25
 */
public interface IdwPeopleMainService extends FileDownloadService,  HomeStatisticalService {
    /**
     * 查询当前分类中最大的排序号
     *
     * @param category 分类
     * @return 结果
     */
    public int selectMaxOrderNum(String category);

    /**
     * 查询人员信息列表
     *
     * @param idwPeopleMain 人员信息
     * @return 人员信息集合
     */
    public List<IdwPeopleMain> selectPeopleList(IdwPeopleMain idwPeopleMain);

    /**
     * 新增人员信息
     *
     * @param idwPeopleMain 人员信息
     * @return 结果
     */
    public int insertIdwPeopleMain(IdwPeopleMain idwPeopleMain);

    /**
     * 修改人员信息
     *
     * @param idwPeopleMain 人员信息
     * @return 结果
     */
    public int updatePeople(IdwPeopleMain idwPeopleMain);

    /**
     * 根据人员编码查询
     *
     * @param peopleCode 人员编码
     * @return 结果
     */
    public IdwPeopleMain selectPeopleByPeopleCode(String peopleCode);

    /**
     * 获取当前最大的人员编码
     *
     * @return 结果
     */
    public String selectMaxPeopleCode();

    /**
     * 根据人员编码删除人员信息
     *
     * @param peopleCodes 人员编码
     * @return 结果
     */
    public int deleteByPeopleCodes(String peopleCodes);

    /**
     * 根据关键字查询人员信息
     *
     * @param keyword 参数
     * @return 结果
     */
    public List<SimplePeople> selectPeopleByKeyword(String keyword);

    /**
     * 获取数据完整度统计情况
     *
     * @param peopleCode 人员编码
     * @return 结果
     */
    public Map<String, Object> getDataIntegrity(String peopleCode);

    /**
     * 人员查询 (返回简易对象)
     *
     * @param simplePeople 查询参数
     * @return 结果
     */
    public List<SimplePeople> selectSimplePeopleList(SimplePeople simplePeople);

    /**
     * 根据人员类型统计
     *
     * @param createUserNotIs 需要排除的创建用户
     * @return 结果
     */
    public List<StatisticsVO> loadPeopleStatisticsByCategoryExcludeCreateUser(String[] createUserNotIs);

    /**
     * 查询所有人员类型
     *
     * @param createUserNotIs 需要排除的创建用户
     * @return 结果
     */
    public List<String> selectCategoryExcludeCreateUser(String[] createUserNotIs);

    /**
     * 根据人员类型与创建时间构建趋势图(按周统计)
     *
     * @param createUserNotIs 需要排除的创建用户
     * @return 结果
     */
    public List<StatisticsVO> loadTrendExcludeCreateUser(String category, String[] createUserNotIs);

    /**
     * AI解析英文简介
     *
     * @param profileEn 英文简介内容
     * @return 解析后的人员信息
     */
    public Map<String, Object> parseEnglishBiography(String profileEn);
}
