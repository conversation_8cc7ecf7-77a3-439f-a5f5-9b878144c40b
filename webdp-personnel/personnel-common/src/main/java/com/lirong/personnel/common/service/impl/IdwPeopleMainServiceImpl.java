package com.lirong.personnel.common.service.impl;

import com.lirong.common.core.text.Convert;
import com.lirong.common.service.CascadeDeleteService;
import com.lirong.common.service.StatisticalService;
import com.lirong.common.utils.DateUtils;
import com.lirong.common.utils.DictUtils;
import com.lirong.common.utils.ShiroUtils;
import com.lirong.common.utils.StringUtils;
import com.lirong.common.utils.db.DBUtils;
import com.lirong.common.vo.StatisticsVO;
import com.lirong.personnel.common.domain.IdwPeopleMain;
import com.lirong.personnel.common.mapper.IdwPeopleMainMapper;
import com.lirong.personnel.common.service.IdwPeopleMainService;
import com.lirong.personnel.common.vo.SimplePeople;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.NumberFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 人员信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-12-25
 */
@Service
public class IdwPeopleMainServiceImpl implements IdwPeopleMainService {
    private static final Logger logger = LoggerFactory.getLogger(IdwPeopleMainServiceImpl.class);

    @Autowired//人员通用
    private IdwPeopleMainMapper idwPeopleMainMapper;
    @Autowired//统计数据完整度接口
    private List<StatisticalService> statisticalServiceList;
    @Autowired//级联删除接口
    private List<CascadeDeleteService> cascadeDeleteServiceList;

    /**
     * 查询当前分类中最大的排序号
     *
     * @return 结果
     */
    @Override
    public int selectMaxOrderNum(String category) {
        return idwPeopleMainMapper.selectMaxOrderNum(category);
    }

    @Override
    public List<IdwPeopleMain> selectPeopleList(IdwPeopleMain idwPeopleMain) {
        return idwPeopleMainMapper.selectIdwPeopleMainList(idwPeopleMain);
    }

    /**
     * 新增人员信息
     *
     * @param idwPeopleMain 人员信息
     * @return 结果
     */
    @Override
    public int insertIdwPeopleMain(IdwPeopleMain idwPeopleMain) {
        Date nowDate = DateUtils.getNowDate();
        idwPeopleMain.setBirthday(DateUtils.mergeDate(idwPeopleMain.getBirthdayYear(), idwPeopleMain.getBirthdayMonth(), idwPeopleMain.getBirthdayDay()));
        idwPeopleMain.setAppointmentDate(DateUtils.mergeDate(idwPeopleMain.getAppointmentYear(), idwPeopleMain.getAppointmentMonth(), idwPeopleMain.getAppointmentDay()));
        idwPeopleMain.setWorkingDate(DateUtils.mergeDate(idwPeopleMain.getWorkingYear(), idwPeopleMain.getWorkingMonth(), idwPeopleMain.getWorkingDay()));
        idwPeopleMain.setJoinPartyDate(DateUtils.mergeDate(idwPeopleMain.getJoinPartyYear(), idwPeopleMain.getJoinPartyMonth(), idwPeopleMain.getJoinPartyDay()));
        idwPeopleMain.setJoinSecondPartyDate(DateUtils.mergeDate(idwPeopleMain.getJoinSecondPartyYear(), idwPeopleMain.getJoinSecondPartyMonth(), idwPeopleMain.getJoinSecondPartyDay()));
        idwPeopleMain.setCreateBy(ShiroUtils.getUserName());
        idwPeopleMain.setCreateTime(nowDate);
        idwPeopleMain.setUpdateTime(nowDate);
        return idwPeopleMainMapper.insertIdwPeopleMain(idwPeopleMain);
    }

    /**
     * 根据人员id修改人员信息
     *
     * @param idwPeopleMain 人员信息
     * @return 结果
     */
    @Override
    public int updatePeople(IdwPeopleMain idwPeopleMain) {
        idwPeopleMain.setBirthday(DateUtils.mergeDate(idwPeopleMain.getBirthdayYear(), idwPeopleMain.getBirthdayMonth(), idwPeopleMain.getBirthdayDay()));
        idwPeopleMain.setAppointmentDate(DateUtils.mergeDate(idwPeopleMain.getAppointmentYear(), idwPeopleMain.getAppointmentMonth(), idwPeopleMain.getAppointmentDay()));
        idwPeopleMain.setWorkingDate(DateUtils.mergeDate(idwPeopleMain.getWorkingYear(), idwPeopleMain.getWorkingMonth(), idwPeopleMain.getWorkingDay()));
        idwPeopleMain.setJoinPartyDate(DateUtils.mergeDate(idwPeopleMain.getJoinPartyYear(), idwPeopleMain.getJoinPartyMonth(), idwPeopleMain.getJoinPartyDay()));
        idwPeopleMain.setJoinSecondPartyDate(DateUtils.mergeDate(idwPeopleMain.getJoinSecondPartyYear(), idwPeopleMain.getJoinSecondPartyMonth(), idwPeopleMain.getJoinSecondPartyDay()));
        idwPeopleMain.setUpdateBy(ShiroUtils.getUserName());
        idwPeopleMain.setUpdateTime(DateUtils.getNowDate());
        return idwPeopleMainMapper.updatePeople(idwPeopleMain);
    }

    /**
     * 根据人员编码查询
     *
     * @param peopleCode 人员编码
     * @return 结果
     */
    @Override
    public IdwPeopleMain selectPeopleByPeopleCode(String peopleCode) {
        IdwPeopleMain people = idwPeopleMainMapper.selectPeopleByPeopleCode(peopleCode);
        if (StringUtils.isNotNull(people)) {
            Map<String, String> birthdayMap = DateUtils.splitDate(people.getBirthday());
            people.setBirthdayYear(birthdayMap.get("year"));
            people.setBirthdayMonth(birthdayMap.get("month"));
            people.setBirthdayDay(birthdayMap.get("day"));
            Map<String, String> appointmentMap = DateUtils.splitDate(people.getAppointmentDate());
            people.setAppointmentYear(appointmentMap.get("year"));
            people.setAppointmentMonth(appointmentMap.get("month"));
            people.setAppointmentDay(appointmentMap.get("day"));
            Map<String, String> workingMap = DateUtils.splitDate(people.getWorkingDate());
            people.setWorkingYear(workingMap.get("year"));
            people.setWorkingMonth(workingMap.get("month"));
            people.setWorkingDay(workingMap.get("day"));
            Map<String, String> joinPartyMap = DateUtils.splitDate(people.getJoinPartyDate());
            people.setJoinPartyYear(joinPartyMap.get("year"));
            people.setJoinPartyMonth(joinPartyMap.get("month"));
            people.setJoinPartyDay(joinPartyMap.get("day"));
            Map<String, String> joinSecondPartyMap = DateUtils.splitDate(people.getJoinSecondPartyDate());
            people.setJoinSecondPartyYear(joinSecondPartyMap.get("year"));
            people.setJoinSecondPartyMonth(joinSecondPartyMap.get("month"));
            people.setJoinSecondPartyDay(joinSecondPartyMap.get("day"));
        }
        return people;
    }

    /**
     * 获取当前最大的人员编码
     *
     * @return 结果
     */
    @Override
    public String selectMaxPeopleCode() {
        return idwPeopleMainMapper.selectMaxPeopleCode();
    }

    /**
     * 根据人员编码删除人员信息
     *
     * @param peopleCodes 人员编码
     * @return 结果
     */
    @Override
    public int deleteByPeopleCodes(String peopleCodes) {
        String loginName = ShiroUtils.getUserName();
        String deleteTime = DateUtils.dateTimeNow();
        String[] peopleCodeArr = Convert.toStrArray(peopleCodes);
        //删除人员相关纬度
        for (CascadeDeleteService cascadeDeleteService : cascadeDeleteServiceList) {
            cascadeDeleteService.deleteByCode("people", peopleCodeArr, loginName, deleteTime);
        }
        return idwPeopleMainMapper.deleteByPeopleCodes(peopleCodeArr, loginName, deleteTime);
    }

    /**
     * 根据关键字查询人员信息
     *
     * @param keyword 参数
     * @return 结果
     */
    @Override
    public List<SimplePeople> selectPeopleByKeyword(String keyword) {
        return idwPeopleMainMapper.selectPeopleByKeyword(keyword);
    }

    /**
     * 统计人员数据完整度
     *
     * @param peopleCode 人员编码
     * @return 结果
     */
    @Override
    public Map<String, Object> getDataIntegrity(String peopleCode) {
        int dimensionalityContent = 1;
        String dataIntegrityDescribe = "";
        for (StatisticalService service : statisticalServiceList) {
            String statisticalTips = service.getStatisticalTips();//维度名称
            Integer statisticalQuantity = service.getStatisticalQuantity(peopleCode);//数据量
            if (statisticalQuantity > 0) {
                dimensionalityContent++;
            }
            if (StringUtils.isNotEmpty(dataIntegrityDescribe)) {
                dataIntegrityDescribe += "<br/>" + statisticalTips + "：" + statisticalQuantity;
            } else {
                dataIntegrityDescribe += statisticalTips + "：" + statisticalQuantity;
            }
        }
        //计算数据完整度
        double dataIntegrity = (double) dimensionalityContent / (double) (statisticalServiceList.size() + 1);
        //格式化数据完整度
        NumberFormat nf = NumberFormat.getPercentInstance();
        //nf.setMaximumFractionDigits(2);
        //nf.setMinimumFractionDigits(2);
        Map<String, Object> map = new HashMap<>();
        map.put("dataIntegrity", nf.format(dataIntegrity));
        map.put("dataIntegrityDescribe", dataIntegrityDescribe);
        return map;
    }

    /**
     * 人员查询 (返回简易对象)
     *
     * @param simplePeople 查询参数
     * @return 结果
     */
    @Override
    public List<SimplePeople> selectSimplePeopleList(SimplePeople simplePeople) {
        return idwPeopleMainMapper.selectSimplePeopleList(simplePeople);
    }

    /**
     * 根据人员类型统计
     *
     * @param createUserNotIs 需要排除的创建用户
     * @return 结果
     */
    @Override
    public List<StatisticsVO> loadPeopleStatisticsByCategoryExcludeCreateUser(String[] createUserNotIs) {
        return idwPeopleMainMapper.loadPeopleStatisticsByCategoryExcludeCreateUser(createUserNotIs);
    }

    /**
     * 查询所有人员类型
     *
     * @param createUserNotIs 需要排除的创建用户
     * @return 结果
     */
    @Override
    public List<String> selectCategoryExcludeCreateUser(String[] createUserNotIs) {
        return idwPeopleMainMapper.selectCategoryExcludeCreateUser(createUserNotIs);
    }

    /**
     * 根据人员类型与创建时间构建趋势图(按周统计)
     *
     * @param createUserNotIs 需要排除的创建用户
     * @return 结果
     */
    @Override
    public List<StatisticsVO> loadTrendExcludeCreateUser(String category, String[] createUserNotIs) {
        return idwPeopleMainMapper.loadTrendExcludeCreateUser(category, createUserNotIs, DBUtils.getDatabaseType());
    }

    /**
     * 获取文件路径
     *
     * @param type 业务类型
     * @return 文件路径
     */
    @Override
    public List<String> getFilePath(List<String> type) {
        if (StringUtils.isNotNull(type) && type.contains("personnel")) {
            return idwPeopleMainMapper.selectAllFilePath();
        } else {
            return null;
        }
    }

    @Override
    public String subject() {
        return "人员";
    }

    @Override
    public Long statisticalTotalQuantity() {
        return idwPeopleMainMapper.selectCountExcludeCreateUser(null);
    }

    @Override
    public List<StatisticsVO> statisticalItemQuantity() {
        return idwPeopleMainMapper.loadPeopleStatisticsByCategoryExcludeCreateUser(null);
    }

    @Override
    public List<StatisticsVO> trendAnalysis() {
        return null;
    }

    /**
     * AI解析英文简介
     *
     * @param profileEn 英文简介内容
     * @return 解析后的人员信息
     */
    @Override
    public Map<String, Object> parseEnglishBiography(String profileEn) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 构建AI请求的prompt进行信息提取
            String extractPrompt = buildParsingPrompt(profileEn);

            // 2. 调用本地AI模型进行信息提取
            String extractResponse = callLocalAI(extractPrompt);

            // 3. 解析AI响应
            result = parseAIResponse(extractResponse);

            // 4. 翻译英文简介为中文
            String profileCn = translateEnglishToChinese(profileEn);
            if (StringUtils.isNotBlank(profileCn)) {
                result.put("profileCn", profileCn);
            }

        } catch (Exception e) {
            logger.error("AI解析英文简介失败", e);
            throw new RuntimeException("AI解析失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 构建AI解析的提示词
     */
    private String buildParsingPrompt(String profileEn) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请从以下英文简介中提取人员信息，并以JSON格式返回。需要提取的字段包括：\n");
        prompt.append("- nameEn: 英文姓名\n");
        prompt.append("- nameCn: 中文姓名（根据英文名称翻译）\n");
        prompt.append("- militaryRank: 军衔（如Vice Adm.=海军中将，Admiral=海军上将，General=陆军/空军上将等）\n");
        prompt.append("- post: 当前职务\n");
        prompt.append("- orgName: 所在机构\n");
        prompt.append("- birthplace: 出生地\n");
        prompt.append("- graduatedUniversity: 毕业院校\n");
        prompt.append("- education: 最高学历\n");
        prompt.append("- degree: 学位\n");
        prompt.append("- birthdayYear: 出生年份（仅在简介中明确提及时填写，不要推测）\n");
        prompt.append("- birthdayMonth: 出生月份（两位数字，如01，仅在简介中明确提及时填写）\n");
        prompt.append("- birthdayDay: 出生日期（两位数字，如01，仅在简介中明确提及时填写）\n");
        prompt.append("- appointmentYear: 任职年份\n");
        prompt.append("- appointmentMonth: 任职月份（两位数字）\n");
        prompt.append("- appointmentDay: 任职日期（两位数字）\n");
        prompt.append("- gender: 性别（0=男，1=女）\n");
        prompt.append("- country: 国家代码（如US=美国）\n");
        prompt.append("- troopsCategory: 军兵种代码（navy=海军，army=陆军，air=空军，space_force=太空军，marines=海军陆战队，national_guard=国民警卫队，coastguard=海岸警卫队）\n");
        prompt.append("- peopleType: 人员类型（0101=行政管理人员，0102=作战指挥人员，0199=其他）\n");
        prompt.append("- educationalExperience: 教育经历（每条记录一行，格式：时间-学校-专业-学位）\n");
        prompt.append("- assignments: 工作经历（每条记录一行，格式：时间段-机构-职位，如果没有明确时间则写''）\n");
        prompt.append("- rewardsPunishments: 荣誉奖项（每条记录一行）\n");
        prompt.append("- promotion: 晋升情况（每条记录一行，格式：时间-职位/军衔）\n");
        prompt.append("- achievement: 主要成就（每条记录一行）\n\n");
        prompt.append("注意事项：\n");
        prompt.append("1. 根据职务判断人员类型：指挥官、司令员等作战职务为0102；行政、管理职务为0101；其他为0199\n");
        prompt.append("2. 多条记录用换行符分隔，每行一条记录\n");
        prompt.append("3. 时间格式尽量统一为YYYY年MM月或YYYY-MM格式\n");
        prompt.append("4. 出生日期信息：只有在简介中明确提及具体出生日期时才填写，不要根据其他信息推测或计算\n");
        prompt.append("5. 如果简介中没有明确的出生日期信息，birthdayYear、birthdayMonth、birthdayDay字段请留空\n");
        prompt.append("6. 工作经历处理规则：\n");
        prompt.append("   - 只提取明确提及时间的职位，格式：开始年份-结束年份-机构-职位\n");
        prompt.append("   - 如果只提到开始时间，格式：开始年份-至今-机构-职位\n");
        prompt.append("   - 如果没有明确时间，格式：时间不详-机构-职位\n");
        prompt.append("   - 按时间顺序排列，早期职位在前\n");
        prompt.append("   - 不要为没有时间信息的职位推测时间\n");
        prompt.append("   - 同一时间段的多个职位要分别列出\n\n");
        prompt.append("英文简介内容：\n");
        prompt.append(profileEn);
        prompt.append("\n\n请只返回JSON格式的数据，不要包含其他文字说明。");

        return prompt.toString();
    }

    /**
     * 调用本地AI模型
     */
    private String callLocalAI(String prompt) throws Exception {
        String aiUrl = "http://192.168.0.222:8000/v1/chat/completions";

        // 构建请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", "gpt-3.5-turbo");
        requestBody.put("max_tokens", 4000);
        requestBody.put("temperature", 0.1);

        Map<String, String> message = new HashMap<>();
        message.put("role", "user");
        message.put("content", prompt);
        requestBody.put("messages", new Map[]{message});

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(requestBody);

        // 发送HTTP请求
        URL url = new URL(aiUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("Accept", "application/json");
        connection.setDoOutput(true);
        connection.setConnectTimeout(30000); // 30秒连接超时
        connection.setReadTimeout(60000);    // 60秒读取超时

        // 写入请求体
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = jsonRequest.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }

        // 读取响应
        int responseCode = connection.getResponseCode();
        if (responseCode != 200) {
            throw new RuntimeException("AI服务返回错误代码: " + responseCode);
        }

        StringBuilder response = new StringBuilder();
        try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                response.append(responseLine.trim());
            }
        }

        return response.toString();
    }

    /**
     * 解析AI响应
     */
    private Map<String, Object> parseAIResponse(String aiResponse) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode responseNode = objectMapper.readTree(aiResponse);

        // 获取AI返回的内容
        String content = responseNode.path("choices").get(0).path("message").path("content").asText();

        // 提取JSON部分（去除可能的额外文字）
        String jsonContent = extractJsonFromContent(content);

        // 解析JSON为Map
        Map<String, Object> parsedData = objectMapper.readValue(jsonContent, Map.class);

        // 数据清理和验证
        return cleanAndValidateData(parsedData);
    }

    /**
     * 从内容中提取JSON
     */
    private String extractJsonFromContent(String content) {
        // 查找JSON开始和结束位置
        int startIndex = content.indexOf("{");
        int endIndex = content.lastIndexOf("}");

        if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
            return content.substring(startIndex, endIndex + 1);
        }

        return content; // 如果没找到，返回原内容
    }

    /**
     * 清理和验证数据
     */
    private Map<String, Object> cleanAndValidateData(Map<String, Object> data) {
        Map<String, Object> cleanedData = new HashMap<>();

        // 清理字符串字段
        String[] stringFields = {"nameEn", "nameCn", "militaryRank", "post", "orgName",
                                "birthplace", "graduatedUniversity", "education", "degree",
                                "country", "troopsCategory", "peopleType"};

        for (String field : stringFields) {
            Object value = data.get(field);
            if (value != null && StringUtils.isNotBlank(value.toString())) {
                cleanedData.put(field, value.toString().trim());
            }
        }

        // 清理和格式化多行文本字段
        String[] multiLineFields = {"educationalExperience", "assignments", "rewardsPunishments",
                                   "promotion", "achievement"};

        for (String field : multiLineFields) {
            Object value = data.get(field);
            if (value != null && StringUtils.isNotBlank(value.toString())) {
                String formattedText = formatMultiLineText(value.toString());
                if (StringUtils.isNotBlank(formattedText)) {
                    cleanedData.put(field, formattedText);
                }
            }
        }

        // 清理数字字段
        String[] numberFields = {"birthdayYear", "birthdayMonth", "birthdayDay",
                                "appointmentYear", "appointmentMonth", "appointmentDay", "gender"};

        for (String field : numberFields) {
            Object value = data.get(field);
            if (value != null) {
                try {
                    String strValue = value.toString().trim();
                    if (StringUtils.isNotBlank(strValue)) {
                        // 特殊处理出生日期字段 - 验证是否为推测值
                        if (field.startsWith("birthday")) {
                            if (isPossibleGuessedBirthday(strValue, field)) {
                                logger.info("跳过可能推测的出生日期字段 {}: {}", field, strValue);
                                continue; // 跳过推测的出生日期
                            }
                        }

                        // 对于月份和日期，确保是两位数
                        if (field.contains("Month") || field.contains("Day")) {
                            int intValue = Integer.parseInt(strValue);
                            cleanedData.put(field, String.format("%02d", intValue));
                        } else {
                            cleanedData.put(field, strValue);
                        }
                    }
                } catch (NumberFormatException e) {
                    logger.warn("无法解析数字字段 {}: {}", field, value);
                }
            }
        }

        // 特殊处理军兵种字段
        if (data.get("troopsCategory") != null) {
            String troopsCategory = smartIdentifyTroopsCategory(data.get("troopsCategory").toString(),
                                                               cleanedData.get("post"),
                                                               cleanedData.get("orgName"));
            if (StringUtils.isNotBlank(troopsCategory)) {
                cleanedData.put("troopsCategory", troopsCategory);
            }
        }

        // 特殊处理军衔字段
        if (data.get("militaryRank") != null) {
            String militaryRank = standardizeMilitaryRank(data.get("militaryRank").toString());
            if (StringUtils.isNotBlank(militaryRank)) {
                cleanedData.put("militaryRank", militaryRank);
            }
        }

        return cleanedData;
    }

    /**
     * 智能识别军兵种
     */
    private String smartIdentifyTroopsCategory(String originalCategory, Object post, Object orgName) {
        if (originalCategory == null) {
            originalCategory = "";
        }

        String postStr = post != null ? post.toString().toLowerCase() : "";
        String orgStr = orgName != null ? orgName.toString().toLowerCase() : "";
        String categoryStr = originalCategory.toLowerCase();

        // 组合所有文本进行分析
        String allText = (categoryStr + " " + postStr + " " + orgStr).toLowerCase();

        // 按优先级匹配军兵种
        if (allText.contains("submarine") || allText.contains("naval") || allText.contains("navy") ||
            allText.contains("fleet") || allText.contains("ship") || allText.contains("maritime")) {
            return "navy";
        }

        if (allText.contains("marine") || allText.contains("corps")) {
            return "marines";
        }

        if (allText.contains("air force") || allText.contains("aviation") || allText.contains("pilot") ||
            allText.contains("aircraft") || allText.contains("aerospace")) {
            return "air";
        }

        if (allText.contains("space") || allText.contains("satellite") || allText.contains("orbital")) {
            return "space_force";
        }

        if (allText.contains("army") || allText.contains("infantry") || allText.contains("armor") ||
            allText.contains("artillery") || allText.contains("ground")) {
            return "army";
        }

        if (allText.contains("coast guard") || allText.contains("coastal")) {
            return "coastguard";
        }

        if (allText.contains("national guard") || allText.contains("guard")) {
            return "national_guard";
        }

        // 如果AI已经返回了正确的代码，直接使用
        if (categoryStr.equals("navy") || categoryStr.equals("army") || categoryStr.equals("air") ||
            categoryStr.equals("space_force") || categoryStr.equals("marines") ||
            categoryStr.equals("national_guard") || categoryStr.equals("coastguard")) {
            return categoryStr;
        }

        // 默认返回原值或空
        return StringUtils.isNotBlank(originalCategory) ? originalCategory : "";
    }

    /**
     * 标准化军衔
     */
    private String standardizeMilitaryRank(String originalRank) {
        if (StringUtils.isBlank(originalRank)) {
            return "";
        }

        String rank = originalRank.trim();

        // 海军军衔标准化
        if (rank.matches("(?i).*Vice\\s*Adm\\.?.*") || rank.matches("(?i).*Vice\\s*Admiral.*")) {
            return "海军中将";
        }
        if (rank.matches("(?i).*Rear\\s*Adm\\.?.*") || rank.matches("(?i).*Rear\\s*Admiral.*")) {
            return "海军少将";
        }
        if (rank.matches("(?i)^Adm\\.?$") || rank.matches("(?i)^Admiral$")) {
            return "海军上将";
        }
        if (rank.matches("(?i).*Captain.*") && !rank.toLowerCase().contains("army") && !rank.toLowerCase().contains("air")) {
            return "海军上校";
        }
        if (rank.matches("(?i).*Commander.*") && !rank.toLowerCase().contains("army") && !rank.toLowerCase().contains("air")) {
            return "海军中校";
        }
        if (rank.matches("(?i).*Lt\\.?\\s*Cmdr\\.?.*") || rank.matches("(?i).*Lieutenant\\s*Commander.*")) {
            return "海军少校";
        }

        // 陆军/空军军衔标准化
        if (rank.matches("(?i).*Lt\\.?\\s*Gen\\.?.*") || rank.matches("(?i).*Lieutenant\\s*General.*")) {
            return "陆军中将";
        }
        if (rank.matches("(?i).*Maj\\.?\\s*Gen\\.?.*") || rank.matches("(?i).*Major\\s*General.*")) {
            return "陆军少将";
        }
        if (rank.matches("(?i).*Brig\\.?\\s*Gen\\.?.*") || rank.matches("(?i).*Brigadier\\s*General.*")) {
            return "陆军准将";
        }
        if (rank.matches("(?i)^General$") || rank.matches("(?i)^Gen\\.?$")) {
            return "陆军上将";
        }
        if (rank.matches("(?i).*Colonel.*") || rank.matches("(?i).*Col\\.?.*")) {
            return "陆军上校";
        }
        if (rank.matches("(?i).*Lt\\.?\\s*Col\\.?.*") || rank.matches("(?i).*Lieutenant\\s*Colonel.*")) {
            return "陆军中校";
        }
        if (rank.matches("(?i)^Major$") || rank.matches("(?i)^Maj\\.?$")) {
            return "陆军少校";
        }

        // 如果没有匹配到标准格式，返回原值
        return rank;
    }

    /**
     * 判断是否为推测的出生日期
     */
    private boolean isPossibleGuessedBirthday(String value, String field) {
        try {
            int intValue = Integer.parseInt(value);

            // 检查年份是否为明显的推测值
            if ("birthdayYear".equals(field)) {
                int currentYear = java.time.Year.now().getValue();
                // 如果年份是整十年（如1960, 1970等）或者过于接近当前年份，可能是推测的
                if (intValue % 10 == 0 || (currentYear - intValue) < 20 || (currentYear - intValue) > 100) {
                    return true;
                }
            }

            // 检查月份和日期是否为默认值
            if ("birthdayMonth".equals(field) || "birthdayDay".equals(field)) {
                // 如果是01月01日，很可能是默认推测值
                if (intValue == 1) {
                    return true;
                }
            }

            return false;
        } catch (NumberFormatException e) {
            return true; // 如果不是数字，认为是无效的
        }
    }

    /**
     * 格式化多行文本，确保每条记录一行
     */
    private String formatMultiLineText(String text) {
        if (StringUtils.isBlank(text)) {
            return "";
        }

        // 移除方括号和引号
        text = text.replaceAll("^\\[|\\]$", "").trim();
        text = text.replaceAll("^\"|\"$", "").trim();

        // 特殊处理工作经历格式
        if (text.contains("USS") || text.contains("commander") || text.contains("officer")) {
            return formatWorkExperience(text);
        }

        // 按逗号分割，但保留句子内的逗号
        String[] items = text.split("(?<=\\)),\\s*(?=[A-Z])|(?<=\\d),\\s*(?=[A-Z])|,\\s*(?=\\d{4})|,\\s*(?=[A-Z][a-z]+\\s+\\d{4})");

        StringBuilder result = new StringBuilder();
        for (String item : items) {
            item = item.trim();
            if (StringUtils.isNotBlank(item)) {
                // 移除开头和结尾的引号
                item = item.replaceAll("^\"|\"$", "").trim();
                if (result.length() > 0) {
                    result.append("\n");
                }
                result.append(item);
            }
        }

        return result.toString();
    }

    /**
     * 格式化工作经历
     */
    private String formatWorkExperience(String text) {
        StringBuilder result = new StringBuilder();

        // 移除方括号和引号
        text = text.replaceAll("^\\[|\\]$", "").trim();
        text = text.replaceAll("^\"|\"$", "").trim();

        // 按逗号分割，但保留重要的句子结构
        String[] items = text.split(",\\s*(?=[A-Z])|;\\s*");

        for (String item : items) {
            item = item.trim();
            if (StringUtils.isBlank(item)) {
                continue;
            }

            // 移除开头和结尾的引号
            item = item.replaceAll("^\"|\"$", "").trim();

            // 跳过过短的片段
            if (item.length() < 10) {
                continue;
            }

            // 解析每个工作经历项目
            String formattedItem = parseWorkItem(item);
            if (StringUtils.isNotBlank(formattedItem)) {
                if (result.length() > 0) {
                    result.append("\n");
                }
                result.append(formattedItem);
            }
        }

        return result.toString();
    }

    /**
     * 解析单个工作经历项目
     */
    private String parseWorkItem(String item) {
        String timeInfo = extractTimeInfo(item);
        String[] orgAndPosition = extractOrgAndPosition(item);

        if (StringUtils.isNotBlank(orgAndPosition[0]) && StringUtils.isNotBlank(orgAndPosition[1])) {
            return timeInfo + "-" + orgAndPosition[0] + "-" + orgAndPosition[1];
        }

        return ""; // 如果无法提取有效信息，返回空字符串
    }

    /**
     * 提取时间信息
     */
    private String extractTimeInfo(String sentence) {
        // 查找时间模式：from February 2006 to February 2009
        Pattern fromToPattern = Pattern.compile("from\\s+(\\w+\\s+)?(\\d{4})\\s+to\\s+(\\w+\\s+)?(\\d{4})");
        Matcher fromToMatcher = fromToPattern.matcher(sentence);
        if (fromToMatcher.find()) {
            String startYear = fromToMatcher.group(2);
            String endYear = fromToMatcher.group(4);
            return startYear + "-" + endYear;
        }

        // 查找时间模式：October 2012 to August 2014
        Pattern monthYearPattern = Pattern.compile("(\\w+\\s+)?(\\d{4})\\s+to\\s+(\\w+\\s+)?(\\d{4})");
        Matcher monthYearMatcher = monthYearPattern.matcher(sentence);
        if (monthYearMatcher.find()) {
            String startYear = monthYearMatcher.group(2);
            String endYear = monthYearMatcher.group(4);
            return startYear + "-" + endYear;
        }

        // 查找单个年份模式
        Pattern singleYearPattern = Pattern.compile("\\b(\\d{4})\\b");
        Matcher singleYearMatcher = singleYearPattern.matcher(sentence);
        if (singleYearMatcher.find()) {
            String year = singleYearMatcher.group(1);
            return year + "-" + year;
        }

        return "时间不详";
    }

    /**
     * 提取机构和职位信息
     */
    private String[] extractOrgAndPosition(String sentence) {
        String org = "";
        String position = "";

        // 处理USS舰艇
        Pattern ussPattern = Pattern.compile("USS\\s+([^\\s,]+(?:\\s+\\([^)]+\\))?)");
        Matcher ussMatcher = ussPattern.matcher(sentence);
        if (ussMatcher.find()) {
            org = "USS " + ussMatcher.group(1);
            if (sentence.contains("executive officer")) {
                position = "executive officer";
            } else if (sentence.contains("commanding officer")) {
                position = "commanding officer";
            }
        }
        // 处理Squadron
        else if (sentence.contains("Squadron")) {
            Pattern squadronPattern = Pattern.compile("(\\w+\\s+Squadron\\s+\\d+)");
            Matcher squadronMatcher = squadronPattern.matcher(sentence);
            if (squadronMatcher.find()) {
                org = squadronMatcher.group(1);
            }

            if (sentence.contains("deputy commander")) {
                position = "deputy commander";
            } else if (sentence.contains("commander")) {
                position = "commander";
            }
        }
        // 处理其他机构
        else {
            // 提取机构名称
            if (sentence.contains("U.S. Pacific Fleet Submarine Force")) {
                org = "U.S. Pacific Fleet Submarine Force";
            } else if (sentence.contains("U.S. Pacific Fleet")) {
                org = "U.S. Pacific Fleet";
            } else if (sentence.contains("U.S. Joint Forces Command")) {
                org = "U.S. Joint Forces Command";
            } else if (sentence.contains("U.S. Fleet Forces Command")) {
                org = "U.S. Fleet Forces Command";
            } else if (sentence.contains("U.S. Submarine Force")) {
                org = "U.S. Submarine Force";
            } else if (sentence.contains("Naval Reactors headquarters")) {
                org = "Naval Reactors headquarters";
            } else if (sentence.contains("Submarine Group")) {
                Pattern groupPattern = Pattern.compile("(Submarine Group\\s+\\d+)");
                Matcher groupMatcher = groupPattern.matcher(sentence);
                if (groupMatcher.find()) {
                    org = groupMatcher.group(1);
                }
            }

            // 提取职位 - 按优先级匹配
            if (sentence.contains("radiological controls officer")) {
                position = "radiological controls officer";
            } else if (sentence.contains("special assistant")) {
                position = "special assistant";
            } else if (sentence.contains("deputy commander")) {
                position = "deputy commander";
            } else if (sentence.contains("chief of staff")) {
                position = "chief of staff";
            } else if (sentence.contains("Prospective Commanding Officer Course instructor")) {
                position = "Prospective Commanding Officer Course instructor";
            } else if (sentence.contains("director, Joint and Fleet Operations")) {
                position = "director, Joint and Fleet Operations (N3)";
            } else if (sentence.contains("director")) {
                position = "director";
            } else if (sentence.contains("commander")) {
                position = "commander";
            } else if (sentence.contains("instructor")) {
                position = "instructor";
            }
        }

        return new String[]{org, position};
    }

    /**
     * 翻译英文简介为中文
     */
    private String translateEnglishToChinese(String profileEn) {
        try {
            // 构建翻译提示词
            String translatePrompt = buildTranslationPrompt(profileEn);

            // 调用AI进行翻译
            String translateResponse = callLocalAI(translatePrompt);

            // 解析翻译结果
            return parseTranslationResponse(translateResponse);

        } catch (Exception e) {
            logger.error("翻译英文简介失败", e);
            return ""; // 翻译失败时返回空字符串，不影响主要功能
        }
    }

    /**
     * 构建翻译提示词
     */
    private String buildTranslationPrompt(String profileEn) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请将以下英文军事人员简介翻译成中文，要求：\n");
        prompt.append("1. 翻译要准确、流畅、符合中文表达习惯\n");
        prompt.append("2. 保持原文的结构和段落格式\n");
        prompt.append("3. 人名、地名、机构名等专有名词要准确翻译\n");
        prompt.append("4. 军衔、职务等要使用标准的中文军事术语，具体对照如下：\n");
        prompt.append("   - Admiral = 海军上将\n");
        prompt.append("   - Vice Admiral (Vice Adm.) = 海军中将\n");
        prompt.append("   - Rear Admiral = 海军少将\n");
        prompt.append("   - Captain = 海军上校\n");
        prompt.append("   - Commander = 海军中校\n");
        prompt.append("   - Lieutenant Commander = 海军少校\n");
        prompt.append("   - General = 陆军/空军上将\n");
        prompt.append("   - Lieutenant General = 陆军/空军中将\n");
        prompt.append("   - Major General = 陆军/空军少将\n");
        prompt.append("   - Brigadier General = 陆军/空军准将\n");
        prompt.append("   - Colonel = 陆军/空军上校\n");
        prompt.append("   - Lieutenant Colonel = 陆军/空军中校\n");
        prompt.append("   - Major = 陆军/空军少校\n");
        prompt.append("5. 职务翻译要准确：\n");
        prompt.append("   - Commander = 指挥官/司令官\n");
        prompt.append("   - Deputy Commander = 副指挥官/副司令官\n");
        prompt.append("   - Chief of Staff = 参谋长\n");
        prompt.append("   - Executive Officer = 执行官\n");
        prompt.append("   - Commanding Officer = 指挥官\n");
        prompt.append("   - Squadron = 中队\n");
        prompt.append("   - Fleet = 舰队\n");
        prompt.append("   - Force = 部队\n");
        prompt.append("6. 舰艇名称保持英文，但要加上中文说明：\n");
        prompt.append("   - USS = 美国海军舰艇\n");
        prompt.append("   - SSN = 核动力攻击潜艇\n");
        prompt.append("   - SSBN = 核动力弹道导弹潜艇\n");
        prompt.append("7. 时间、日期保持原格式\n");
        prompt.append("8. 只返回翻译后的中文内容，不要包含其他说明文字\n\n");
        prompt.append("英文简介：\n");
        prompt.append(profileEn);
        prompt.append("\n\n请直接返回翻译后的中文简介：");

        return prompt.toString();
    }

    /**
     * 解析翻译响应
     */
    private String parseTranslationResponse(String translateResponse) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode responseNode = objectMapper.readTree(translateResponse);

        // 获取AI返回的翻译内容
        String content = responseNode.path("choices").get(0).path("message").path("content").asText();

        // 清理翻译结果
        content = content.trim();

        // 移除可能的引号包围
        if (content.startsWith("\"") && content.endsWith("\"")) {
            content = content.substring(1, content.length() - 1);
        }

        return content;
    }
}
