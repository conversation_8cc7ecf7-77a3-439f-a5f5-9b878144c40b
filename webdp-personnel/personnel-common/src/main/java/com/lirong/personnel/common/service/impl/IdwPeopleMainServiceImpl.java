package com.lirong.personnel.common.service.impl;

import com.lirong.common.core.text.Convert;
import com.lirong.common.service.CascadeDeleteService;
import com.lirong.common.service.StatisticalService;
import com.lirong.common.utils.DateUtils;
import com.lirong.common.utils.DictUtils;
import com.lirong.common.utils.ShiroUtils;
import com.lirong.common.utils.StringUtils;
import com.lirong.common.utils.db.DBUtils;
import com.lirong.common.vo.StatisticsVO;
import com.lirong.personnel.common.domain.IdwPeopleMain;
import com.lirong.personnel.common.mapper.IdwPeopleMainMapper;
import com.lirong.personnel.common.service.IdwPeopleMainService;
import com.lirong.personnel.common.vo.SimplePeople;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.NumberFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.JsonNode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 人员信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2020-12-25
 */
@Service
public class IdwPeopleMainServiceImpl implements IdwPeopleMainService {
    private static final Logger logger = LoggerFactory.getLogger(IdwPeopleMainServiceImpl.class);

    @Autowired//人员通用
    private IdwPeopleMainMapper idwPeopleMainMapper;
    @Autowired//统计数据完整度接口
    private List<StatisticalService> statisticalServiceList;
    @Autowired//级联删除接口
    private List<CascadeDeleteService> cascadeDeleteServiceList;

    /**
     * 查询当前分类中最大的排序号
     *
     * @return 结果
     */
    @Override
    public int selectMaxOrderNum(String category) {
        return idwPeopleMainMapper.selectMaxOrderNum(category);
    }

    @Override
    public List<IdwPeopleMain> selectPeopleList(IdwPeopleMain idwPeopleMain) {
        return idwPeopleMainMapper.selectIdwPeopleMainList(idwPeopleMain);
    }

    /**
     * 新增人员信息
     *
     * @param idwPeopleMain 人员信息
     * @return 结果
     */
    @Override
    public int insertIdwPeopleMain(IdwPeopleMain idwPeopleMain) {
        Date nowDate = DateUtils.getNowDate();
        idwPeopleMain.setBirthday(DateUtils.mergeDate(idwPeopleMain.getBirthdayYear(), idwPeopleMain.getBirthdayMonth(), idwPeopleMain.getBirthdayDay()));
        idwPeopleMain.setAppointmentDate(DateUtils.mergeDate(idwPeopleMain.getAppointmentYear(), idwPeopleMain.getAppointmentMonth(), idwPeopleMain.getAppointmentDay()));
        idwPeopleMain.setWorkingDate(DateUtils.mergeDate(idwPeopleMain.getWorkingYear(), idwPeopleMain.getWorkingMonth(), idwPeopleMain.getWorkingDay()));
        idwPeopleMain.setJoinPartyDate(DateUtils.mergeDate(idwPeopleMain.getJoinPartyYear(), idwPeopleMain.getJoinPartyMonth(), idwPeopleMain.getJoinPartyDay()));
        idwPeopleMain.setJoinSecondPartyDate(DateUtils.mergeDate(idwPeopleMain.getJoinSecondPartyYear(), idwPeopleMain.getJoinSecondPartyMonth(), idwPeopleMain.getJoinSecondPartyDay()));
        idwPeopleMain.setCreateBy(ShiroUtils.getUserName());
        idwPeopleMain.setCreateTime(nowDate);
        idwPeopleMain.setUpdateTime(nowDate);
        return idwPeopleMainMapper.insertIdwPeopleMain(idwPeopleMain);
    }

    /**
     * 根据人员id修改人员信息
     *
     * @param idwPeopleMain 人员信息
     * @return 结果
     */
    @Override
    public int updatePeople(IdwPeopleMain idwPeopleMain) {
        idwPeopleMain.setBirthday(DateUtils.mergeDate(idwPeopleMain.getBirthdayYear(), idwPeopleMain.getBirthdayMonth(), idwPeopleMain.getBirthdayDay()));
        idwPeopleMain.setAppointmentDate(DateUtils.mergeDate(idwPeopleMain.getAppointmentYear(), idwPeopleMain.getAppointmentMonth(), idwPeopleMain.getAppointmentDay()));
        idwPeopleMain.setWorkingDate(DateUtils.mergeDate(idwPeopleMain.getWorkingYear(), idwPeopleMain.getWorkingMonth(), idwPeopleMain.getWorkingDay()));
        idwPeopleMain.setJoinPartyDate(DateUtils.mergeDate(idwPeopleMain.getJoinPartyYear(), idwPeopleMain.getJoinPartyMonth(), idwPeopleMain.getJoinPartyDay()));
        idwPeopleMain.setJoinSecondPartyDate(DateUtils.mergeDate(idwPeopleMain.getJoinSecondPartyYear(), idwPeopleMain.getJoinSecondPartyMonth(), idwPeopleMain.getJoinSecondPartyDay()));
        idwPeopleMain.setUpdateBy(ShiroUtils.getUserName());
        idwPeopleMain.setUpdateTime(DateUtils.getNowDate());
        return idwPeopleMainMapper.updatePeople(idwPeopleMain);
    }

    /**
     * 根据人员编码查询
     *
     * @param peopleCode 人员编码
     * @return 结果
     */
    @Override
    public IdwPeopleMain selectPeopleByPeopleCode(String peopleCode) {
        IdwPeopleMain people = idwPeopleMainMapper.selectPeopleByPeopleCode(peopleCode);
        if (StringUtils.isNotNull(people)) {
            Map<String, String> birthdayMap = DateUtils.splitDate(people.getBirthday());
            people.setBirthdayYear(birthdayMap.get("year"));
            people.setBirthdayMonth(birthdayMap.get("month"));
            people.setBirthdayDay(birthdayMap.get("day"));
            Map<String, String> appointmentMap = DateUtils.splitDate(people.getAppointmentDate());
            people.setAppointmentYear(appointmentMap.get("year"));
            people.setAppointmentMonth(appointmentMap.get("month"));
            people.setAppointmentDay(appointmentMap.get("day"));
            Map<String, String> workingMap = DateUtils.splitDate(people.getWorkingDate());
            people.setWorkingYear(workingMap.get("year"));
            people.setWorkingMonth(workingMap.get("month"));
            people.setWorkingDay(workingMap.get("day"));
            Map<String, String> joinPartyMap = DateUtils.splitDate(people.getJoinPartyDate());
            people.setJoinPartyYear(joinPartyMap.get("year"));
            people.setJoinPartyMonth(joinPartyMap.get("month"));
            people.setJoinPartyDay(joinPartyMap.get("day"));
            Map<String, String> joinSecondPartyMap = DateUtils.splitDate(people.getJoinSecondPartyDate());
            people.setJoinSecondPartyYear(joinSecondPartyMap.get("year"));
            people.setJoinSecondPartyMonth(joinSecondPartyMap.get("month"));
            people.setJoinSecondPartyDay(joinSecondPartyMap.get("day"));
        }
        return people;
    }

    /**
     * 获取当前最大的人员编码
     *
     * @return 结果
     */
    @Override
    public String selectMaxPeopleCode() {
        return idwPeopleMainMapper.selectMaxPeopleCode();
    }

    /**
     * 根据人员编码删除人员信息
     *
     * @param peopleCodes 人员编码
     * @return 结果
     */
    @Override
    public int deleteByPeopleCodes(String peopleCodes) {
        String loginName = ShiroUtils.getUserName();
        String deleteTime = DateUtils.dateTimeNow();
        String[] peopleCodeArr = Convert.toStrArray(peopleCodes);
        //删除人员相关纬度
        for (CascadeDeleteService cascadeDeleteService : cascadeDeleteServiceList) {
            cascadeDeleteService.deleteByCode("people", peopleCodeArr, loginName, deleteTime);
        }
        return idwPeopleMainMapper.deleteByPeopleCodes(peopleCodeArr, loginName, deleteTime);
    }

    /**
     * 根据关键字查询人员信息
     *
     * @param keyword 参数
     * @return 结果
     */
    @Override
    public List<SimplePeople> selectPeopleByKeyword(String keyword) {
        return idwPeopleMainMapper.selectPeopleByKeyword(keyword);
    }

    /**
     * 统计人员数据完整度
     *
     * @param peopleCode 人员编码
     * @return 结果
     */
    @Override
    public Map<String, Object> getDataIntegrity(String peopleCode) {
        int dimensionalityContent = 1;
        String dataIntegrityDescribe = "";
        for (StatisticalService service : statisticalServiceList) {
            String statisticalTips = service.getStatisticalTips();//维度名称
            Integer statisticalQuantity = service.getStatisticalQuantity(peopleCode);//数据量
            if (statisticalQuantity > 0) {
                dimensionalityContent++;
            }
            if (StringUtils.isNotEmpty(dataIntegrityDescribe)) {
                dataIntegrityDescribe += "<br/>" + statisticalTips + "：" + statisticalQuantity;
            } else {
                dataIntegrityDescribe += statisticalTips + "：" + statisticalQuantity;
            }
        }
        //计算数据完整度
        double dataIntegrity = (double) dimensionalityContent / (double) (statisticalServiceList.size() + 1);
        //格式化数据完整度
        NumberFormat nf = NumberFormat.getPercentInstance();
        //nf.setMaximumFractionDigits(2);
        //nf.setMinimumFractionDigits(2);
        Map<String, Object> map = new HashMap<>();
        map.put("dataIntegrity", nf.format(dataIntegrity));
        map.put("dataIntegrityDescribe", dataIntegrityDescribe);
        return map;
    }

    /**
     * 人员查询 (返回简易对象)
     *
     * @param simplePeople 查询参数
     * @return 结果
     */
    @Override
    public List<SimplePeople> selectSimplePeopleList(SimplePeople simplePeople) {
        return idwPeopleMainMapper.selectSimplePeopleList(simplePeople);
    }

    /**
     * 根据人员类型统计
     *
     * @param createUserNotIs 需要排除的创建用户
     * @return 结果
     */
    @Override
    public List<StatisticsVO> loadPeopleStatisticsByCategoryExcludeCreateUser(String[] createUserNotIs) {
        return idwPeopleMainMapper.loadPeopleStatisticsByCategoryExcludeCreateUser(createUserNotIs);
    }

    /**
     * 查询所有人员类型
     *
     * @param createUserNotIs 需要排除的创建用户
     * @return 结果
     */
    @Override
    public List<String> selectCategoryExcludeCreateUser(String[] createUserNotIs) {
        return idwPeopleMainMapper.selectCategoryExcludeCreateUser(createUserNotIs);
    }

    /**
     * 根据人员类型与创建时间构建趋势图(按周统计)
     *
     * @param createUserNotIs 需要排除的创建用户
     * @return 结果
     */
    @Override
    public List<StatisticsVO> loadTrendExcludeCreateUser(String category, String[] createUserNotIs) {
        return idwPeopleMainMapper.loadTrendExcludeCreateUser(category, createUserNotIs, DBUtils.getDatabaseType());
    }

    /**
     * 获取文件路径
     *
     * @param type 业务类型
     * @return 文件路径
     */
    @Override
    public List<String> getFilePath(List<String> type) {
        if (StringUtils.isNotNull(type) && type.contains("personnel")) {
            return idwPeopleMainMapper.selectAllFilePath();
        } else {
            return null;
        }
    }

    @Override
    public String subject() {
        return "人员";
    }

    @Override
    public Long statisticalTotalQuantity() {
        return idwPeopleMainMapper.selectCountExcludeCreateUser(null);
    }

    @Override
    public List<StatisticsVO> statisticalItemQuantity() {
        return idwPeopleMainMapper.loadPeopleStatisticsByCategoryExcludeCreateUser(null);
    }

    @Override
    public List<StatisticsVO> trendAnalysis() {
        return null;
    }

    /**
     * AI解析英文简介
     *
     * @param profileEn 英文简介内容
     * @return 解析后的人员信息
     */
    @Override
    public Map<String, Object> parseEnglishBiography(String profileEn) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 构建AI请求的prompt
            String prompt = buildParsingPrompt(profileEn);

            // 调用本地AI模型
            String aiResponse = callLocalAI(prompt);

            // 解析AI响应
            result = parseAIResponse(aiResponse);

        } catch (Exception e) {
            logger.error("AI解析英文简介失败", e);
            throw new RuntimeException("AI解析失败: " + e.getMessage());
        }

        return result;
    }

    /**
     * 构建AI解析的提示词
     */
    private String buildParsingPrompt(String profileEn) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请从以下英文简介中提取人员信息，并以JSON格式返回。需要提取的字段包括：\n");
        prompt.append("- nameEn: 英文姓名\n");
        prompt.append("- nameCn: 中文姓名（根据英文名称翻译）\n");
        prompt.append("- militaryRank: 军衔\n");
        prompt.append("- post: 当前职务\n");
        prompt.append("- orgName: 所在机构\n");
        prompt.append("- birthplace: 出生地\n");
        prompt.append("- graduatedUniversity: 毕业院校\n");
        prompt.append("- education: 学历\n");
        prompt.append("- degree: 学位\n");
        prompt.append("- birthdayYear: 出生年份\n");
        prompt.append("- birthdayMonth: 出生月份（两位数字，如01）\n");
        prompt.append("- birthdayDay: 出生日期（两位数字，如01）\n");
        prompt.append("- appointmentYear: 任职年份\n");
        prompt.append("- appointmentMonth: 任职月份（两位数字）\n");
        prompt.append("- appointmentDay: 任职日期（两位数字）\n");
        prompt.append("- gender: 性别（0=男，1=女）\n");
        prompt.append("- country: 国家代码（如US=美国）\n");
        prompt.append("- troopsCategory: 军兵种\n");
        prompt.append("- peopleType: 人员类型（0101=行政管理人员，0102=作战指挥人员，0199=其他）\n");
        prompt.append("- educationalExperience: 教育经历（每条记录一行，格式：时间-学校-专业-学位）\n");
        prompt.append("- assignments: 工作经历（每条记录一行，格式：时间-职位-机构）\n");
        prompt.append("- rewardsPunishments: 荣誉奖项（每条记录一行）\n");
        prompt.append("- promotion: 晋升情况（每条记录一行，格式：时间-职位/军衔）\n");
        prompt.append("- achievement: 主要成就（每条记录一行）\n\n");
        prompt.append("注意事项：\n");
        prompt.append("1. 根据职务判断人员类型：指挥官、司令员等作战职务为0102；行政、管理职务为0101；其他为0199\n");
        prompt.append("2. 多条记录用换行符分隔，每行一条记录\n");
        prompt.append("3. 时间格式尽量统一为YYYY年MM月或YYYY-MM格式\n\n");
        prompt.append("英文简介内容：\n");
        prompt.append(profileEn);
        prompt.append("\n\n请只返回JSON格式的数据，不要包含其他文字说明。");

        return prompt.toString();
    }

    /**
     * 调用本地AI模型
     */
    private String callLocalAI(String prompt) throws Exception {
        String aiUrl = "http://192.168.0.222:8000/v1/chat/completions";

        // 构建请求体
        Map<String, Object> requestBody = new HashMap<>();
        requestBody.put("model", "gpt-3.5-turbo");
        requestBody.put("max_tokens", 2000);
        requestBody.put("temperature", 0.1);

        Map<String, String> message = new HashMap<>();
        message.put("role", "user");
        message.put("content", prompt);
        requestBody.put("messages", new Map[]{message});

        ObjectMapper objectMapper = new ObjectMapper();
        String jsonRequest = objectMapper.writeValueAsString(requestBody);

        // 发送HTTP请求
        URL url = new URL(aiUrl);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setRequestProperty("Accept", "application/json");
        connection.setDoOutput(true);
        connection.setConnectTimeout(30000); // 30秒连接超时
        connection.setReadTimeout(60000);    // 60秒读取超时

        // 写入请求体
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = jsonRequest.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }

        // 读取响应
        int responseCode = connection.getResponseCode();
        if (responseCode != 200) {
            throw new RuntimeException("AI服务返回错误代码: " + responseCode);
        }

        StringBuilder response = new StringBuilder();
        try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                response.append(responseLine.trim());
            }
        }

        return response.toString();
    }

    /**
     * 解析AI响应
     */
    private Map<String, Object> parseAIResponse(String aiResponse) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode responseNode = objectMapper.readTree(aiResponse);

        // 获取AI返回的内容
        String content = responseNode.path("choices").get(0).path("message").path("content").asText();

        // 提取JSON部分（去除可能的额外文字）
        String jsonContent = extractJsonFromContent(content);

        // 解析JSON为Map
        Map<String, Object> parsedData = objectMapper.readValue(jsonContent, Map.class);

        // 数据清理和验证
        return cleanAndValidateData(parsedData);
    }

    /**
     * 从内容中提取JSON
     */
    private String extractJsonFromContent(String content) {
        // 查找JSON开始和结束位置
        int startIndex = content.indexOf("{");
        int endIndex = content.lastIndexOf("}");

        if (startIndex != -1 && endIndex != -1 && endIndex > startIndex) {
            return content.substring(startIndex, endIndex + 1);
        }

        return content; // 如果没找到，返回原内容
    }

    /**
     * 清理和验证数据
     */
    private Map<String, Object> cleanAndValidateData(Map<String, Object> data) {
        Map<String, Object> cleanedData = new HashMap<>();

        // 清理字符串字段
        String[] stringFields = {"nameEn", "nameCn", "militaryRank", "post", "orgName",
                                "birthplace", "graduatedUniversity", "education", "degree",
                                "country", "troopsCategory", "peopleType"};

        for (String field : stringFields) {
            Object value = data.get(field);
            if (value != null && StringUtils.isNotBlank(value.toString())) {
                cleanedData.put(field, value.toString().trim());
            }
        }

        // 清理和格式化多行文本字段
        String[] multiLineFields = {"educationalExperience", "assignments", "rewardsPunishments",
                                   "promotion", "achievement"};

        for (String field : multiLineFields) {
            Object value = data.get(field);
            if (value != null && StringUtils.isNotBlank(value.toString())) {
                String formattedText = formatMultiLineText(value.toString());
                if (StringUtils.isNotBlank(formattedText)) {
                    cleanedData.put(field, formattedText);
                }
            }
        }

        // 清理数字字段
        String[] numberFields = {"birthdayYear", "birthdayMonth", "birthdayDay",
                                "appointmentYear", "appointmentMonth", "appointmentDay", "gender"};

        for (String field : numberFields) {
            Object value = data.get(field);
            if (value != null) {
                try {
                    String strValue = value.toString().trim();
                    if (StringUtils.isNotBlank(strValue)) {
                        // 对于月份和日期，确保是两位数
                        if (field.contains("Month") || field.contains("Day")) {
                            int intValue = Integer.parseInt(strValue);
                            cleanedData.put(field, String.format("%02d", intValue));
                        } else {
                            cleanedData.put(field, strValue);
                        }
                    }
                } catch (NumberFormatException e) {
                    logger.warn("无法解析数字字段 {}: {}", field, value);
                }
            }
        }

        return cleanedData;
    }

    /**
     * 格式化多行文本，确保每条记录一行
     */
    private String formatMultiLineText(String text) {
        if (StringUtils.isBlank(text)) {
            return "";
        }

        // 移除方括号和引号
        text = text.replaceAll("^\\[|\\]$", "").trim();
        text = text.replaceAll("^\"|\"$", "").trim();

        // 按逗号分割，但保留句子内的逗号
        String[] items = text.split("(?<=\\)),\\s*(?=[A-Z])|(?<=\\d),\\s*(?=[A-Z])|,\\s*(?=\\d{4})|,\\s*(?=[A-Z][a-z]+\\s+\\d{4})");

        StringBuilder result = new StringBuilder();
        for (String item : items) {
            item = item.trim();
            if (StringUtils.isNotBlank(item)) {
                // 移除开头和结尾的引号
                item = item.replaceAll("^\"|\"$", "").trim();
                if (result.length() > 0) {
                    result.append("\n");
                }
                result.append(item);
            }
        }

        return result.toString();
    }
}
