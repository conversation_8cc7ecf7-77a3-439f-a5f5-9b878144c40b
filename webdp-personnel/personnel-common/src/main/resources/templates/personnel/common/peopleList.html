<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('人员加工列表')" />
    <th:block th:include="include :: layout-latest-css"/>
    <th:block th:include="include :: select2-css" />
    <th:block th:include="include :: ztree-css"/>
</head>
<body class="white-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="form-people">
                <input type="hidden" name="category" th:value="${category}">
                <input type="hidden" name="auditPhase" value="not_submit">
                <div class="select-list">
                    <ul>
                        <li>
                            <label style="width: 100px;">国家/地区：</label>
                            <select name="country" th:with="type=${@dict.getType('sys_country')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <label>人员编码：</label>
                            <input type="text" name="peopleCode"/>
                        </li>
                        <li>
                            <label>人员名称：</label>
                            <input type="text" name="nameCn"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search('form-people', 'bootstrap-table-people')"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('form-people', 'bootstrap-table-people')"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar-people" role="group">
            <!--<a class="btn btn-primary multiple disabled" onclick="submitPublishPeople()">
                <i class="fa fa-check"></i> 提交发布
            </a>-->
            <a class="btn btn-success" onclick="$.operate.addTab()" shiro:hasPermission="people:people:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.editTab()" shiro:hasPermission="people:people:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="people:people:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-info" onclick="importExcel()" shiro:hasPermission="people:people:import">
                <i class="fa fa-upload"></i> 导入
            </a>
            <a class="btn btn-warning multiple disabled" onclick="exportExcel()" shiro:hasPermission="people:people:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-bordered">
            <table id="bootstrap-table-people" data-resizable="true"></table>
        </div>
    </div>
</div>

<th:block th:include="include :: footer"/>
<th:block th:include="include :: select2-js" />
<th:block th:include="include :: layout-latest-js" />
<th:block th:include="include :: bootstrap-table-resizable-js" />
<script th:inline="javascript">
    let selectFlag = [[${@permission.hasPermi('people:people:list:list')}]]
    let editFlag = [[${@permission.hasPermi('people:people:edit')}]];
    let removeFlag = [[${@permission.hasPermi('people:people:remove')}]];
    let countryDatas = [[${@dict.getType('sys_country')}]];
    let genderDatas = [[${@dict.getType('sys_user_sex')}]];
    let people = ctx + "people/people";
    let prefix = ctx + "people/" + [[${url}]];
    let category = [[${category}]]
    $(function() {
        let options = {
            id: "bootstrap-table-people",          // 指定表格ID
            toolbar: "toolbar-people",   // 指定工具栏ID
            formId: "form-people",
            url: people + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: people + "/removeByPeopleCode",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            sortName: "orderNum",
            sortOrder: "Asc",
            modalName: category,
            rememberSelected: true,//翻页记住选择
            uniqueId: "peopleCode",
            columns: [{
                field: 'state',
                checkbox: true
            },
                {
                    align: 'center',
                    title: "序号",
                    width:60,
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    align: 'center',
                    field: 'peopleCode',
                    title: '人员编码',
                    width:120,
                },
                {
                    align: 'center',
                    field: 'avatar',
                    title: '头像',
                    width:80,
                    formatter: function(value, row, index) {
                        if (value != null && value != ''){
                            return $.table.imageView(value, 300 , 300);
                        }else{
                            return $.table.imageView('/img/default_people.png', 300 , 300);
                        }
                    }
                },
                {
                    field: 'nameCn',
                    title: '中文名称',
                    width:190
                },
                {
                    field: 'nameEn',
                    title: '英文名称',
                    width:250
                },
                {
                    align: 'center',
                    field: 'gender',
                    title: '性别',
                    width:60,
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(genderDatas, value);
                    }
                },
                {
                    align: 'center',
                    field: 'country',
                    title: '国家/地区',
                    width:120,
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(countryDatas, value);
                    }
                },
                {
                    field: 'source',
                    title: '数据来源',
                    formatter: function (value, row, index) {
                        if (value != '' && value != null) {
                            let html = "";
                            let sourceArr = value.split(';');
                            if (sourceArr != null && sourceArr.length > 0){
                                let urlReg = /[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?/;
                                for (let i = 0; i < sourceArr.length; i++) {
                                    let source = sourceArr[i];
                                    let domainName = urlReg.exec(source);//根据正则取出网址域名
                                    if (domainName != null && domainName != '') {
                                        if (i > 0){
                                            html+= '</br>'
                                        }
                                        html+= "<a target='_blank' href='" + source + "' title='" + source + "'>" + (domainName[0] != null && domainName[0] != '' ? domainName[0] : source) + "</a>";
                                    }
                                }
                                if (html != null && html != ''){
                                    return html;
                                }
                            }
                        }
                        return $.table.tooltip(value, 8);
                    }
                },
                {
                    field: 'updateBy',
                    title: '更新用户'
                },
                {
                    field: 'updateTime',
                    title: '更新时间'
                },
                {
                    field: 'createBy',
                    title: '创建用户'
                },
                {
                    field: 'createTime',
                    title: '创建时间',
                    formatter: function (value, row, index) {
                        let date = value.split(' ');
                        return (date != null && date.length > 0) ? date[0] : value
                    }
                },
                {
                    width:240,
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        let actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.peopleCode + '\')"><i class="fa fa-edit"></i>修改</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.peopleCode + '\')"><i class="fa fa-remove"></i>删除</a>');
/*
                        actions.push('<a class="btn btn-primary btn-xs" href="javascript:void(0)" onclick="submitPublishPeople(\'' + row.peopleCode +'\')"><i class="fa fa-plus"></i>提交发布</a> ');
*/
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    //提交发布
    function submitPublishPeople(peopleCode) {
        let peopleCodes;
        if (peopleCode !== null && peopleCode !== '' && peopleCode !== undefined){
            peopleCodes = peopleCode;
        } else {
            peopleCodes = $.table.selectColumns("peopleCode").toString();
        }
        $.ajax({
            type: "GET",
            url: people + "/submitPublishPeople/" + peopleCodes,
            async: false,
            success: function(result) {
                if (result.code == web_status.SUCCESS) {
                    $.form.reset('form-people', 'bootstrap-table-people')
                    $.modal.msgSuccess("发布成功！");
                } else {
                    $.modal.alertError(result.msg);
                }
            }
        });
    }

    //Excel导入
    function importExcel() {
        let options = {
            title: category + '导入',
            width: "1200",
            height: "700",
            url: people + "/importExcel/" + [[${url}]],
            btn: 0,
            callBack: doSubmit
        };
        $.modal.openOptions(options);
    }

    function doSubmit(index, layero) {
        location.reload();
    }

    //导出
    function exportExcel() {
        let peopleCodes = $.table.selectColumns("peopleCode");
        $.modal.loading("正在导出数据，请稍后...");
        $.ajax({
            type: "POST",
            url: prefix + "/exportExcel",
            data: {
                'peopleCodes':  peopleCodes.toString()
            },
            success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                    $.modal.msgSuccess('导出成功！');
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            }
        });
    }
</script>
</body>
</html>
