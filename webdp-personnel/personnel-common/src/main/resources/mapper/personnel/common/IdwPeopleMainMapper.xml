<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lirong.personnel.common.mapper.IdwPeopleMainMapper">
    
    <resultMap type="com.lirong.personnel.common.domain.IdwPeopleMain" id="IdwPeopleMainResult">
        <result property="peopleId"    column="people_id"    />
        <result property="peopleCode"    column="people_code"    />
        <result property="country"    column="country"    />
        <result property="peopleType"    column="people_type"    />
        <result property="category"    column="category"    />
        <result property="nameCn"    column="name_cn"    />
        <result property="nameEn"    column="name_en"    />
        <result property="status"    column="status"    />
        <result property="formerName"    column="former_name"    />
        <result property="gender"    column="gender"    />
        <result property="certificateType"    column="certificate_type"    />
        <result property="idNumber"    column="id_number"    />
        <result property="homePhone"    column="home_phone"    />
        <result property="officePhone"    column="office_phone"    />
        <result property="faxNumber"    column="fax_number"    />
        <result property="address"    column="address"    />
        <result property="zipCode"    column="zip_code"    />
        <result property="nationality"    column="nationality"    />
        <result property="nativePlace"    column="native_place"    />
        <result property="birthplace"    column="birthplace"    />
        <result property="birthday"    column="birthday"    />
        <result property="workingDate"    column="working_date"    />
        <result property="age"    column="age"    />
        <result property="avatar"    column="avatar"    />
        <result property="email"    column="email"    />
        <result property="telephone"    column="telephone"    />
        <result property="graduatedUniversity"    column="graduated_university"    />
        <result property="degree"    column="degree"    />
        <result property="education"    column="education"    />
        <result property="inServiceEduUniversity"    column="in_service_edu_university"    />
        <result property="inServiceEduEducation"    column="in_service_edu_education"    />
        <result property="inServiceEduDegree"    column="in_service_edu_degree"    />
        <result property="orgCode"    column="org_code"    />
        <result property="orgName"    column="org_name"    />
        <result property="post"    column="post"    />
        <result property="appointmentDate"    column="appointment_date"    />
        <result property="occupation"    column="occupation"    />
        <result property="workplace"    column="workplace"    />
        <result property="cppccPosts"    column="cppcc_posts"    />
        <result property="mainSocialPost"    column="main_social_post"    />
        <result property="technicalPost"    column="technical_post"    />
        <result property="participantOrg"    column="participant_org"    />
        <result property="innerCode"    column="inner_code"    />
        <result property="party"    column="party"    />
        <result property="joinPartyDate"    column="join_party_date"    />
        <result property="secondParty"    column="second_party"    />
        <result property="joinSecondPartyDate"    column="join_second_party_date"    />
        <result property="politicalPerformance"    column="political_performance"    />
        <result property="troopsCategory"    column="troops_category"    />
        <result property="militaryRank"    column="military_rank"    />
        <result property="profileCn"    column="profile_cn"    />
        <result property="profileEn"    column="profile_en"    />
        <result property="educationalExperience"    column="educational_experience"    />
        <result property="assignments"    column="assignments"    />
        <result property="promotion"    column="promotion"    />
        <result property="peopleCharacter"    column="people_character"    />
        <result property="hobby"    column="hobby"    />
        <result property="strengthsWeaknesses"    column="strengths_weaknesses"    />
        <result property="technicalExpertise"    column="technical_expertise"    />
        <result property="achievement"    column="achievement"    />
        <result property="socialInfluence"    column="social_influence"    />
        <result property="identityCategory"    column="identity_category"    />
        <result property="level1213"    column="level_1213"    />
        <result property="rewardsPunishments"    column="rewards_punishments"    />
        <result property="race"    column="race"    />
        <result property="religiousBelief"    column="religious_belief"    />
        <result property="physicalCondition"    column="physical_condition"    />
        <result property="policyProposition"    column="policy_proposition"    />
        <result property="politicalOrientation"    column="political_orientation"    />
        <result property="politicalFaction"    column="political_faction"    />
        <result property="attitudeTowardsChina"    column="attitude_towards_china"    />
        <result property="extraversion"    column="extraversion"    />
        <result property="emotionalStability"    column="emotional_stability"    />
        <result property="agreeableness"    column="agreeableness"    />
        <result property="conscientiousness"    column="conscientiousness"    />
        <result property="openness"    column="openness"    />
        <result property="showHome"    column="show_home"    />
        <result property="orderNum"    column="order_num"    />
        <result property="tags"    column="tags"    />
        <result property="source"    column="source"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="com.lirong.personnel.common.vo.SimplePeople" id="SimplePeople">
        <result property="peopleCode"    column="people_code"    />
        <result property="category"    column="category"    />
        <result property="nameCn"    column="name_cn"    />
        <result property="nameEn"    column="name_en"    />
        <result property="avatar"    column="avatar"    />
        <result property="orgName"    column="org_name"    />
        <result property="post"    column="post"    />
        <result property="profileCn"    column="profile_cn"    />
        <result property="profileEn"    column="profile_en"    />
    </resultMap>

    <select id="selectIdwPeopleMainList" parameterType="com.lirong.personnel.common.domain.IdwPeopleMain" resultMap="IdwPeopleMainResult">
        SELECT
            people.people_id, people.people_code, people.country, people.avatar,
            people.name_cn, people.name_en, people.former_name, people.gender,
            people.nationality, people.attitude_towards_china, people.political_orientation, people.source,
            people.create_by, people.create_time, people.update_by, people.update_time
        FROM
            idw_people_main people
        WHERE
            people.is_delete = 0
        <if test="peopleCode != null  and peopleCode != ''"> and people.people_code like concat('%', #{peopleCode}, '%')</if>
        <if test="idNumber != null  and idNumber != ''"> and people.id_number like concat('%', #{idNumber}, '%')</if>
        <if test="orgName != null  and orgName != ''"> and people.org_name like concat('%', #{orgName}, '%')</if>
        <if test="country != null  and country != ''"> and people.country = #{country}</if>
        <if test="peopleType != null  and peopleType != ''"> and people.people_type = #{peopleType}</if>
        <if test="category != null  and category != ''"> and people.category = #{category}</if>
        <if test="nameCn != null  and nameCn != ''"> and (
                people.name_cn like concat('%', #{nameCn}, '%') or people.name_en like concat('%', #{nameCn}, '%')
            )
        </if>
        <if test="nameEn != null  and nameEn != ''"> and (
                people.name_cn like concat('%', #{nameEn}, '%') or people.name_en like concat('%', #{nameEn}, '%')
            )
        </if>
         order by people.order_num
    </select>

    <!--根据人员编码查询-->
    <select id="selectPeopleByPeopleCode" resultMap="IdwPeopleMainResult">
        SELECT
            *
        FROM
            idw_people_main
        WHERE
            is_delete = 0
            AND people_code = #{peopleCode}
    </select>

    <!--查询当前分类中最大的排序号-->
    <select id="selectMaxOrderNum" resultType="java.lang.Integer">
        SELECT
        IF
            ( MAX( order_num ) IS NULL, 0, MAX( order_num ) ) AS order_num
        FROM
            idw_people_main
        WHERE
            is_delete = 0
            AND category = #{category}
    </select>

    <!--获取当前最大的人员编码-->
    <select id="selectMaxPeopleCode" resultType="java.lang.String">
        SELECT
        IF
            ( MAX( people_code ) IS NOT NULL AND MAX( people_code ) != '', MAX( people_code ), 'P0000000' ) AS people_code
        FROM
            idw_people_main
    </select>

    <!--根据关键字查询人员信息-->
    <select id="selectPeopleByKeyword"  resultMap="SimplePeople">
        SELECT
            people_code,
            category,
            name_cn,
            name_en,
            birthday,
            avatar,
            org_name,
            post,
            profile_cn,
            profile_en
        FROM
            idw_people_main
        WHERE
            is_delete = 0
            AND (
                name_cn LIKE CONCAT( '%', #{keyword}, '%' )
                OR name_en LIKE CONCAT( '%', #{keyword}, '%' )
                OR people_code LIKE CONCAT( '%', #{keyword}, '%' )
            )
        ORDER BY
            category DESC
            LIMIT 20
    </select>

    <!--人员查询 (返回简易对象)-->
    <select id="selectSimplePeopleList"  resultMap="SimplePeople">
          SELECT
            people_code,
            category,
            name_cn,
            name_en,
            avatar,
            org_name,
            post,
            profile_cn,
            profile_en
        FROM
            idw_people_main
        <where>
            is_delete = 0
            <if test="nameCn != null  and nameCn != ''"> and (
            name_cn like concat('%', #{nameCn}, '%') or name_en like concat('%', #{nameCn}, '%')
            )</if>
            <if test="orgName != null  and orgName != ''"> and org_name like concat('%', #{orgName}, '%')</if>
        </where>
    </select>

    <!--根据文件名称查询 Excel导入时将带后缀的头像名称赋值到 avatar 中-->
    <select id="selectCountByAvatar" resultType="java.lang.Integer">
        SELECT
            COUNT( * ) AS count
        FROM
            idw_people_main
        WHERE
            is_delete = 0
            AND avatar = #{fileName}
    </select>

    <!--根据条件查询人员总数-->
    <select id="selectCountExcludeCreateUser" resultType="java.lang.Long">
        SELECT
            COUNT( * ) AS count
        FROM
            idw_people_main
        WHERE
            is_delete = 0
        <if test="createUserNotIs != null and createUserNotIs.length > 0">
            AND create_by NOT IN
            <foreach item="createUser" collection="createUserNotIs" open="(" separator="," close=")">
                #{createUser}
            </foreach>
        </if>
    </select>

    <!--根据人员类型统计-->
    <select id="loadPeopleStatisticsByCategoryExcludeCreateUser" resultType="com.lirong.common.vo.StatisticsVO">
        SELECT
            category AS name,
            COUNT( * ) AS value
        FROM
            idw_people_main
        WHERE
            is_delete = 0
        <if test="createUserNotIs != null and createUserNotIs.length > 0">
            AND create_by NOT IN
            <foreach item="createUser" collection="createUserNotIs" open="(" separator="," close=")">
                #{createUser}
            </foreach>
        </if>
        GROUP BY
            category
    </select>

    <!--查询所有人员类型-->
    <select id="selectCategoryExcludeCreateUser" resultType="java.lang.String">
        SELECT
            category
        FROM
            idw_people_main
        WHERE
            is_delete = 0
        <if test="createUserNotIs != null and createUserNotIs.length > 0">
            AND create_by NOT IN
            <foreach item="createUser" collection="createUserNotIs" open="(" separator="," close=")">
                #{createUser}
            </foreach>
        </if>
        GROUP BY
            category
    </select>

    <sql id="dmLoadTrendExcludeCreateUser">
        <if test="createUserNotIs != null and createUserNotIs.length > 0">
            AND create_by NOT IN
            <foreach item="createUser" collection="createUserNotIs" open="(" separator="," close=")">
                #{createUser}
            </foreach>
        </if>
        group by
            trunc(create_time, 'iw'),
            DATE_FORMAT( create_time, '%Y-%u' )
    </sql>

    <sql id="mySqlLoadTrendExcludeCreateUser">
        AND DATE_SUB( CURDATE( ), INTERVAL 365 DAY ) &lt;= date( create_time )
        <if test="createUserNotIs != null and createUserNotIs.length > 0">
            AND create_by NOT IN
            <foreach item="createUser" collection="createUserNotIs" open="(" separator="," close=")">
                #{createUser}
            </foreach>
        </if>
        GROUP BY
            DATE_FORMAT( create_time, '%Y-%u' )
    </sql>

    <!--根据人员类型与创建时间构建趋势图(按周统计)-->
    <select id="loadTrendExcludeCreateUser" resultType="com.lirong.common.vo.StatisticsVO">
        SELECT
            DATE_FORMAT( create_time, '%Y-%u' ) AS name,
            COUNT( * ) AS value
        FROM
            idw_people_main
        WHERE
            is_delete = 0
            AND category = #{category}
        <if test="databaseType != null and databaseType == 'DM DBMS'">
            <include refid="dmLoadTrendExcludeCreateUser"/>
        </if>
        <if test="databaseType != null and databaseType == 'MySQL'">
            <include refid="mySqlLoadTrendExcludeCreateUser"/>
        </if>
    </select>

    <!--获取文件路径-->
    <select id="selectAllFilePath" resultType="java.lang.String">
        SELECT
            avatar
        FROM
            idw_people_main
        WHERE
            is_delete = 0
            AND avatar IS NOT NULL
            AND avatar != ''
    </select>

    <!--根据人员名称查询-->
    <select id="selectByPeopleName" resultMap="IdwPeopleMainResult">
        SELECT
            *
        FROM
            idw_people_main
        WHERE
            is_delete = 0
        <if test="nameCn != null and nameCn != ''">
            AND ( name_cn = #{nameCn} OR name_en = #{nameCn} )
        </if>
        <if test="nameEn != null and nameEn != ''">
            AND ( name_cn = #{nameEn} OR name_en = #{nameEn} )
        </if>
    </select>

    <!--根据人员编码集合查询-->
    <select id="selectByPeopleCodes" resultMap="IdwPeopleMainResult">
        SELECT
            *
        FROM
            idw_people_main
        WHERE
            is_delete = 0
            AND people_code  in
        <foreach item="peopleCode" collection="peopleCodes" open="(" separator="," close=")">
            #{peopleCode}
        </foreach>
    </select>

    <insert id="insertIdwPeopleMain" parameterType="com.lirong.personnel.common.domain.IdwPeopleMain" useGeneratedKeys="true" keyProperty="peopleId">
        insert into idw_people_main
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="peopleCode != null and peopleCode != ''">people_code,</if>
            <if test="country != null and country != ''">country,</if>
            <if test="peopleType != null">people_type,</if>
            <if test="category != null">category,</if>
            <if test="nameCn != null and nameCn != ''">name_cn,</if>
            <if test="nameEn != null">name_en,</if>
            <if test="status != null">status,</if>
            <if test="formerName != null">former_name,</if>
            <if test="gender != null and gender != ''">gender,</if>
            <if test="certificateType != null">certificate_type,</if>
            <if test="idNumber != null">id_number,</if>
            <if test="homePhone != null">home_phone,</if>
            <if test="officePhone != null">office_phone,</if>
            <if test="faxNumber != null">fax_number,</if>
            <if test="address != null">address,</if>
            <if test="zipCode != null">zip_code,</if>
            <if test="nationality != null">nationality,</if>
            <if test="nativePlace != null">native_place,</if>
            <if test="birthplace != null">birthplace,</if>
            <if test="birthday != null">birthday,</if>
            <if test="workingDate != null">working_date,</if>
            <if test="age != null">age,</if>
            <if test="avatar != null">avatar,</if>
            <if test="email != null">email,</if>
            <if test="telephone != null">telephone,</if>
            <if test="graduatedUniversity != null">graduated_university,</if>
            <if test="degree != null">degree,</if>
            <if test="education != null">education,</if>
            <if test="inServiceEduUniversity != null">in_service_edu_university,</if>
            <if test="inServiceEduEducation != null">in_service_edu_education,</if>
            <if test="inServiceEduDegree != null">in_service_edu_degree,</if>
            <if test="orgCode != null">org_code,</if>
            <if test="orgName != null">org_name,</if>
            <if test="post != null">post,</if>
            <if test="appointmentDate != null">appointment_date,</if>
            <if test="occupation != null">occupation,</if>
            <if test="workplace != null">workplace,</if>
            <if test="cppccPosts != null">cppcc_posts,</if>
            <if test="mainSocialPost != null">main_social_post,</if>
            <if test="technicalPost != null">technical_post,</if>
            <if test="participantOrg != null">participant_org,</if>
            <if test="innerCode != null">inner_code,</if>
            <if test="party != null">party,</if>
            <if test="joinPartyDate != null">join_party_date,</if>
            <if test="secondParty != null">second_party,</if>
            <if test="joinSecondPartyDate != null">join_second_party_date,</if>
            <if test="politicalPerformance != null">political_performance,</if>
            <if test="troopsCategory != null">troops_category,</if>
            <if test="militaryRank != null">military_rank,</if>
            <if test="profileCn != null">profile_cn,</if>
            <if test="profileEn != null">profile_en,</if>
            <if test="educationalExperience != null">educational_experience,</if>
            <if test="assignments != null">assignments,</if>
            <if test="promotion != null">promotion,</if>
            <if test="peopleCharacter != null">people_character,</if>
            <if test="hobby != null">hobby,</if>
            <if test="strengthsWeaknesses != null">strengths_weaknesses,</if>
            <if test="technicalExpertise != null">technical_expertise,</if>
            <if test="achievement != null">achievement,</if>
            <if test="socialInfluence != null">social_influence,</if>
            <if test="identityCategory != null">identity_category,</if>
            <if test="level1213 != null">level_1213,</if>
            <if test="rewardsPunishments != null">rewards_punishments,</if>
            <if test="race != null">race,</if>
            <if test="religiousBelief != null">religious_belief,</if>
            <if test="physicalCondition != null">physical_condition,</if>
            <if test="policyProposition != null">policy_proposition,</if>
            <if test="politicalOrientation != null">political_orientation,</if>
            <if test="politicalFaction != null">political_faction,</if>
            <if test="attitudeTowardsChina != null">attitude_towards_china,</if>
            <if test="extraversion != null">extraversion,</if>
            <if test="emotionalStability != null">emotional_stability,</if>
            <if test="agreeableness != null">agreeableness,</if>
            <if test="conscientiousness != null">conscientiousness,</if>
            <if test="openness != null">openness,</if>
            <if test="showHome != null">show_home,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="tags != null">tags,</if>
            <if test="source != null">source,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            is_delete
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="peopleCode != null and peopleCode != ''">#{peopleCode},</if>
            <if test="country != null and country != ''">#{country},</if>
            <if test="peopleType != null">#{peopleType},</if>
            <if test="category != null">#{category},</if>
            <if test="nameCn != null and nameCn != ''">#{nameCn},</if>
            <if test="nameEn != null">#{nameEn},</if>
            <if test="status != null">#{status},</if>
            <if test="formerName != null">#{formerName},</if>
            <if test="gender != null and gender != ''">#{gender},</if>
            <if test="certificateType != null">#{certificateType},</if>
            <if test="idNumber != null">#{idNumber},</if>
            <if test="homePhone != null">#{homePhone},</if>
            <if test="officePhone != null">#{officePhone},</if>
            <if test="faxNumber != null">#{faxNumber},</if>
            <if test="address != null">#{address},</if>
            <if test="zipCode != null">#{zipCode},</if>
            <if test="nationality != null">#{nationality},</if>
            <if test="nativePlace != null">#{nativePlace},</if>
            <if test="birthplace != null">#{birthplace},</if>
            <if test="birthday != null">#{birthday},</if>
            <if test="workingDate != null">#{workingDate},</if>
            <if test="age != null">#{age},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="email != null">#{email},</if>
            <if test="telephone != null">#{telephone},</if>
            <if test="graduatedUniversity != null">#{graduatedUniversity},</if>
            <if test="degree != null">#{degree},</if>
            <if test="education != null">#{education},</if>
            <if test="inServiceEduUniversity != null">#{inServiceEduUniversity},</if>
            <if test="inServiceEduEducation != null">#{inServiceEduEducation},</if>
            <if test="inServiceEduDegree != null">#{inServiceEduDegree},</if>
            <if test="orgCode != null">#{orgCode},</if>
            <if test="orgName != null">#{orgName},</if>
            <if test="post != null">#{post},</if>
            <if test="appointmentDate != null">#{appointmentDate},</if>
            <if test="occupation != null">#{occupation},</if>
            <if test="workplace != null">#{workplace},</if>
            <if test="cppccPosts != null">#{cppccPosts},</if>
            <if test="mainSocialPost != null">#{mainSocialPost},</if>
            <if test="technicalPost != null">#{technicalPost},</if>
            <if test="participantOrg != null">#{participantOrg},</if>
            <if test="innerCode != null">#{innerCode},</if>
            <if test="party != null">#{party},</if>
            <if test="joinPartyDate != null">#{joinPartyDate},</if>
            <if test="secondParty != null">#{secondParty},</if>
            <if test="joinSecondPartyDate != null">#{joinSecondPartyDate},</if>
            <if test="politicalPerformance != null">#{politicalPerformance},</if>
            <if test="troopsCategory != null">#{troopsCategory},</if>
            <if test="militaryRank != null">#{militaryRank},</if>
            <if test="profileCn != null">#{profileCn},</if>
            <if test="profileEn != null">#{profileEn},</if>
            <if test="educationalExperience != null">#{educationalExperience},</if>
            <if test="assignments != null">#{assignments},</if>
            <if test="promotion != null">#{promotion},</if>
            <if test="peopleCharacter != null">#{peopleCharacter},</if>
            <if test="hobby != null">#{hobby},</if>
            <if test="strengthsWeaknesses != null">#{strengthsWeaknesses},</if>
            <if test="technicalExpertise != null">#{technicalExpertise},</if>
            <if test="achievement != null">#{achievement},</if>
            <if test="socialInfluence != null">#{socialInfluence},</if>
            <if test="identityCategory != null">#{identityCategory},</if>
            <if test="level1213 != null">#{level1213},</if>
            <if test="rewardsPunishments != null">#{rewardsPunishments},</if>
            <if test="race != null">#{race},</if>
            <if test="religiousBelief != null">#{religiousBelief},</if>
            <if test="physicalCondition != null">#{physicalCondition},</if>
            <if test="policyProposition != null">#{policyProposition},</if>
            <if test="politicalOrientation != null">#{politicalOrientation},</if>
            <if test="politicalFaction != null">#{politicalFaction},</if>
            <if test="attitudeTowardsChina != null">#{attitudeTowardsChina},</if>
            <if test="extraversion != null">#{extraversion},</if>
            <if test="emotionalStability != null">#{emotionalStability},</if>
            <if test="agreeableness != null">#{agreeableness},</if>
            <if test="conscientiousness != null">#{conscientiousness},</if>
            <if test="openness != null">#{openness},</if>
            <if test="showHome != null">#{showHome},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="tags != null">#{tags},</if>
            <if test="source != null">#{source},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            0
        </trim>
    </insert>

    <update id="updatePeople" parameterType="com.lirong.personnel.common.domain.IdwPeopleMain">
        update idw_people_main
        <trim prefix="SET" suffixOverrides=",">
            <if test="peopleCode != null">people_code = #{peopleCode},</if>
            <if test="country != null">country = #{country},</if>
            <if test="peopleType != null">people_type = #{peopleType},</if>
            <if test="category != null">category = #{category},</if>
            <if test="nameCn != null">name_cn = #{nameCn},</if>
            <if test="nameEn != null">name_en = #{nameEn},</if>
            <if test="status != null">status = #{status},</if>
            <if test="formerName != null">former_name = #{formerName},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="certificateType != null">certificate_type = #{certificateType},</if>
            <if test="idNumber != null">id_number = #{idNumber},</if>
            <if test="homePhone != null">home_phone = #{homePhone},</if>
            <if test="officePhone != null">office_phone = #{officePhone},</if>
            <if test="faxNumber != null">fax_number = #{faxNumber},</if>
            <if test="address != null">address = #{address},</if>
            <if test="zipCode != null">zip_code = #{zipCode},</if>
            <if test="nationality != null">nationality = #{nationality},</if>
            <if test="nativePlace != null">native_place = #{nativePlace},</if>
            <if test="birthplace != null">birthplace = #{birthplace},</if>
            <if test="birthday != null">birthday = #{birthday},</if>
            <if test="workingDate != null">working_date = #{workingDate},</if>
            <if test="age != null">age = #{age},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="email != null">email = #{email},</if>
            <if test="telephone != null">telephone = #{telephone},</if>
            <if test="graduatedUniversity != null">graduated_university = #{graduatedUniversity},</if>
            <if test="degree != null">degree = #{degree},</if>
            <if test="education != null">education = #{education},</if>
            <if test="inServiceEduUniversity != null">in_service_edu_university = #{inServiceEduUniversity},</if>
            <if test="inServiceEduEducation != null">in_service_edu_education = #{inServiceEduEducation},</if>
            <if test="inServiceEduDegree != null">in_service_edu_degree = #{inServiceEduDegree},</if>
            <if test="orgCode != null">org_code = #{orgCode},</if>
            <if test="orgName != null">org_name = #{orgName},</if>
            <if test="post != null">post = #{post},</if>
            <if test="appointmentDate != null">appointment_date = #{appointmentDate},</if>
            <if test="occupation != null">occupation = #{occupation},</if>
            <if test="workplace != null">workplace = #{workplace},</if>
            <if test="cppccPosts != null">cppcc_posts = #{cppccPosts},</if>
            <if test="mainSocialPost != null">main_social_post = #{mainSocialPost},</if>
            <if test="technicalPost != null">technical_post = #{technicalPost},</if>
            <if test="participantOrg != null">participant_org = #{participantOrg},</if>
            <if test="innerCode != null">inner_code = #{innerCode},</if>
            <if test="party != null">party = #{party},</if>
            <if test="joinPartyDate != null">join_party_date = #{joinPartyDate},</if>
            <if test="secondParty != null">second_party = #{secondParty},</if>
            <if test="joinSecondPartyDate != null">join_second_party_date = #{joinSecondPartyDate},</if>
            <if test="politicalPerformance != null">political_performance = #{politicalPerformance},</if>
            <if test="troopsCategory != null">troops_category = #{troopsCategory},</if>
            <if test="militaryRank != null">military_rank = #{militaryRank},</if>
            <if test="profileCn != null">profile_cn = #{profileCn},</if>
            <if test="profileEn != null">profile_en = #{profileEn},</if>
            <if test="educationalExperience != null">educational_experience = #{educationalExperience},</if>
            <if test="assignments != null">assignments = #{assignments},</if>
            <if test="promotion != null">promotion = #{promotion},</if>
            <if test="peopleCharacter != null">people_character = #{peopleCharacter},</if>
            <if test="hobby != null">hobby = #{hobby},</if>
            <if test="strengthsWeaknesses != null">strengths_weaknesses = #{strengthsWeaknesses},</if>
            <if test="technicalExpertise != null">technical_expertise = #{technicalExpertise},</if>
            <if test="achievement != null">achievement = #{achievement},</if>
            <if test="socialInfluence != null">social_influence = #{socialInfluence},</if>
            <if test="identityCategory != null">identity_category = #{identityCategory},</if>
            <if test="level1213 != null">level_1213 = #{level1213},</if>
            <if test="rewardsPunishments != null">rewards_punishments = #{rewardsPunishments},</if>
            <if test="race != null">race = #{race},</if>
            <if test="religiousBelief != null">religious_belief = #{religiousBelief},</if>
            <if test="physicalCondition != null">physical_condition = #{physicalCondition},</if>
            <if test="policyProposition != null">policy_proposition = #{policyProposition},</if>
            <if test="politicalOrientation != null">political_orientation = #{politicalOrientation},</if>
            <if test="politicalFaction != null">political_faction = #{politicalFaction},</if>
            <if test="attitudeTowardsChina != null">attitude_towards_china = #{attitudeTowardsChina},</if>
            <if test="extraversion != null">extraversion = #{extraversion},</if>
            <if test="emotionalStability != null">emotional_stability = #{emotionalStability},</if>
            <if test="agreeableness != null">agreeableness = #{agreeableness},</if>
            <if test="conscientiousness != null">conscientiousness = #{conscientiousness},</if>
            <if test="openness != null">openness = #{openness},</if>
            <if test="showHome != null">show_home = #{showHome},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="source != null">source = #{source},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where people_id = #{peopleId}
    </update>

    <!--根据人员编码删除人员信息-->
    <update id="deleteByPeopleCodes">
        UPDATE idw_people_main
        SET update_by = #{loginName},
        update_time = sysdate(),
        is_delete = 1
        WHERE
            people_code in
        <foreach item="peopleCode" collection="peopleCodes" open="(" separator="," close=")">
            #{peopleCode}
        </foreach>
    </update>

    <!--根据人员编码更新人员相关模型数据-->
    <update id="updatePersonnelModelByPeopleCode">
        UPDATE idw_people_main
        SET update_by = #{updateBy},
        <if test="politicalOrientation != null and politicalOrientation != ''">
            political_orientation = #{politicalOrientation},
        </if>
        <if test="politicalFaction != null and politicalFaction != ''">
            political_faction = #{politicalFaction},
        </if>
        <if test="attitudeTowardsChina != null and attitudeTowardsChina != ''">
            attitude_towards_china = #{attitudeTowardsChina},
        </if>
        <if test="extraversion != null and extraversion != ''">
            extraversion = #{extraversion},
        </if>
        <if test="emotionalStability != null and emotionalStability != ''">
            emotional_stability = #{emotionalStability},
        </if>
        <if test="agreeableness != null and agreeableness != ''">
            agreeableness = #{agreeableness},
        </if>
        <if test="conscientiousness != null and conscientiousness != ''">
            conscientiousness = #{conscientiousness},
        </if>
        <if test="openness != null and openness != ''">
            openness = #{openness},
        </if>
        update_time = sysdate()
        WHERE
            people_code = #{peopleCode}
    </update>

    <!--根据文件名称修改-->
    <update id="updateAvatarByFileName">
        UPDATE idw_people_main
        SET update_by = #{userName},
         update_time = sysdate(),
         avatar = #{fileUrl}
        WHERE
            avatar = #{fileName}
    </update>

</mapper>