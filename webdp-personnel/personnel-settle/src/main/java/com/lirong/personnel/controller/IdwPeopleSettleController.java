package com.lirong.personnel.controller;

import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.lirong.common.annotation.Log;
import com.lirong.common.enums.BusinessType;
import com.lirong.personnel.domain.IdwPeopleSettle;
import com.lirong.personnel.service.IdwPeopleSettleService;
import com.lirong.common.core.controller.BaseController;
import com.lirong.common.core.domain.AjaxResult;
import com.lirong.common.utils.poi.ExcelUtil;
import com.lirong.common.core.page.TableDataInfo;

/**
 * 定居信息Controller
 *
 * <AUTHOR>
 * @date 2021-06-08
 */
@Controller
@RequestMapping("/people/settle")
public class IdwPeopleSettleController extends BaseController {
    private String prefix = "personnel/settle";

    @Autowired
    private IdwPeopleSettleService idwPeopleSettleService;

    @RequiresPermissions("people:settle:view")
    @GetMapping("/{peopleCode}")
    public String settle(@PathVariable("peopleCode") String peopleCode, ModelMap mmap) {
        mmap.put("peopleCode", peopleCode);
        return prefix + "/settle";
    }

    /**
     * 查询定居信息列表
     */
    @RequiresPermissions("people:settle:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(IdwPeopleSettle idwPeopleSettle) {
        startPage();
        List<IdwPeopleSettle> list = idwPeopleSettleService.selectIdwPeopleSettleList(idwPeopleSettle);
        return getDataTable(list);
    }

    /**
     * 新增定居信息
     */
    @GetMapping("/add/{peopleCode}")
    public String add(@PathVariable("peopleCode") String peopleCode, ModelMap mmap) {
        mmap.put("peopleCode", peopleCode);
        return prefix + "/add";
    }

    /**
     * 新增保存定居信息
     */
    @RequiresPermissions("people:settle:add")
    @Log(title = "人员定居信息", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(IdwPeopleSettle idwPeopleSettle) {
        return toAjax(idwPeopleSettleService.insertIdwPeopleSettle(idwPeopleSettle));
    }

    /**
     * 修改定居信息
     */
    @GetMapping("/edit/{settleId}")
    public String edit(@PathVariable("settleId") Long settleId, ModelMap mmap) {
        IdwPeopleSettle idwPeopleSettle = idwPeopleSettleService.selectIdwPeopleSettleById(settleId);
        mmap.put("idwPeopleSettle", idwPeopleSettle);
        return prefix + "/edit";
    }

    /**
     * 修改保存定居信息
     */
    @RequiresPermissions("people:settle:edit")
    @Log(title = "人员定居信息", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(IdwPeopleSettle idwPeopleSettle) {
        return toAjax(idwPeopleSettleService.updateIdwPeopleSettle(idwPeopleSettle));
    }

    /**
     * 删除定居信息
     */
    @RequiresPermissions("people:settle:remove")
    @Log(title = "人员定居信息", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(idwPeopleSettleService.deleteIdwPeopleSettleByIds(ids));
    }

    /**
     * 导出定居信息列表
     */
    @RequiresPermissions("people:settle:export")
    @Log(title = "人员定居信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(IdwPeopleSettle idwPeopleSettle) {
        List<IdwPeopleSettle> list = idwPeopleSettleService.selectIdwPeopleSettleList(idwPeopleSettle);
        ExcelUtil<IdwPeopleSettle> util = new ExcelUtil<IdwPeopleSettle>(IdwPeopleSettle.class);
        return util.exportExcel(list, "settle");
    }
}
