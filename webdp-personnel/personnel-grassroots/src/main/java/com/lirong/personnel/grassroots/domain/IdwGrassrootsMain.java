package com.lirong.personnel.grassroots.domain;

import com.lirong.common.annotation.Excel;
import com.lirong.common.core.domain.BaseEntity;

import com.lirong.common.utils.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;

/**
 * 基层人员对象 idw_people_main
 *
 * <AUTHOR>
 * @date 2020-12-28
 */
public class IdwGrassrootsMain extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 人员ID
     */
    private Long peopleId;

    /**
     * 人员编码
     */
    @Excel(name = "人员编码")
    private String peopleCode;

    /**
     * 国家
     */
    @Excel(name = "国家", dictType = "sys_country")
    private String country;

    /**
     * 人员所属类型
     */
    @Excel(name = "人员所属类型", dictType = "personnel_grassroots_type")
    private String peopleType;

    /**
     * 人员分类
     */
    private String category;

    /**
     * 中文名称
     */
    @Excel(name = "中文名称|中文译名")
    private String nameCn;

    /**
     * 英文名称
     */
    @Excel(name = "英文名称")
    private String nameEn;

    /**
     * 工作状态
     */
    @Excel(name = "工作状态", dictType = "sys_work_status", combo = {"现任", "离任"})
    private String status;

    /**
     * 性别
     */
    @Excel(name = "性别", dictType = "sys_user_sex", combo = {"男", "女", "未知"})
    private String gender;

    /**
     * 出生地
     */
    @Excel(name = "出生地")
    private String birthplace;

    /**
     * 出生日期
     */
    @Excel(name = "出生日期")
    private String birthday;

    private String birthdayYear;
    private String birthdayMonth;
    private String birthdayDay;

    /**
     * 年龄
     */
    @Excel(name = "年龄")
    private Integer age;

    /**
     * 头像
     */
    @Excel(name = "头像")
    private String avatar;

    /**
     * 邮箱
     */
    @Excel(name = "邮箱")
    private String email;

    /**
     * 联系方式
     */
    @Excel(name = "联系方式|手机号码")
    private String telephone;

    /**
     * 毕业院校
     */
    @Excel(name = "毕业院校")
    private String graduatedUniversity;

    /**
     * 学历
     */
    @Excel(name = "学历")
    private String education;

    /**
     * 学位
     */
    @Excel(name = "学位")
    private String degree;

    /**
     * 所在机构编码
     */
    @Excel(name = "所在机构编码")
    private String orgCode;

    /**
     * 所在机构名称
     */
    @Excel(name = "所在机构名称")
    private String orgName;

    /**
     * 当前职务/岗位
     */
    @Excel(name = "当前职务")
    private String post;

    /**
     * 任职日期
     */
    @Excel(name = "任职日期")
    private String appointmentDate;

    private String appointmentYear;
    private String appointmentMonth;
    private String appointmentDay;

    /**
     * 职业
     */
    @Excel(name = "职业")
    private String occupation;

    /**
     * 工作地点
     */
    @Excel(name = "工作地点")
    private String workplace;

    /**
     * 参与的组织
     */
    @Excel(name = "参与的组织")
    private String participantOrg;

    /**
     * 内网编码
     */
    @Excel(name = "内网编码")
    private String innerCode;

    /**
     * 中文简介
     */
    @Excel(name = "中文简介")
    private String profileCn;

    /**
     * 英文简介
     */
    @Excel(name = "英文简介")
    private String profileEn;

    /**
     * 性格特点/人员性格
     */
    @Excel(name = "性格特点")
    private String peopleCharacter;

    /**
     * 兴趣爱好
     */
    @Excel(name = "兴趣爱好")
    private String hobby;

    /**
     * 强项弱项/强弱项
     */
    @Excel(name = "强项弱项")
    private String strengthsWeaknesses;

    /**
     * 技术特长
     */
    @Excel(name = "技术特长")
    private String technicalExpertise;

    /**
     * 主要成就
     */
    @Excel(name = "主要成就")
    private String achievement;

    /**
     * 身体状况
     */
    @Excel(name = "身体状况")
    private String physicalCondition;

    /**
     * 大五人格-外倾性
     */
    private BigDecimal extraversion;

    /**
     * 大五人格-神经质性
     */
    private BigDecimal emotionalStability;

    /**
     * 政治倾向性
     */
    private String politicalOrientation;

    /**
     * 大五人格-宜人性
     */
    private BigDecimal agreeableness;

    /**
     * 大五人格-尽责性
     */
    private BigDecimal conscientiousness;

    /**
     * 大五人格-开放性
     */
    private BigDecimal openness;

    /**
     * 对华态度
     */
    private String attitudeTowardsChina;

    /**
     * 排序号
     */
    @Excel(name = "排序号")
    private Integer orderNum;

    /**
     * 标签
     */
    @Excel(name = "标签")
    private String tags;

    /**
     * 是否删除，0-未删除，1-已删除
     */
    private Integer isDelete;

    /**
     * 数据来源
     */
    @Excel(name = "数据来源")
    private String source;

    public void setPeopleId(Long peopleId) {
        this.peopleId = peopleId;
    }

    public Long getPeopleId() {
        return peopleId;
    }

    public void setPeopleCode(String peopleCode) {
        this.peopleCode = peopleCode;
    }

    public String getPeopleCode() {
        return peopleCode;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCountry() {
        return StringUtils.isNotBlank(country) ? country.trim() : country;
    }

    public String getPeopleType() {
        return peopleType;
    }

    public void setPeopleType(String peopleType) {
        this.peopleType = peopleType;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCategory() {
        return category;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getNameCn() {
        if (StringUtils.isNotBlank(nameCn)) {
            return nameCn.trim();
        } else {
            return nameCn;
        }
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getNameEn() {
        if (StringUtils.isNotBlank(nameEn)) {
            return nameEn.trim();
        } else {
            return nameEn;
        }
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getGender() {
        return gender;
    }

    public void setBirthplace(String birthplace) {
        this.birthplace = birthplace;
    }

    public String getBirthplace() {
        return birthplace;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getBirthday() {
        return birthday;
    }

    public String getBirthdayYear() {
        return birthdayYear;
    }

    public void setBirthdayYear(String birthdayYear) {
        this.birthdayYear = birthdayYear;
    }

    public String getBirthdayMonth() {
        return birthdayMonth;
    }

    public void setBirthdayMonth(String birthdayMonth) {
        this.birthdayMonth = birthdayMonth;
    }

    public String getBirthdayDay() {
        return birthdayDay;
    }

    public void setBirthdayDay(String birthdayDay) {
        this.birthdayDay = birthdayDay;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public Integer getAge() {
        return age;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getEmail() {
        return email;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setGraduatedUniversity(String graduatedUniversity) {
        this.graduatedUniversity = graduatedUniversity;
    }

    public String getGraduatedUniversity() {
        return graduatedUniversity;
    }

    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public void setDegree(String degree) {
        this.degree = degree;
    }

    public String getDegree() {
        return degree;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setPost(String post) {
        this.post = post;
    }

    public String getPost() {
        return post;
    }

    public void setAppointmentDate(String appointmentDate) {
        this.appointmentDate = appointmentDate;
    }

    public String getAppointmentDate() {
        return appointmentDate;
    }

    public String getAppointmentYear() {
        return appointmentYear;
    }

    public void setAppointmentYear(String appointmentYear) {
        this.appointmentYear = appointmentYear;
    }

    public String getAppointmentMonth() {
        return appointmentMonth;
    }

    public void setAppointmentMonth(String appointmentMonth) {
        this.appointmentMonth = appointmentMonth;
    }

    public String getAppointmentDay() {
        return appointmentDay;
    }

    public void setAppointmentDay(String appointmentDay) {
        this.appointmentDay = appointmentDay;
    }

    public void setOccupation(String occupation) {
        this.occupation = occupation;
    }

    public String getOccupation() {
        return occupation;
    }

    public void setWorkplace(String workplace) {
        this.workplace = workplace;
    }

    public String getWorkplace() {
        return workplace;
    }

    public void setParticipantOrg(String participantOrg) {
        this.participantOrg = participantOrg;
    }

    public String getParticipantOrg() {
        return participantOrg;
    }

    public void setInnerCode(String innerCode) {
        this.innerCode = innerCode;
    }

    public String getInnerCode() {
        return innerCode;
    }

    public void setProfileCn(String profileCn) {
        this.profileCn = profileCn;
    }

    public String getProfileCn() {
        return profileCn;
    }

    public void setProfileEn(String profileEn) {
        this.profileEn = profileEn;
    }

    public String getProfileEn() {
        return profileEn;
    }

    public void setPeopleCharacter(String peopleCharacter) {
        this.peopleCharacter = peopleCharacter;
    }

    public String getPeopleCharacter() {
        return peopleCharacter;
    }

    public void setHobby(String hobby) {
        this.hobby = hobby;
    }

    public String getHobby() {
        return hobby;
    }

    public void setStrengthsWeaknesses(String strengthsWeaknesses) {
        this.strengthsWeaknesses = strengthsWeaknesses;
    }

    public String getStrengthsWeaknesses() {
        return strengthsWeaknesses;
    }

    public void setTechnicalExpertise(String technicalExpertise) {
        this.technicalExpertise = technicalExpertise;
    }

    public String getTechnicalExpertise() {
        return technicalExpertise;
    }

    public String getAchievement() {
        return achievement;
    }

    public void setAchievement(String achievement) {
        this.achievement = achievement;
    }

    public void setPhysicalCondition(String physicalCondition) {
        this.physicalCondition = physicalCondition;
    }

    public String getPhysicalCondition() {
        return physicalCondition;
    }

    public void setExtraversion(BigDecimal extraversion) {
        this.extraversion = extraversion;
    }

    public BigDecimal getExtraversion() {
        return extraversion;
    }

    public void setEmotionalStability(BigDecimal emotionalStability) {
        this.emotionalStability = emotionalStability;
    }

    public BigDecimal getEmotionalStability() {
        return emotionalStability;
    }

    public void setPoliticalOrientation(String politicalOrientation) {
        this.politicalOrientation = politicalOrientation;
    }

    public String getPoliticalOrientation() {
        return politicalOrientation;
    }

    public void setAgreeableness(BigDecimal agreeableness) {
        this.agreeableness = agreeableness;
    }

    public BigDecimal getAgreeableness() {
        return agreeableness;
    }

    public void setConscientiousness(BigDecimal conscientiousness) {
        this.conscientiousness = conscientiousness;
    }

    public BigDecimal getConscientiousness() {
        return conscientiousness;
    }

    public void setOpenness(BigDecimal openness) {
        this.openness = openness;
    }

    public BigDecimal getOpenness() {
        return openness;
    }

    public void setAttitudeTowardsChina(String attitudeTowardsChina) {
        this.attitudeTowardsChina = attitudeTowardsChina;
    }

    public String getAttitudeTowardsChina() {
        return attitudeTowardsChina;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSource() {
        return StringUtils.isNotBlank(source) ? source.replaceAll("；", ";").replaceAll("(\r\n|\r|\n|\n\r)", ";") : source;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("peopleId", getPeopleId())
                .append("peopleCode", getPeopleCode())
                .append("country", getCountry())
                .append("peopleType", getPeopleType())
                .append("category", getCategory())
                .append("nameCn", getNameCn())
                .append("nameEn", getNameEn())
                .append("status", getStatus())
                .append("gender", getGender())
                .append("birthplace", getBirthplace())
                .append("birthday", getBirthday())
                .append("birthdayYear", getBirthdayYear())
                .append("birthdayMonth", getBirthdayMonth())
                .append("birthdayDay", getBirthdayDay())
                .append("age", getAge())
                .append("avatar", getAvatar())
                .append("email", getEmail())
                .append("telephone", getTelephone())
                .append("graduatedUniversity", getGraduatedUniversity())
                .append("education", getEducation())
                .append("degree", getDegree())
                .append("orgCode", getOrgCode())
                .append("orgName", getOrgName())
                .append("post", getPost())
                .append("appointmentDate", getAppointmentDate())
                .append("appointmentYear", getAppointmentYear())
                .append("appointmentMonth", getAppointmentMonth())
                .append("appointmentDay", getAppointmentDay())
                .append("occupation", getOccupation())
                .append("workplace", getWorkplace())
                .append("participantOrg", getParticipantOrg())
                .append("innerCode", getInnerCode())
                .append("profileCn", getProfileCn())
                .append("profileEn", getProfileEn())
                .append("peopleCharacter", getPeopleCharacter())
                .append("hobby", getHobby())
                .append("strengthsWeaknesses", getStrengthsWeaknesses())
                .append("technicalExpertise", getTechnicalExpertise())
                .append("achievement", getAchievement())
                .append("physicalCondition", getPhysicalCondition())
                .append("extraversion", getExtraversion())
                .append("emotionalStability", getEmotionalStability())
                .append("politicalOrientation", getPoliticalOrientation())
                .append("agreeableness", getAgreeableness())
                .append("conscientiousness", getConscientiousness())
                .append("openness", getOpenness())
                .append("attitudeTowardsChina", getAttitudeTowardsChina())
                .append("orderNum", getOrderNum())
                .append("tags", getTags())
                .append("isDelete", getIsDelete())
                .append("source", getSource())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
