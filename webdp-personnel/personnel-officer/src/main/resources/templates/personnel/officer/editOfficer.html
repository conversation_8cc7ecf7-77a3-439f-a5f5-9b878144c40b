
<head>
    <th:block th:include="include :: header('修改政府军队官员')" />
    <th:block th:include="include :: bootstrap-fileinput-css"/>
    <th:block th:include="include :: jasny-bootstrap-css" />
    <th:block th:include="include :: select2-css" />
    <th:block th:include="include :: bootstrap-tagsinput-css" />
    <style type="text/css">
        .bootstrap-tagsinput {
            width: 100%;
        }
        .label-info {
            background-color: #5bc0de;
        }
        .kv-avatar .krajee-default.file-preview-frame,.kv-avatar .krajee-default.file-preview-frame:hover {
            margin: 0;
            padding: 0;
            border: none;
            box-shadow: none;
            text-align: center;
        }
        .kv-avatar {
            display: inline-block;
        }
        .kv-avatar .file-input {
            display: table-cell;
            width: 213px;
        }
        .kv-reqd {
            color: red;
            font-family: monospace;
            font-weight: normal;
        }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-people-edit" th:object="${idwPeopleMain}">
            <input name="isSubmit" id="isSubmit" type="hidden"/>
            <input name="category" th:field="*{category}" type="hidden">
            <input name="peopleId" th:field="*{peopleId}" type="hidden"/>
            <div class="form-group" id="auditOpinionDiv" style="display: none;">
                <label class="col-sm-1 control-label is-required">审核意见：</label>
                <div class="col-sm-11">
                    <textarea id="auditOpinion" class="form-control" rows="4" readonly></textarea>
                </div>
            </div>
            <div class="row">
                <div class="col-sm-9">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">国家/地区：</label>
                                <div class="col-sm-8">
                                    <select name="country" id="country" class="form-control" th:with="type=${@dict.getType('sys_country')}" required>
                                        <option value="" style="color: #b6b6b6" disabled selected>选择国家/地区</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{country}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">中文名称：</label>
                                <div class="col-sm-8">
                                    <input name="nameCn" th:field="*{nameCn}" class="form-control" type="text" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">英文名称：</label>
                                <div class="col-sm-8">
                                    <input name="nameEn" th:field="*{nameEn}" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">性别：</label>
                                <div class="col-sm-8">
                                    <div class="radio-box" th:each="dict : ${@dict.getType('sys_user_sex')}">
                                        <input type="radio" th:id="${'gender_' + dict.dictCode}" name="gender" th:value="${dict.dictValue}" th:field="*{gender}" required>
                                        <label th:for="${'gender_' + dict.dictCode}" th:text="${dict.dictLabel}"></label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">人员编码：</label>
                                <div class="col-sm-8">
                                    <input name="peopleCode" id = "peopleCode" th:field="*{peopleCode}" class="form-control" type="text" readonly="readonly">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">人员所属类型：</label>
                                <div class="col-sm-8">
                                    <select name="peopleType" class="form-control" th:with="type=${@dict.getType('personnel_officer_type')}" required>
                                        <option value="" style="color: #b6b6b6" disabled selected>选择人员所属类型</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{peopleType}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">所属政党：</label>
                                <div class="col-sm-8">
                                    <input name="party" th:field="*{party}" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">所属军兵种：</label>
                                <div class="col-sm-8">
                                    <select name="troopsCategory" class="form-control" th:with="type=${@dict.getType('sys_troops_categories')}">
                                        <option value="" style="color: #b6b6b6" disabled selected>选择所属军兵种</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{troopsCategory}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">军衔：</label>
                                <div class="col-sm-8">
                                    <input name="militaryRank" th:field="*{militaryRank}" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">职业：</label>
                                <div class="col-sm-8">
                                    <input name="occupation" th:field="*{occupation}" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">所在机构：</label>
                                <div class="col-sm-8">
                                    <input name="orgName" th:field="*{orgName}" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">当前职务：</label>
                                <div class="col-sm-8">
                                    <input name="post" th:field="*{post}" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-sm-3">
                    <div class="form-group text-center">
                        <input type="hidden" name="avatar" id="avatar" th:field="*{avatar}">
                        <p class="user-info-head" onclick="peopleAvatar()">
                            <img class="img-lg" id="avatarUrl" th:src="*{avatar}" th:onerror="'this.src=\'' + @{'/img/default_people.png'} + '\''">
                        </p>
                        <p><input type="file" id="peopleAvatarInput" style="display: none;"><a onclick="peopleAvatar()"></a></p>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">任职日期：</label>
                        <div class="col-sm-8">
                            <div class="row">
                                <div class="col-sm-4">
                                    <select  name="appointmentYear" id="appointmentYear" class="form-control">
                                        <option value="">年份</option>
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select  name="appointmentMonth" id="appointmentMonth" class="form-control" disabled="disabled">
                                        <option value="">月份</option>
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="appointmentDay" id="appointmentDay" class="form-control" disabled="disabled">
                                        <option value="">日</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">工作地点：</label>
                        <div class="col-sm-8">
                            <input name="workplace" th:field="*{workplace}" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">出生地：</label>
                        <div class="col-sm-8">
                            <input name="birthplace" th:field="*{birthplace}" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group" id="nativePlaceHtml" style="display:none;">
                        <label class="col-sm-4 control-label">籍贯：</label>
                        <div class="col-sm-8">
                            <input name="nativePlace" id="nativePlace" th:field="*{nativePlace}" class="form-control" type="text">
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">出生日期：</label>
                        <div class="col-sm-8">
                            <div class="row">
                                <div class="col-sm-4">
                                    <select  name="birthdayYear" id="birthdayYear" class="form-control">
                                        <option value="">年份</option>
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select  name="birthdayMonth" id="birthdayMonth" class="form-control" disabled="disabled">
                                        <option value="">月份</option>
                                    </select>
                                </div>
                                <div class="col-sm-4">
                                    <select name="birthdayDay" id="birthdayDay" class="form-control" disabled="disabled">
                                        <option value="">日</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">年龄：</label>
                        <div class="col-sm-8">
                            <input name="age" id="age" th:field="*{age}" class="form-control" type="text" number="true">
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">邮箱：</label>
                        <div class="col-sm-8">
                            <input name="email" th:field="*{email}" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">联系方式：</label>
                        <div class="col-sm-8">
                            <input name="telephone" th:field="*{telephone}" class="form-control" type="text">
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">毕业院校：</label>
                        <div class="col-sm-8">
                            <input name="graduatedUniversity" th:field="*{graduatedUniversity}" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">学历：</label>
                        <div class="col-sm-8">
                            <input name="education" th:field="*{education}" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">学位：</label>
                        <div class="col-sm-8">
                            <input name="degree" th:field="*{degree}" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">政治派别：</label>
                        <div class="col-sm-8">
                            <select name="politicalFaction" class="form-control" th:with="type=${@dict.getType('sys_political_faction')}">
                                <option value="" style="color: #b6b6b6" disabled selected>选择政治派别</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{politicalFaction}"></option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">对华态度：</label>
                        <div class="col-sm-8">
                            <select name="attitudeTowardsChina" class="form-control" th:with="type=${@dict.getType('sys_attitude_towards_china')}">
                                <option value="" style="color: #b6b6b6" disabled selected>选择对华态度</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{attitudeTowardsChina}"></option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">政治倾向性：</label>
                        <div class="col-sm-8">
                            <select name="politicalOrientation" class="form-control" th:with="type=${@dict.getType('sys_political_orientation')}">
                                <option value="" style="color: #b6b6b6" disabled selected>选择政治倾向性</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{politicalOrientation}"></option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">政治主张：</label>
                        <div class="col-sm-8">
                            <input name="policyProposition"  th:field="*{policyProposition}" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">宗教信仰：</label>
                        <div class="col-sm-8">
                            <input name="religiousBelief"  th:field="*{religiousBelief}" class="form-control" type="text">
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">身体状况：</label>
                        <div class="col-sm-8">
                            <input name="physicalCondition"  th:field="*{physicalCondition}" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">工作状态：</label>
                        <div class="col-sm-8">
                            <select name="status" class="form-control" th:with="type=${@dict.getType('sys_work_status')}" required>
                                <option value="" style="color: #b6b6b6" disabled selected>选择工作状态</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{status}"></option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">是否显示前台：</label>
                        <div class="col-sm-8">
                            <div class="radio check-box">
                                <label><input type="radio" value="1" name="showHome" id="showHomeTrue"> <i></i> 是</label>
                            </div>
                            <div class="radio check-box">
                                <label><input type="radio" checked="" value="0" name="showHome" id="showHomeFalse"> <i></i>否</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">排序号：</label>
                        <div class="col-sm-8">
                            <input name="orderNum"  th:field="*{orderNum}" class="form-control" type="text" digits="true" required>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-1 control-label">标签：</label>
                <div class="col-sm-11">
                    <input name="tags" data-role="tagsinput" th:field="*{tags}" class="form-control" type="text">
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-1 control-label">中文简介：</label>
                <div class="col-sm-11">
                    <textarea name="profileCn" th:field="*{profileCn}"  class="form-control" rows="6"></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label">英文简介：</label>
                <div class="col-sm-11">
                    <textarea name="profileEn" th:field="*{profileEn}"  class="form-control" rows="6"></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label">教育经历：</label>
                <div class="col-sm-11">
                    <textarea name="educationalExperience" th:field="*{educationalExperience}"  class="form-control" rows="6"></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label">工作经历：</label>
                <div class="col-sm-11">
                    <textarea name="assignments" th:field="*{assignments}"  class="form-control" rows="6"></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label">荣誉奖项：</label>
                <div class="col-sm-11">
                    <textarea name="rewardsPunishments" th:field="*{rewardsPunishments}"  class="form-control" rows="6"></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label">晋升情况：</label>
                <div class="col-sm-11">
                    <textarea name="promotion" th:field="*{promotion}"  class="form-control" rows="6"></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label">参与的组织：</label>
                <div class="col-sm-11">
                    <textarea name="participantOrg" th:field="*{participantOrg}"  class="form-control" rows="6"></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label">技术特长：</label>
                <div class="col-sm-11">
                    <textarea name="technicalExpertise" th:field="*{technicalExpertise}" class="form-control" rows="6"></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label">性格特点：</label>
                <div class="col-sm-11">
                    <textarea name="peopleCharacter" th:field="*{peopleCharacter}"  class="form-control" rows="6"></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label">兴趣爱好：</label>
                <div class="col-sm-11">
                    <textarea name="hobby" th:field="*{hobby}" class="form-control" rows="6"></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label">主要成就：</label>
                <div class="col-sm-11">
                    <textarea name="achievement" th:field="*{achievement}" class="form-control" rows="6"></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label">强项弱项：</label>
                <div class="col-sm-11">
                    <textarea name="strengthsWeaknesses" th:field="*{strengthsWeaknesses}" class="form-control" rows="6"></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label is-required">数据来源：</label>
                <div class="col-sm-11">
                    <input name="source" th:field="*{source}" class="form-control" required>
                </div>
            </div>
        </form>
    </div>
    <div class="row">
        <div class="col-sm-offset-5 col-sm-10">
            <button type="button" class="btn btn-sm btn-primary" onclick="save(false)" id="save"><i class="fa fa-check"></i>保 存</button>&nbsp;
            <button type="button" class="btn btn-sm btn-primary" onclick="save(true)" id="saveAndSubmit"><i class="fa fa-check"></i>保存并提交审核</button>&nbsp;
            <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()" id="closeItem"><i class="fa fa-reply-all"></i>关 闭 </button>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js" />
    <th:block th:include="include :: jasny-bootstrap-js" />
    <th:block th:include="include :: bootstrap-fileinput-js"/>
    <th:block th:include="include :: bootstrap-tagsinput-js" />
    <script th:src="@{/ajax/libs/bootstrap-fileinput/fileinput.min.js}"></script>
    <script th:inline="javascript">
        var prefix = ctx +  "people/people";
        var isView = false;

        $("#form-people-edit").validate({
            focusCleanup: true
        });

        let gender = [[${idwPeopleMain.gender}]]
        if (gender == 0){
            $("#gender_1").iCheck('check');
        } else if (gender == 1){
            $("#gender_2").iCheck('check');
        } else {
            $("#gender_3").iCheck('check');
        }

        let showHome = [[${idwPeopleMain.showHome}]]
        if (showHome == 1){
            $("#showHomeFalse").iCheck('uncheck');
            $("#showHomeTrue").iCheck('check');
        }

        let auditOpinion = [[${auditOpinion}]]
        if (auditOpinion != null && auditOpinion != '' && auditOpinion != undefined){
            document.getElementById('auditOpinionDiv').style.display = ''
            document.getElementById('auditOpinion').innerText = auditOpinion
        }

        let auditEnabled = [[${auditEnabled}]]
        if (!auditEnabled){
            document.getElementById("saveAndSubmit").style.display="none";
        }

        if (isView){
            //隐藏提交按钮
            document.getElementById("save").style.display="none";
            document.getElementById("saveAndSubmit").style.display="none";
        }

        function save(isSubmit) {
            if ($.validate.form()) {
                $('#isSubmit').val(isSubmit)
                $.operate.saveTab(prefix + "/edit", $('#form-people-edit').serialize());
            }
        }

        var birthdayYear = document.getElementById("birthdayYear");
        var appointmentYear = document.getElementById("appointmentYear");
        var date = new Date();
        var year = date.getFullYear();
        //组建年份选择器
        for (var i = year; i >= year - 110; i--) {
            birthdayYear.options.add(new Option(i, i));
            appointmentYear.options.add(new Option(i, i));
        }

        //组建月份选择器
        var birthdayMonth = document.getElementById("birthdayMonth");
        var appointmentMonth = document.getElementById("appointmentMonth");
        for (var j = 1; j <= 12; j++) {
            if (j < 10){
                birthdayMonth.options.add(new Option('0'+j, '0'+j));
                appointmentMonth.options.add(new Option('0'+j, '0'+j));
            }else{
                birthdayMonth.options.add(new Option(j, j));
                appointmentMonth.options.add(new Option(j, j));
            }
        }
        //组建日选择器
        let birthdayDay = document.getElementById("birthdayDay");
        let appointmentDay = document.getElementById("appointmentDay");
        for (let j = 1; j <= 31; j++) {
            if (j < 10){
                birthdayDay.options.add(new Option('0'+j, '0'+j));
                appointmentDay.options.add(new Option('0'+j, '0'+j));
            }else{
                birthdayDay.options.add(new Option(j, j));
                appointmentDay.options.add(new Option(j, j));
            }
        }

        //赋值出生/任职年月日
        let birthdayYearValue = [[${idwPeopleMain.birthdayYear}]] ? [[${idwPeopleMain.birthdayYear}]] : ''
        $("#birthdayYear").val(birthdayYearValue);
        if (birthdayYearValue != null && birthdayYearValue != ''){
            $('#birthdayMonth').attr("disabled",false);
        }
        let birthdayMonthValue = [[${idwPeopleMain.birthdayMonth}]]
        $("#birthdayMonth").val(birthdayMonthValue);
        if (birthdayMonthValue != null && birthdayMonthValue != ''){
            $('#birthdayDay').attr("disabled",false);
        }
        let birthdayDayValue = [[${idwPeopleMain.birthdayDay}]]
        $("#birthdayDay").val(birthdayDayValue);
        let  appointmentYearValue= [[${idwPeopleMain.appointmentYear}]]
        $("#appointmentYear").val(appointmentYearValue);
        if (appointmentYearValue != null && appointmentYearValue != ''){
            $('#appointmentMonth').attr("disabled",false);
        }
        let  appointmentMonthValue = [[${idwPeopleMain.appointmentMonth}]]
        $("#appointmentMonth").val(appointmentMonthValue);
        if (appointmentMonthValue != null && appointmentMonthValue != ''){
            $('#appointmentDay').attr("disabled",false);
        }
        let  appointmentDayValue= [[${idwPeopleMain.appointmentDay}]]
        $("#appointmentDay").val(appointmentDayValue);

        $("#birthdayYear").change(function(){
            let birthday = $('#birthdayYear').select2('val');
            if (birthday != null && birthday != ''){
                $('#age').val(year - birthday);//计算年龄
            }else{
                $('#age').val('');
            }
            var birthdayYear = $('#birthdayYear option:selected') .val();
            if (birthdayYear != '' && birthdayYear != null){
                $('#birthdayMonth').attr("disabled",false);
            }else{
                $('#birthdayMonth').attr("disabled",true);
                $("#birthdayMonth").select2("val", [""]);
            }
        });

        $("#birthdayMonth").change(function(){
            let birthdayMonth = $('#birthdayMonth option:selected') .val();
            if (birthdayMonth != '' && birthdayMonth != null){
                $('#birthdayDay').attr("disabled",false);
            }else{
                $("#birthdayDay").select2("val", [""]);
                $('#birthdayDay').attr("disabled",true);
            }
        });

        $("#appointmentYear").change(function(){
            var appointmentYear = $('#appointmentYear option:selected') .val();
            if (appointmentYear != '' && appointmentYear != null){
                $('#appointmentMonth').attr("disabled",false);
            }else{
                $('#appointmentMonth').attr("disabled",true);
                $("#appointmentMonth").select2("val", [""]);
            }
        });

        $("#appointmentMonth").change(function(){
            let appointmentMonth = $('#appointmentMonth option:selected') .val();
            if (appointmentMonth != '' && appointmentMonth != null){
                $('#appointmentDay').attr("disabled",false);
            }else{
                $("#appointmentDay").select2("val", [""]);
                $('#appointmentDay').attr("disabled",true);
            }
        });

        // 上传人员头像
        function peopleAvatar() {
            if(!isView){
                $('#peopleAvatarInput').trigger('click');
            }
        }
        $("#peopleAvatarInput").change(function () {
            var data = new FormData();
            data.append("file", $("#peopleAvatarInput")[0].files[0]);
            $.ajax({
                type: "POST",
                url: ctx + "common/upload/img",
                data: data,
                cache: false,
                contentType: false,
                processData: false,
                dataType: 'json',
                success: function(result) {
                    if (result.code == web_status.SUCCESS) {
                        $("#avatarUrl").attr("src",result.url)
                        $("#avatar").val(result.url)
                    }
                },
                error: function(error) {
                    alert("图片上传失败。");
                }
            });
        });

        // country 当国家为台湾时 显示籍贯 nativePlaceHtml
        $("#country").change(function(){
            //要触发的事件
            let country =  $('#country').select2('val');  //选中的值
            if (country == 'CT' || country == 'MA' || country == 'HK'){
                document.getElementById("nativePlaceHtml").style.display="";//显示
            } else {
                document.getElementById("nativePlaceHtml").style.display="none";//隐藏
                $("#nativePlace").val('');
            }
        });

        //加载页面时判断是否显示籍贯
        let country = $("#country option:selected").val();
        if (country == 'CT' || country == 'MA' || country == 'HK'){
            document.getElementById("nativePlaceHtml").style.display="";//显示
        }

    </script>