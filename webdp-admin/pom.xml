<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>webdp</artifactId>
        <groupId>com.lirong</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>
    <artifactId>webdp-admin</artifactId>

    <description>
        web服务入口
    </description>

    <dependencies>

        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>3.14.8</version>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.9.0</version>
        </dependency>

        <!-- SpringBoot集成thymeleaf模板 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>

        <!-- spring-boot-devtools -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <optional>true</optional> <!-- 表示依赖不会传递 -->
        </dependency>

        <!-- swagger2-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
        </dependency>

        <!--防止进入swagger页面报类型转换错误，排除2.9.2中的引用，手动增加1.5.21版本-->
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>1.5.21</version>
        </dependency>

        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-models</artifactId>
            <version>1.5.21</version>
        </dependency>

        <!-- 达梦数据库 -->
        <dependency>
            <groupId>com.dm</groupId>
            <artifactId>Dm7JdbcDriver</artifactId>
            <version>1.8</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/lib/Dm7JdbcDriver18.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>com.dm.dialect</groupId>
            <artifactId>hibernate4</artifactId>
            <version>5.3</version>
            <scope>system</scope>
            <systemPath>${project.basedir}/src/lib/DmDialect-for-hibernate5.3.jar</systemPath>
        </dependency>

        <!-- swagger2-UI-->
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
        </dependency>

        <!-- Mysql驱动包 -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
        </dependency>

        <!-- 核心模块-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>webdp-framework</artifactId>
        </dependency>

        <!-- 定时任务-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>webdp-quartz</artifactId>
        </dependency>

        <!-- 代码生成-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>webdp-generator</artifactId>
        </dependency>

        <!--国防单位-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>organization-operational</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--政府机构-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>organization-administration</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--体系架构-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>webdp-system-architecture</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--国防企业-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>organization-enterprises</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--国防科研-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>organization-research</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--政府军队官员-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>personnel-officer</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--装备领域科学家-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>personnel-scientist</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--基层人员-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>personnel-grassroots</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--党外人士-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>personnel-non-party</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--资源库-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>webdp-resource</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--项目-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>project-main</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--合同信息-国防部-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>webdp-contract-dod</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--合同信息-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>webdp-contract</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- 武器装备 -->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>weaponry</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--装备主题库-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>weaponry-theme</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--事件管理-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>webdp-event</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--技术领域-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>webdp-technosphere</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--知识图谱-->
        <!--<dependency>
            <groupId>com.lirong</groupId>
            <artifactId>webdp-kg</artifactId>
            <version>1.0.0</version>
        </dependency>-->

        <!--文本抽取-->
        <!--<dependency>
            <groupId>com.lirong</groupId>
            <artifactId>webdp-nlp</artifactId>
            <version>1.0.0</version>
        </dependency>-->

        <!--专利发明-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>webdp-patent</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--获奖产品-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>webdp-award</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--军事设施-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>installation-installation</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>update-database</artifactId>
            <version>1.0.0</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>2.1.1.RELEASE</version>
                <configuration>
                    <fork>true</fork> <!-- 如果没有该配置，devtools不会生效 -->
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>3.0.0</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <warName>${project.artifactId}</warName>
                </configuration>
            </plugin>
        </plugins>
        <finalName>${project.artifactId}</finalName>
    </build>

</project>