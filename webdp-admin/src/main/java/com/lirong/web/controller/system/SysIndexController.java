package com.lirong.web.controller.system;

import java.util.*;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletResponse;

import com.lirong.common.service.HomeStatisticalService;
import com.lirong.common.vo.StatisticsVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.lirong.common.config.WebdpConfig;
import com.lirong.common.constant.ShiroConstants;
import com.lirong.common.core.controller.BaseController;
import com.lirong.common.core.domain.AjaxResult;
import com.lirong.common.core.domain.entity.SysMenu;
import com.lirong.common.core.domain.entity.SysUser;
import com.lirong.common.core.text.Convert;
import com.lirong.common.utils.CookieUtils;
import com.lirong.common.utils.DateUtils;
import com.lirong.common.utils.ServletUtils;
import com.lirong.common.utils.ShiroUtils;
import com.lirong.common.utils.StringUtils;
import com.lirong.framework.shiro.service.SysPasswordService;
import com.lirong.system.service.ISysConfigService;
import com.lirong.system.service.ISysMenuService;

/**
 * 首页 业务处理
 *
 * <AUTHOR>
 */
@Controller
public class SysIndexController extends BaseController {
    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private ISysConfigService configService;

    @Autowired
    private SysPasswordService passwordService;

    @Autowired//参数配置
    private ISysConfigService sysConfigService;

    @Autowired
    List<HomeStatisticalService> statisticalServiceList;

    // 系统首页
    @GetMapping("/index")
    public String index(ModelMap mmap) {
        // 取身份信息
        SysUser user = ShiroUtils.getSysUser();
        //获取系统名称
        String sysName = sysConfigService.selectConfigByKey("sys.name.cn");
        mmap.put("sysName", sysName);
        // 根据用户id取出菜单
        List<SysMenu> menus = menuService.selectMenusByUser(user);
        mmap.put("menus", menus);
        mmap.put("user", user);
        mmap.put("sideTheme", configService.selectConfigByKey("sys.index.sideTheme"));
        mmap.put("skinName", configService.selectConfigByKey("sys.index.skinName"));
        mmap.put("ignoreFooter", configService.selectConfigByKey("sys.index.ignoreFooter"));
        mmap.put("copyrightYear", WebdpConfig.getCopyrightYear());
        mmap.put("demoEnabled", WebdpConfig.isDemoEnabled());
        mmap.put("isDefaultModifyPwd", initPasswordIsModify(user.getPwdUpdateDate()));
        mmap.put("isPasswordExpired", passwordIsExpiration(user.getPwdUpdateDate()));

        // 菜单导航显示风格
        String menuStyle = configService.selectConfigByKey("sys.index.menuStyle");
        // 移动端，默认使左侧导航菜单，否则取默认配置
        String indexStyle = ServletUtils.checkAgentIsMobile(ServletUtils.getRequest().getHeader("User-Agent")) ? "index" : menuStyle;

        // 优先Cookie配置导航菜单
        Cookie[] cookies = ServletUtils.getRequest().getCookies();
        for (Cookie cookie : cookies) {
            if (StringUtils.isNotEmpty(cookie.getName()) && "nav-style".equalsIgnoreCase(cookie.getName())) {
                indexStyle = cookie.getValue();
                break;
            }
        }
        String webIndex = "topnav".equalsIgnoreCase(indexStyle) ? "index-topnav" : "index";
        return webIndex;
    }

    /**
     * 首页统计机构/人员/装备/资源库数量
     *
     * @return 结果
     */
    @GetMapping("home/countStatistics")
    @ResponseBody
    public List<StatisticsVO> accordingCategoryStatistics() {
        List<StatisticsVO> countStatistics = new ArrayList<>();

        statisticalServiceList.forEach(service -> {
            StatisticsVO statistics = new StatisticsVO();
            statistics.setName(service.subject() + "总数");
            statistics.setValue(service.statisticalTotalQuantity().toString());
            countStatistics.add(statistics);
        });
        return countStatistics;
    }

    /**
     * 按数据类型统计
     *
     * @return
     */
    @GetMapping("home/statisticalItemQuantity")
    @ResponseBody
    public Map<String, List<StatisticsVO>> statisticalItemQuantity() {
        Map<String, List<StatisticsVO>> result = new LinkedHashMap<>();
        for (HomeStatisticalService service : statisticalServiceList) {
            List<StatisticsVO> statisticsVOList = service.statisticalItemQuantity();
            if (StringUtils.isNotNull(statisticsVOList)) {
                result.put(service.subject(), statisticsVOList);
            }
        }
        return result;
    }


    /**
     * 首页趋势统计
     *
     * @return 结果
     */
    @GetMapping("home/trendStatistics")
    @ResponseBody
    public Map<String, List<StatisticsVO>> trendStatistics() {
        Map<String, List<StatisticsVO>> map = new HashMap<>();
        statisticalServiceList.forEach(service -> {
            List<StatisticsVO> statisticsVOList = service.trendAnalysis();
            if (null != statisticsVOList) {
                map.put(service.subject(), statisticsVOList);
            }
        });
        return map;
    }

    // 锁定屏幕
    @GetMapping("/lockscreen")
    public String lockscreen(ModelMap mmap) {
        mmap.put("user", ShiroUtils.getSysUser());
        ServletUtils.getSession().setAttribute(ShiroConstants.LOCK_SCREEN, true);
        return "lock";
    }

    // 解锁屏幕
    @PostMapping("/unlockscreen")
    @ResponseBody
    public AjaxResult unlockscreen(String password) {
        SysUser user = ShiroUtils.getSysUser();
        if (StringUtils.isNull(user)) {
            return AjaxResult.error("服务器超时，请重新登陆");
        }
        if (passwordService.matches(user, password)) {
            ServletUtils.getSession().removeAttribute(ShiroConstants.LOCK_SCREEN);
            return AjaxResult.success();
        }
        return AjaxResult.error("密码不正确，请重新输入。");
    }

    // 切换主题
    @GetMapping("/system/switchSkin")
    public String switchSkin() {
        return "skin";
    }

    // 切换菜单
    @GetMapping("/system/menuStyle/{style}")
    public void menuStyle(@PathVariable String style, HttpServletResponse response) {
        CookieUtils.setCookie(response, "nav-style", style);
    }

    // 系统介绍
    @GetMapping("/system/main")
    public String main(ModelMap mmap) {
        mmap.put("version", WebdpConfig.getVersion());
        return "main";
    }

    // 检查初始密码是否提醒修改
    public boolean initPasswordIsModify(Date pwdUpdateDate) {
        Integer initPasswordModify = Convert.toInt(configService.selectConfigByKey("sys.account.initPasswordModify"));
        return initPasswordModify != null && initPasswordModify == 1 && pwdUpdateDate == null;
    }

    // 检查密码是否过期
    public boolean passwordIsExpiration(Date pwdUpdateDate) {
        Integer passwordValidateDays = Convert.toInt(configService.selectConfigByKey("sys.account.passwordValidateDays"));
        if (passwordValidateDays != null && passwordValidateDays > 0) {
            if (StringUtils.isNull(pwdUpdateDate)) {
                // 如果从未修改过初始密码，直接提醒过期
                return true;
            }
            Date nowDate = DateUtils.getNowDate();
            return DateUtils.differentDaysByMillisecond(nowDate, pwdUpdateDate) > passwordValidateDays;
        }
        return false;
    }
}
