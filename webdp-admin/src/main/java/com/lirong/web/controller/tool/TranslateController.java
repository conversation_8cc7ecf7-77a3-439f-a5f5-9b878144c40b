package com.lirong.web.controller.tool;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lirong.common.core.domain.AjaxResult;
import com.lirong.common.utils.StringUtils;
import com.lirong.common.utils.translate.TranslateUtils;
import com.lirong.system.domain.SysTranslate;
import com.lirong.system.domain.Translate;
import com.lirong.system.service.ISysTranslateService;
import com.lirong.web.controller.tool.translate.MicrosoftTranslateApi;
import com.lirong.web.controller.tool.translate.TransApi;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

@Controller
@RequestMapping("/tool/translate")
public class TranslateController {

    @Autowired
    private ISysTranslateService translateService;

    private String prefix = "tool/translate";

    //private static final String APP_ID = "20210108000666570";//侯
    //private static final String APP_ID = "20220801001289415";//丁
    //private static final String APP_ID = "20220526001229921";//丁
    private static final String APP_ID = "20200307000394331";//王
    //private static final String APP_ID = "20200307000394331";
    //private static final String SECURITY_KEY = "v040BNGP04OQkCRUpHNT";//侯
    //private static final String SECURITY_KEY = "Y70hJThgNrPEWPcWb2HR";//丁
    //private static final String SECURITY_KEY = "W22QTIEwqOW5uktcltV1";//丁
    private static final String SECURITY_KEY = "GiaZrtov1Fve7BNwZlKl";//王
    //private static final String SECURITY_KEY = "GiaZrtov1Fve7BNwZlKl";
    private static TransApi api = new TransApi(APP_ID, SECURITY_KEY);
    private static AtomicLong expend = new AtomicLong();

    @GetMapping()
    public String build() {
        return prefix + "/index";
    }

    public static List<SysTranslate> translateList;

    public static List<String> processedIdList;
    //翻译结果，用于解决翻译相同字符串问题
    public static Map<String, String> translateResultMap;
    //是否翻译，解决已翻译直接从translateResultMap取值后也会等待问题
    public static Boolean isTranslated;

    /**
     * 获取待翻译数据
     *
     * @param translate 翻译表
     * @return 结果
     */
    @GetMapping("/pending")
    @ResponseBody
    public AjaxResult getPending(Translate translate) {
        if (StringUtils.isNull(translateResultMap)) {
            translateResultMap = new HashMap<>();
        }
        if (StringUtils.isNull(processedIdList)) {
            processedIdList = new ArrayList<>();
        }
        int maxTranslateStringCount = 1000000;
        int translateStringCount = 0;
        // 查询待翻译数据
        List<SysTranslate> presentTranslateList = translateService.getPendingData(translate).stream().filter(sysTranslate -> sysTranslate.getPending().length() < 35000).collect(Collectors.toList());
        for (SysTranslate sysTranslate : presentTranslateList) {
            if (!processedIdList.contains(sysTranslate.getId())) {
                processedIdList.add(sysTranslate.getId());
                String pending = sysTranslate.getPending();
                int length = pending.length();
                translateStringCount = translateStringCount + length;
                //调用翻译前校验已翻译字符串长度，避免欠费
                if (translateStringCount > maxTranslateStringCount) {
                    return AjaxResult.error("超出设置翻译字符串长度！");
                }
                String value = translateResultMap.get(pending);
                if (StringUtils.isBlank(value)) {
                    //待翻译字段数据不为中文且翻译结果字段为空或者翻译字段与带翻译字段一致
                    String translated = translate(pending);//百度翻译
                    //String translated = MicrosoftTranslateApi.translate(pending);/微软翻译
                    sysTranslate.setTranslated(translated);
                    translateResultMap.put(pending, translated);
                    isTranslated = true;
                } else {
                    sysTranslate.setTranslated(value);
                    isTranslated = false;
                }
                // 保存翻译结果
                translateService.saveTranslateResult(translate.getTableName(), translate.getPrimaryKey(), sysTranslate.getId(), translate.getTranslatedField(), sysTranslate.getTranslated());
                if (isTranslated) {
                    try {
                        if (expend.get() < 1000) {
                            Thread.sleep(1000 - expend.get() + 200);
                        }
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }
            }
        }
        // 保存翻译结果
        //translateService.saveTranslateResult(translate);
        if (StringUtils.isNull(translateList) || translateList.size() == 0) {
            translateList = presentTranslateList;
        } else {
            translateList.addAll(presentTranslateList);
        }
        if (translateService.getPendingData(translate).stream().filter(sysTranslate -> sysTranslate.getPending().length() < 35000).collect(Collectors.toList()).size() > 0) {
            getPending(translate);
        }
        return AjaxResult.success(translateList);
    }

    public static void main(String[] args) {
        String query = "hello world.";
        System.out.println(translate(query));
    }

    /**
     * 错误码	含义	解决方法
     * 52000	成功
     * 52001	请求超时	重试
     * 52002	系统错误	重试
     * 52003	未授权用户	检查您的 appid 是否正确，或者服务是否开通
     * 54000	必填参数为空	检查是否少传参数
     * 54001	签名错误	请检查您的签名生成方法
     * 54003	访问频率受限	请降低您的调用频率
     * 54004	账户余额不足	请前往管理控制台为账户充值
     * 54005	长query请求频繁	请降低长query的发送频率，3s后再试
     * 58000	客户端IP非法	检查个人资料里填写的 IP地址 是否正确
     * 可前往管理控制平台修改
     * IP限制，IP可留空
     * 58001	译文语言方向不支持	检查译文语言是否在语言列表里
     * 58002	服务当前已关闭	请前往管理控制台开启服务
     * 90107	认证未通过或未生效	请前往我的认证查看认证进度
     *
     * @param text
     * @return
     */
    public static String translate(String text) {
        int tryTimes = 0;
        long current = System.currentTimeMillis();
        JSONObject object = JSON.parseObject(api.getTransResult(text, "auto", "zh"));
        if (StringUtils.isNull(object)) {
            return text;
        }
        tryTimes++;
        StringBuilder builder = new StringBuilder();

        JSONArray array = object.getJSONArray("trans_result");
        if (array != null) {
            for (int i = 0; i < array.size(); i++) {
                JSONObject obj = array.getJSONObject(i);
                if (i == array.size() - 1) {
                    builder.append(obj.get("dst"));
                } else {
                    builder.append(obj.get("dst") + "\n");
                }
            }
        } else {
            System.err.println(object.toJSONString());
            // 由于调用过于频繁失败时等待1秒钟重新调用
            if ("54003".equals(object.get("error_code")) && tryTimes <= 3) {
                try {
                    Thread.sleep(1000);
                    tryTimes++;
                    return translate(text);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
            // 判断是否翻译
            if (!text.isEmpty() && ("52001".equals(object.get("error_code")) || "52002".equals(object.get("error_code"))) && tryTimes <= 3) {
                try {
                    Thread.sleep(1000);
                    tryTimes++;
                    return translate(text);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }

        long expendTime = (System.currentTimeMillis() - current);
        expend.set(expendTime);

        return builder.toString();
    }

}
