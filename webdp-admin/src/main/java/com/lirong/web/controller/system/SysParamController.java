package com.lirong.web.controller.system;

import java.util.*;
import java.util.stream.Collectors;

import com.alibaba.fastjson.JSONObject;
import com.lirong.common.utils.StringUtils;
import com.lirong.system.domain.SysParam;
import com.lirong.system.service.SysParamService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.lirong.common.annotation.Log;
import com.lirong.common.enums.BusinessType;
import com.lirong.common.core.controller.BaseController;
import com.lirong.common.core.domain.AjaxResult;

/**
 * 系统配置Controller
 *
 * <AUTHOR>
 * @date 2023-03-29
 */
@Controller
@RequestMapping("/system/param")
public class SysParamController extends BaseController {
    private String prefix = "system/param";

    @Autowired
    private SysParamService sysParamService;

    @RequiresPermissions("system:param:view")
    @GetMapping()
    public String param(ModelMap mmap) {
        List<SysParam> list = sysParamService.selectSysParamList();
        Map<String, List<SysParam>> listMap = list.stream().collect(Collectors.groupingBy(SysParam::getParamKey));
        mmap.put("listMap", listMap);
        List<SysParam> patemNameList = list.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(SysParam::getParamName))), ArrayList::new));
        mmap.put("patemNameList", patemNameList);
        return prefix + "/param";
    }

    /**
     * 修改保存系统配置
     */
    @RequiresPermissions("system:param:edit")
    @Log(title = "系统配置", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(String data) {
        List<SysParam> list = new ArrayList<SysParam>();
        list = JSONObject.parseArray(data, SysParam.class);
        String mag = sysParamService.updateSysParam(list);
        if (StringUtils.isNotBlank(mag)){
            return AjaxResult.error(mag);
        }
        return AjaxResult.success("操作成功！");
    }
}
