package com.lirong.web.controller.common;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.alibaba.fastjson.JSONObject;
import com.lirong.common.service.FileDownloadService;
import com.lirong.common.utils.file.MimeTypeUtils;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import com.lirong.common.config.WebdpConfig;
import com.lirong.common.config.ServerConfig;
import com.lirong.common.constant.Constants;
import com.lirong.common.core.domain.AjaxResult;
import com.lirong.common.utils.StringUtils;
import com.lirong.common.utils.file.FileUploadUtils;
import com.lirong.common.utils.file.FileUtils;

import java.io.*;
import java.net.URLEncoder;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static org.springframework.util.FileCopyUtils.BUFFER_SIZE;

/**
 * 通用请求处理
 *
 * <AUTHOR>
 */
@Controller
public class CommonController {
    private static final Logger log = LoggerFactory.getLogger(CommonController.class);

    @Autowired
    private ServerConfig serverConfig;
    @Autowired
    private List<FileDownloadService> fileDownloadServiceList;

    /**
     * 通用下载请求
     *
     * @param fileName 文件名称
     * @param delete   是否删除
     */
    @GetMapping("common/download")
    public void fileDownload(String fileName, Boolean delete, HttpServletResponse response, HttpServletRequest request) {
        try {
            if (!FileUtils.checkAllowDownload(fileName)) {
                throw new Exception(StringUtils.format("文件名称({})非法，不允许下载。 ", fileName));
            }
            String realFileName = "";
            if (fileName.contains(".zip")) {
                realFileName = fileName.substring(fileName.indexOf("_") + 1);
            } else {
                realFileName = System.currentTimeMillis() + fileName.substring(fileName.indexOf("_") + 1);
            }
            String filePath = WebdpConfig.getDownloadPath() + fileName;

            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.setHeader("filename", URLEncoder.encode(fileName, "UTF-8"));
            response.setHeader("Access-Control-Expose-Headers", "filename");
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(filePath, response.getOutputStream());
            if (delete) {
                FileUtils.deleteFile(filePath);
            }
        } catch (Exception e) {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 通用上传请求
     */
    @PostMapping("/common/upload")
    @ResponseBody
    public AjaxResult upload(MultipartFile file) throws Exception {
        try {
            // 上传文件路径
            String filePath = WebdpConfig.getUploadPath();
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file);
            String url = serverConfig.getUrl() + fileName;
            AjaxResult ajax = AjaxResult.success();
            ajax.put("fileName", fileName);
            ajax.put("url", url);
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * editor markdown 上传图片
     *
     * @param file
     * @return
     */
    @ResponseBody
    @PostMapping(value = "/editor/upload")
    public JSONObject editorUpload(@RequestParam(value = "editormd-image-file", required = false) MultipartFile file) {
        JSONObject result = new JSONObject();
        try {
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(WebdpConfig.getUploadPath(), file);
            result.put("url", fileName);
            result.put("success", 1);
            result.put("message", "上传成功");
        } catch (Exception e) {
            result.put("success", 0);
        }
        return result;
    }


    /**
     * 图片上传请求
     */
    @PostMapping("/common/upload/img")
    @ResponseBody
    public AjaxResult uploadImg(MultipartFile file, String type) throws Exception {
        try {
            // 上传文件路径
            String filePath = "";
            if (StringUtils.isNotBlank(type)) {
                filePath = WebdpConfig.getPathByType(type);
            } else {
                filePath = WebdpConfig.getImgPath();
            }
            // 上传并返回新文件名称
            String fileName = FileUploadUtils.upload(filePath, file, MimeTypeUtils.IMAGE_EXTENSION);
            AjaxResult ajax = AjaxResult.success();
            ajax.put("fileName", file.getOriginalFilename());
            ajax.put("url", fileName);
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 文件上传请求
     */
    @PostMapping("/common/upload/file")
    @ResponseBody
    public AjaxResult uploadFile(MultipartFile file, MultipartFile fileUpload) {
        try {
            AjaxResult ajax = AjaxResult.success();
            //file->InputStream,生成md5
            ajax.put("md5", DigestUtils.md5Hex(file.getInputStream()));
            // 上传文件路径
            String filePath = WebdpConfig.getFilePath();
            // 上传并返回新文件存储路径
            String fileurl = FileUploadUtils.upload(filePath, file);
            String fileName = file.getOriginalFilename();
            String fileSuffix = fileName.substring(fileName.indexOf(".") + 1, fileName.length());
            int pages = 0;
            if (fileSuffix.equalsIgnoreCase("pdf")) {
                PDDocument pdfReader = PDDocument.load(new File((filePath + fileurl).replace("profile/file", "")));
                pages = pdfReader.getNumberOfPages();
                pdfReader.close();
            }
            ajax.put("pages", pages);
            ajax.put("fileName", fileName);
            ajax.put("url", fileurl);
            return ajax;
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 根据文件路径删除文件
     */
    @PostMapping("/common/deleteFileByFileUrl")
    @ResponseBody
    public AjaxResult deleteFileByFileUrl(String fileUrl) {
        try {
            // 文件路径
            fileUrl = fileUrl.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile());
            File myDelFile = new File(fileUrl);
            if (myDelFile.exists()) {
                myDelFile.delete();
            }
            //判断是否存在缩略图 如果有 删除
            if (fileUrl.contains(Constants.THUMBNAIL_PREFIX)) {
                String thumbnailPath = fileUrl.replace(Constants.THUMBNAIL_PREFIX, "");
                File thumbnailDelFile = new File(thumbnailPath);
                if (thumbnailDelFile.exists()) {
                    thumbnailDelFile.delete();
                }
            }
            return AjaxResult.success();
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }
    }

    /**
     * 本地资源通用下载
     */
    @GetMapping("/common/download/resource")
    public void resourceDownload(String resource, HttpServletRequest request, HttpServletResponse response) {
        try {
            if (!FileUtils.checkAllowDownload(resource)) {
                throw new Exception(StringUtils.format("资源文件({})非法，不允许下载。 ", resource));
            }
            // 本地资源路径
            String localPath = WebdpConfig.getProfile();
            // 数据库资源地址
            String downloadPath = localPath + StringUtils.substringAfter(resource, Constants.RESOURCE_PREFIX);
            // 下载名称
            String downloadName = StringUtils.substringAfterLast(downloadPath, "/");
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            FileUtils.setAttachmentResponseHeader(response, downloadName);
            FileUtils.writeBytes(downloadPath, response.getOutputStream());
        } catch (Exception e) {
            log.error("下载文件失败", e);
        }
    }

    /**
     * 跳转文件导出页面
     */
    @GetMapping("/file/export")
    public String people() {
        return "export/exportFile";
    }

    /**
     * 文件导出
     */
    @GetMapping("/file/export/{type}")
    @ResponseBody
    public AjaxResult fileExport(@PathVariable("type") List<String> type) {
        List<String> filePathList = new ArrayList<>();
        List<String> inexistenceFilePath = new ArrayList<>();
        for (FileDownloadService fileDownloadService : fileDownloadServiceList) {
            List<String> path = fileDownloadService.getFilePath(type);
            if (StringUtils.isNotNull(path) && path.size() > 0) {
                for (String filePath : path) {
                    String fileAbsolutePath = filePath.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile());
                    File file = new File(fileAbsolutePath);
                    if (!file.exists()) {
                        inexistenceFilePath.add(filePath);
                    }
                }
                filePathList.addAll(path);
            }
        }
        if (type.contains("firepower")) {
            File flagsDir = new File(WebdpConfig.getProfile() + "/img/flags");
            if (flagsDir.exists()) {
                File[] files = flagsDir.listFiles();
                for (File file : files) {
                    filePathList.add(file.getAbsolutePath().replaceAll("\\\\", "/").replace(WebdpConfig.getProfile(), Constants.RESOURCE_PREFIX));
                }
            } else {
                AjaxResult.error("军力指数国旗不存在");
            }
            File mapsDir = new File(WebdpConfig.getProfile() + "/img/maps");
            if (mapsDir.exists()) {
                File[] files = mapsDir.listFiles();
                for (File file : files) {
                    filePathList.add(file.getAbsolutePath().replaceAll("\\\\", "/").replace(WebdpConfig.getProfile(), Constants.RESOURCE_PREFIX));
                }
            } else {
                AjaxResult.error("军力指数地图不存在");
            }
        }
        if (inexistenceFilePath.size() > 0) {
            AjaxResult.error("以下文件不存在：" + JSONObject.toJSONString(inexistenceFilePath));
        }
        //第一层文件夹,也是压缩包名称
        String zipName = UUID.randomUUID().toString().replace("-", "");
        String downloadPath = WebdpConfig.getDownloadPath();
        if (filePathList.size() > 0) {
            basisFilePathCopy(filePathList, downloadPath, zipName);
        } else {
            return AjaxResult.error("选中类型无文件！");
        }
        return AjaxResult.success(zipName + ".zip");
    }

    /**
     * 根据文件路径拷贝文件到指定路径
     *
     * @param filePathList 需要拷贝的文件路径
     * @param downloadPath 临时文件夹路径
     * @param zipName      第一层文件夹,也是压缩包名称
     */
    public void basisFilePathCopy(List<String> filePathList, String downloadPath, String zipName) {
        if (StringUtils.isNotNull(filePathList) && filePathList.size() > 0) {
            int listSize = filePathList.size();
            String profile = WebdpConfig.getProfile();
            for (String path : filePathList) {
                System.err.println("剩余：" + listSize-- + "个文件");
                if (StringUtils.isNotBlank(path)) {
                    //获取绝对路径
                    String absolutePath = path.replace(Constants.RESOURCE_PREFIX, profile);
                    //将文件拷贝到临时文件夹中
                    copyFile(absolutePath, absolutePath.replace(profile, downloadPath + zipName));
                    if (absolutePath.contains(Constants.THUMBNAIL_PREFIX)) {
                        //校验是否为缩略图
                        copyFile(absolutePath.replace(Constants.THUMBNAIL_PREFIX, ""), absolutePath.replace(Constants.THUMBNAIL_PREFIX, "").replace(profile, downloadPath + zipName));
                    }
                }
            }
            //将所有文件打包
            try {
                ZipOutputStream zos = null;
                try {
                    File zipFile = new File(downloadPath + zipName + ".zip");
                    if (zipFile.exists()) {
                        //删除已存在压缩包
                        zipFile.delete();
                    }
                    zos = new ZipOutputStream(new FileOutputStream(zipFile));
                    File sourceFile = new File(downloadPath + zipName);
                    compress(sourceFile, zos, sourceFile.getName(), true);
                    //压缩完成 删除原文件
                    deleteFiles(sourceFile);
                } catch (Exception e) {
                    e.printStackTrace();
                } finally {
                    if (zos != null) {
                        try {
                            zos.close();
                        } catch (IOException e) {
                            e.printStackTrace();
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 删除文件夹
     * 删除某个目录及目录下的所有子目录和文件
     *
     * @param file 文件或目录
     * @return 删除结果
     */
    public static boolean deleteFiles(File file) {
        boolean result = false;
        //目录
        if (file.isDirectory()) {
            File[] childrenFiles = file.listFiles();
            for (File childFile : childrenFiles) {
                result = deleteFiles(childFile);
                if (!result) {
                    return result;
                }
            }
        }
        //删除 文件、空目录
        result = file.delete();
        return result;
    }

    /**
     * 递归压缩方法
     *
     * @param sourceFile       源文件
     * @param zos              zip输出流
     * @param name             压缩后的名称
     * @param keepDirStructure 是否保留原来的目录结构
     *                         true:保留目录结构;
     *                         false:所有文件跑到压缩包根目录下(注意：不保留目录结构可能会出现同名文件,会压缩失败)
     */
    private static void compress(File sourceFile, ZipOutputStream zos, String name, boolean keepDirStructure) throws Exception {
        byte[] buf = new byte[BUFFER_SIZE];
        if (sourceFile.isFile()) {
            // 向zip输出流中添加一个zip实体，构造器中name为zip实体的文件的名字
            zos.putNextEntry(new ZipEntry(name));
            // copy文件到zip输出流中
            int len;
            FileInputStream in = new FileInputStream(sourceFile);
            while ((len = in.read(buf)) != -1) {
                zos.write(buf, 0, len);
            }
            // Complete the entry
            zos.closeEntry();
            in.close();
        } else {
            File[] listFiles = sourceFile.listFiles();
            if (listFiles == null || listFiles.length == 0) {
                // 需要保留原来的文件结构时,需要对空文件夹进行处理
                if (keepDirStructure) {
                    // 空文件夹的处理
                    zos.putNextEntry(new ZipEntry(name + "/"));
                    // 没有文件，不需要文件的copy
                    zos.closeEntry();
                }
            } else {
                for (File file : listFiles) {
                    // 判断是否需要保留原来的文件结构
                    if (keepDirStructure) {
                        // 注意：file.getName()前面需要带上父文件夹的名字加一斜杠,
                        // 不然最后压缩包中就不能保留原来的文件结构,即：所有文件都跑到压缩包根目录下了
                        compress(file, zos, name + "/" + file.getName(), keepDirStructure);
                    } else {
                        compress(file, zos, file.getName(), keepDirStructure);
                    }
                }
            }
        }
    }

    /**
     * 文件拷贝
     *
     * @param oldPath 文件绝对路径
     * @param newPath 新的文件绝对路径
     */
    public static void copyFile(String oldPath, String newPath) {
        try {
            int byteread = 0;
            File oldfile = new File(oldPath);
            File newFile = new File(newPath);
            if (oldfile.isDirectory()) {
                //文件夹
                return;
            }
            //判断原文件是否存在 存在才进行拷贝
            if (oldfile.exists()) {
                //判断新文件路径中包含的所有文件夹是否存在
                if (!newFile.getParentFile().exists()) {
                    //如果文件夹不存在就新建文件夹
                    newFile.getParentFile().mkdirs();
                }
                InputStream inStream = new FileInputStream(oldPath);
                FileOutputStream fs = new FileOutputStream(newPath);
                byte[] buffer = new byte[1024];
                while ((byteread = inStream.read(buffer)) != -1) {
                    fs.write(buffer, 0, byteread);
                }
                inStream.close();
                fs.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
