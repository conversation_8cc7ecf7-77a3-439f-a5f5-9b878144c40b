package com.lirong.web.controller.tool.translate;

import java.io.IOException;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.*;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;

public class MicrosoftTranslateApi {
    private static String key = "c58fdece93ba4c66ac5a7178ffc2a035";
    public String endpoint = "https://api.cognitive.microsofttranslator.com";
    public String route = "/translate?api-version=3.0&to=zh-cn&to=it";
    public String url = endpoint.concat(route);

    // location, also known as region.
    // required if you're using a multi-service or regional (not global) resource. It can be found in the Azure portal on the Keys and Endpoint page.
    private static String location = "northcentralus";
    // Instantiates the OkHttpClient.
    OkHttpClient client = new OkHttpClient();

    // This function performs a POST request.
    public String Post(String text) throws IOException {
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType,
                "[{\"Text\": \"" + text + "\"}]");
        Request request = new Request.Builder()
                .url(url)
                .post(body)
                .addHeader("Ocp-Apim-Subscription-Key", key)
                // location required if you're using a multi-service or regional (not global) resource.
                .addHeader("Ocp-Apim-Subscription-Region", location)
                .addHeader("Content-type", "application/json")
                .build();
        Response response = client.newCall(request).execute();
        return response.body().string();
    }

    // This function prettifies the json response.
    public static String prettify(String json_text) {
        JsonParser parser = new JsonParser();
        JsonElement json = parser.parse(json_text);
        Gson gson = new GsonBuilder().setPrettyPrinting().create();
        return gson.toJson(json);
    }

    //翻译文本
    public static String translate(String text) {
        try {
            MicrosoftTranslateApi translateRequest = new MicrosoftTranslateApi();
            String response = translateRequest.Post(text);
            String prettify = prettify(response);
            System.err.println(prettify);
            JSONArray array = JSON.parseArray(prettify);
            String res = array.get(0).toString();
            JSONObject object = JSON.parseObject(res);
            JSONObject detectedLanguage = object.getJSONObject("detectedLanguage");
            String language = detectedLanguage.getString("language");
            if ("en".equals(language)) {
                JSONArray translationsJson = object.getJSONArray("translations");
                JSONObject resData = JSON.parseObject(translationsJson.get(0).toString());
                return resData.getString("text");
            }
            return "";
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    public static void main(String[] args) {
        try {
            String text = "";
            MicrosoftTranslateApi translateRequest = new MicrosoftTranslateApi();
            String response = translateRequest.Post(text);
            System.out.println(prettify(response));
        } catch (Exception e) {
            System.out.println(e);
        }
    }
}
