<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('分配用户')" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="form-user">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>账号：</label>
                            <input type="text" name="userName"/>
                        </li>
                        <li>
                            <label>用户昵称：</label>
                            <input type="text" name="nickName"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search('form-user', 'bootstrap-table-user')"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('form-user', 'bootstrap-table-user')"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table-user"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<script th:inline="javascript">
    let editFlag = [[${@permission.hasPermi('system:user:edit')}]];
    let removeFlag = [[${@permission.hasPermi('system:user:remove')}]];
    let sexDatas = [[${@dict.getType('sys_user_sex')}]];
    let prefix = ctx + "system/user";

    $(function() {
        let options = {
            id: "bootstrap-table-user",          // 指定表格ID
            toolbar: "toolbar-user",   // 指定工具栏ID
            formId: "form-user",
            url: prefix + "/list",
            modalName: "用户信息",
            singleSelect: true,
            columns: [{
                checkbox: true
            },
            {
                field: 'userName',
                title: '账号'
            },
            {
                field: 'nickName',
                title: '用户昵称'
            },
            {
                field: 'avatar',
                title: '头像',
                width: 80,
                formatter: function (value, row, index) {
                    if (value != null && value != '') {
                        return $.table.imageView(value, 300, 300);
                    } else {
                        return $.table.imageView('/img/default_people.png', 300, 300);
                    }
                }
            },
            {
                field: 'sex',
                title: '用户性别',
                formatter: function(value, row, index) {
                    return $.table.selectDictLabel(sexDatas, value);
                }
            },
            {
                field: 'email',
                title: '用户邮箱'
            }]
        };
        $.table.init(options);
    });

    let urlPrefix = [[${urlPrefix}]]
    let code = [[${code}]]
    function submitHandler() {
        let userId = $.table.selectColumns("userId").toString();
        if (userId == ''){
            $.modal.msgWarning('请选择用户！');
        } else {
            $.modal.loading("正在处理，请稍后...");
            $.ajax({
                type: "GET",
                url: ctx + urlPrefix + "/reedit/" + userId+ '/' + code,
                success: function(result) {
                    $.modal.closeLoading();
                    if (result.code == web_status.SUCCESS) {
                        window.parent.search()
                        $.modal.close();
                    }
                }
            });
        }
    }
</script>
</body>
</html>