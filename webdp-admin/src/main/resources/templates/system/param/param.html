<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改系统配置')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-param-edit1">
            <div id="html">

            </div>

        </form>
    </div>
    <div class="row">
        <div class="col-sm-offset-5 col-sm-10">
            <button type="button" class="btn btn-sm btn-primary" shiro:hasPermission="system:footer:edit" onclick="submitHandler()"><i class="fa fa-check"></i>保 存</button>&nbsp;
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        let prefix = ctx + "system/param";
        $("#form-param-edit").validate({
            focusCleanup: true
        });

        let listMap = [[${listMap}]]
        let patemNameList = [[${patemNameList}]]
        for (let i = 0; i < patemNameList.length; i++) {
            var element = patemNameList[i];
            let html ="" +
                "<div>\n" +
                "    <div><h3>" + element.paramName + "</h3></div>\n" +
                "    <form class='form-horizontal m' id = '" + element.paramKey.replaceAll(':', '') + "form'><div id = '" + element.paramKey.replaceAll(':', '') + "'></div></form>\n" +
                "</div>";
            $("#html").append(html)
            let paramValList = listMap[element.paramKey];
            for (let i = 0; i < paramValList.length; i++) {
                let param = paramValList[i];
                let html =
                    "       <div class='col-sm-12'>\n" +
                    "           <div class='form-group'>\n" +
                    "                <label class='col-sm-2 control-label is-required'>" + param.name + "：</label>\n" +
                    "                <input name='paramId' value='" + param.paramId + "' type='hidden'>\n" +
                    "                <input name='paramName' value='" + param.paramName + "' type='hidden'>\n" +
                    "                <input name='paramKey' value='" + param.paramKey + "' type='hidden'>\n" +
                    "                <input name='paramType' id='paramType" + i + "' value='" + param.paramType + "' type='hidden'>\n" +
                    "                <input name='name' value='" + param.name + "' type='hidden'>\n" +
                    "                <input name='oldValue' id='oldValue" + i + "' value='" + param.value + "' type='hidden'>\n" +
                    "                <div class='col-sm-9'>\n" +
                    "                    <input style='display: inline-block;' name='value' id='value" + i + "' value='" + param.value + "' oninput='valueChange(" + i + ")' class='form-control' type='text' required>\n" +
                    "                </div>\n" +
                    "                </div>\n" +
                    "           </div>\n" +
                    "       </div>"
                $("#" + param.paramKey.replaceAll(':', '')).append(html)
            }
        }


        function valueChange(index) {
            let paramType = $('#paramType' + index).val();
            let value = $('#value' + index).val();
            if (paramType === 'weight') {
                let exp = /^[+-]?\d*(\.\d*)?(e[+-]?\d+)?$/
                if (!exp.test(value) || value > 1 || value < 0 || value == 0 || value === ''){
                    $('#value' + index).val($('#oldValue' + index).val())
                    $.modal.msgWarning('请填写正确的权重，范围（0~1）');
                }
            }
        }

        function submitHandler() {
            let paramIds = document.getElementsByName('paramId');
            let paramNames = document.getElementsByName('paramName');
            let paramKeys = document.getElementsByName('paramKey');
            let paramTypes = document.getElementsByName('paramType');
            let names = document.getElementsByName('name');
            let values = document.getElementsByName('value');
            let  data = '[';
            let  suffix = ']';
            for (let i = 0; i < paramIds.length; i++) {
                let paramId = paramIds[i].value;
                let paramName = paramNames[i].value;
                let paramKey = paramKeys[i].value;
                let paramType = paramTypes[i].value;
                let name = names[i].value;
                let value = values[i].value;
                let entity = "{'paramId':'"+paramId+"','paramName':'"+paramName+"','paramKey':'"+paramKey+"','paramType':'"+paramType+"','name':'"+name+"','value':'"+value+"'}"
                if (i == 0){
                    data += entity
                } else {
                    data += "," + entity
                }
            }
            $.ajax({
                cache : true,
                type : "POST",
                url : prefix + "/edit",
                dataType:"JSON",
                data: {
                    data: data + suffix
                },
                async : false,
                error : function(res) {
                    debugger
                    $.modal.alertError("系统错误");
                },
                success : function(res) {
                    if (res.code == web_status.SUCCESS){
                        $.modal.msgSuccess(res.msg)
                    }else{
                        $.modal.alertError(res.msg)
                    }
                }
            });
        }
    </script>
</body>
</html>