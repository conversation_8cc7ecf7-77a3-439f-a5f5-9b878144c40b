<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('翻译')" />
    <th:block th:include="include :: select2-css" />
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content animated fadeInRight">
        <div class="row">
            <div class="col-sm-5">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>数据翻译</h5>
                    </div>
                    <div class="ibox-content">
                        <form class="form-horizontal" id="translateForm">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">表名：</label>

                                <div class="col-sm-8">
                                    <input type="input" name="tableName" placeholder="表名" class="form-control"> <span class="help-block m-b-none">请输入需要翻译的表名</span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">主键：</label>

                                <div class="col-sm-8">
                                    <input type="input" name="primaryKey" placeholder="主键" class="form-control">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">待翻译字段：</label>

                                <div class="col-sm-8">
                                    <input type="input" name="pendingField" placeholder="待翻译字段" class="form-control">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">存储翻译结果的字段：</label>

                                <div class="col-sm-8">
                                    <input type="input" name="translatedField" placeholder="存储翻译结果的字段" class="form-control">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">覆盖原有翻译结果：</label>
                                <div class="col-sm-8">
                                    <div class="radio check-box">
                                        <label>
                                            <input type="radio" checked="" value="false" name="overwrite"> <i></i> 否</label>
                                    </div>
                                    <div class="radio check-box">
                                        <label>
                                            <input type="radio" value="true" name="overwrite"> <i></i> 是</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-offset-3 col-sm-8">
                                    <button class="btn btn-sm btn-white" id="btnTranslate" type="button">翻 译</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-sm-5">
                <div class="ibox float-e-margins">
                    <div class="ibox-title">
                        <h5>格式化文件路径</h5>
                    </div>
                    <div class="ibox-content">
                        <form class="form-horizontal" id="formatFilePathForm">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">表名：</label>

                                <div class="col-sm-8">
                                    <input type="input" name="tableName" placeholder="表名" class="form-control"> <span class="help-block m-b-none">请输入需要格式化文件路径的表名</span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">主键：</label>

                                <div class="col-sm-8">
                                    <input type="input" name="primaryKey" placeholder="主键" class="form-control">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">文件路径字段：</label>

                                <div class="col-sm-8">
                                    <input type="input" name="filePathField" placeholder="文件路径字段" class="form-control">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">缩略图存储字段：</label>

                                <div class="col-sm-8">
                                    <input type="input" name="thumbnailField" placeholder="生成缩略图存储字段" class="form-control">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 control-label">类型：</label>

                                <div class="col-sm-8">
                                    <div class="radio check-box">
                                        <label>
                                            <input type="radio" checked="" value="personnel" name="type"> <i></i> 人员</label>
                                    </div>
                                    <div class="radio check-box">
                                        <label>
                                            <input type="radio" value="organization" name="type"> <i></i> 机构</label>
                                    </div>
                                    <div class="radio check-box">
                                        <label>
                                            <input type="radio" value="weaponry" name="type"> <i></i> 装备</label>
                                    </div>
                                    <div class="radio check-box">
                                        <label>
                                            <input type="radio" value="resource" name="type"> <i></i> 资源库</label>
                                    </div>
                                    <div class="radio check-box">
                                        <label>
                                            <input type="radio" value="other" name="type"> <i></i> 其他</label>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-offset-3 col-sm-8">
                                    <button class="btn btn-sm btn-white" id="btnFormatFilePath" type="button">格式化文件路径</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js" />
    <script th:inline="javascript">
        var translate = ctx + "tool/translate";
        var file = ctx + "tool/file";

        $(function() {
            $("#btnTranslate").click(function () {
                $.get(translate + "/pending", $("#translateForm").serialize(), function(data){
                   console.log(data)
                },'json');
            });
            $("#btnFormatFilePath").click(function () {
                $.get(file + "/formatFilePath", $("#formatFilePathForm").serialize(), function(data){
                   console.log(data)
                },'json');
            });
        });
    </script>
</body>
</html>