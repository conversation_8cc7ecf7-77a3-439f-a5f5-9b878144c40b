<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('国防单位列表')"/>
    <th:block th:include="include :: layout-latest-css"/>
    <th:block th:include="include :: select2-css"/>
    <th:block th:include="include :: ztree-css"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="form-operational" class="form-inline">
                <div class="select-list">
                    <input type="hidden" name="orgType" id="orgType" value="国防单位">
                    <ul>
                        <li>
                            <label style="width: 100px;">国家/地区：</label>
                            <select class="form-control" name="country" id="country" th:with="type=${@dict.getType('sys_country')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <label>机构名称：</label>
                            <input type="text" name="orgNameCn" id="orgNameCn"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search('form-operational', 'bootstrap-table-operational')"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('form-operational', 'bootstrap-table-operational')"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar-operational" role="group">
            <a class="btn btn-success" onclick="$.operate.addTab()" shiro:hasPermission="organization:operational:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="editOrg(null, null)"
               shiro:hasPermission="organization:operational:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="remove(null, null)"
               shiro:hasPermission="organization:operational:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-info" onclick="importExcel()" shiro:hasPermission="organization:operational:import">
                <i class="fa fa-upload"></i> 导入
            </a>
            <a class="btn btn-white" th:href="@{/template/org/Operational.xlsx}"
               shiro:hasPermission="organization:operational:import" style="display:none;">
                <i class="fa fa-cloud-download"></i> 导入下载模板
            </a>
            <a class="btn btn-warning multiple disabled" onclick="exportExcel()"
               shiro:hasPermission="organization:operational:export">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table-operational"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: ztree-js"/>
<th:block th:include="include :: select2-js"/>
<th:block th:include="include :: layout-latest-js"/>
<script th:inline="javascript">
    let editFlag = [[${@permission.hasPermi('organization:operational:edit')}]]
    let removeFlag = [[${@permission.hasPermi('organization:operational:remove')}]]
    let selectFlag = [[${@permission.hasPermi('organization:operational:list')}]]
    let countryDatas = [[${@dict.getType('sys_country')}]]
    let organization = ctx + "organization/org";
    let prefix = ctx + "organization/operational";

    //加载数据列表
    $(function () {
        let options = {
            id: "bootstrap-table-operational",          // 指定表格ID
            toolbar: "toolbar-operational",   // 指定工具栏ID
            formId: "form-operational",
            url: organization + "/orgList",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: organization + "/removeOrgByOrgCode",
            exportUrl: prefix + "/exportOperational",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            modalName: "国防单位",
            rememberSelected: true,//翻页记住选择
            uniqueId: 'orgCode',
            columns: [{
                field: 'state',
                checkbox: true
            },
                {
                    align: 'center',
                    title: "序号",
                    width: 60,
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'orgCode',
                    title: '机构编码',
                    formatter: function (value, row, index) {
                        return $.table.tooltip(value, 7);
                    }
                },
                {
                    field: 'country',
                    title: '国家/地区',
                    width: 100,
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(countryDatas, value);
                    }
                },
                {
                    field: 'orgNameCn',
                    title: '机构名称',
                    formatter: function (value, row, index) {
                        return $.table.tooltip(value, 8);
                    }
                },
                {
                    field: 'avatar',
                    title: '徽章',
                    width: 80,
                    formatter: function (value, row, index) {
                        if (value != null && value != '') {
                            return $.table.imageView(value, 300, 300);
                        } else {
                            return $.table.imageView('/img/default_org.png', 300, 300);
                        }
                    }
                },
                {
                    field: 'establishTime',
                    title: '成立时间'
                },
                {
                    field: 'address',
                    title: '驻地',
                    formatter: function (value, row, index) {
                        return $.table.tooltip(value, 16);
                    }
                },
                {
                    field: 'source',
                    title: '数据来源',
                    formatter: function (value, row, index) {
                        if (value != '' && value != null) {
                            let html = "";
                            var sourceArr = value.split(';');
                            if (sourceArr != null && sourceArr.length > 0) {
                                let urlReg = /[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?/;
                                for (let i = 0; i < sourceArr.length; i++) {
                                    let source = sourceArr[i];
                                    let domainName = urlReg.exec(source);//根据正则取出网址域名
                                    if (domainName != null && domainName != '') {
                                        if (i > 0) {
                                            html += '</br>'
                                        }
                                        html += "<a target='_blank' href='" + source + "' title='" + source + "'>" + (domainName[0] != null && domainName[0] != '' ? domainName[0] : source) + "</a>";
                                    }
                                }
                                if (html != null && html != '') {
                                    return html;
                                }
                            }
                        }
                        return $.table.tooltip(value, 8);
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    width: 240,
                    formatter: function (value, row, index) {
                        let actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="editOrg(\'' + row.orgCode + '\')"><i class="fa fa-edit"></i>修改</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="remove(\'' + row.orgCode + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    //刷新机构列表
    function refreshOrgList() {
        $.table.search('form-operational', 'bootstrap-table-operational')
    }

    //修改国防单位弹框
    function editOrg(orgCode) {
        if (orgCode == null || orgCode == '' || orgCode == undefined) {
            let optionsRow = $("#" + table.options.id).bootstrapTable('getSelections')[0];//获取选中行数据
            orgCode = optionsRow.orgCode;
        }
        $.modal.openTab('修改国防单位', prefix + "/edit/" + orgCode);
    }

    //Excel导入
    function importExcel() {
        let options = {
            title: '国防单位导入',
            width: "1200",
            height: "700",
            url: organization + "/importExcel/operational",
            btn: 0,
            callBack: doSubmit
        };
        $.modal.openOptions(options);
    }

    function doSubmit(index, layero) {
        location.reload();
    }

    //导出
    function exportExcel() {
        let orgCodes = $.table.selectColumns("orgCode");
        $.modal.loading("正在导出数据，请稍后...");
        $.ajax({
            type: "POST",
            url: prefix + "/exportExcel",
            data: {
                'orgCodes': orgCodes.toString()
            },
            success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                } else if (result.code == web_status.WARNING) {
                    $.modal.alertWarning(result.msg)
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            }
        })
    }

    //删除机构
    function remove(orgCode) {
        let orgCodes;
        if (orgCode) {
            orgCodes = orgCode;
        } else {
            //批量删除
            let optionsRow = $("#" + table.options.id).bootstrapTable('getSelections');//获取所有选中行数据
            for (let i = 0; i < optionsRow.length; i++) {
                let org = optionsRow[i];
                if (i == 0) {
                    orgCodes = org.orgCode;
                } else {
                    orgCodes += ',' + org.orgCode;
                }
            }
        }
        if (orgCodes) {
            $.modal.confirm("确定删除选中的国防单位吗？", function () {
                $.ajax({
                    type: "POST",
                    url: organization + "/removeOrgByOrgCode",
                    data: {
                        'orgCodes': orgCodes
                    },
                    success: function (result) {
                        if (result.code == web_status.SUCCESS) {
                            refreshOrgList()
                            $.modal.msgSuccess("操作成功");
                        } else {
                            $.modal.alertError(result.msg);
                        }
                    },
                });
            });
        } else {
            $.modal.alert("请先选择数据");
        }
    }

</script>
</body>
</html>