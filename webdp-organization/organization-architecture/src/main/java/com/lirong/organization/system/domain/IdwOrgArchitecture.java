package com.lirong.organization.system.domain;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.lirong.common.annotation.AutoId;
import com.lirong.common.annotation.Excel;
import com.lirong.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 机构架构对象 idw_org_architecture
 *
 * <AUTHOR>
 * @date 2023-11-07
 */
public class IdwOrgArchitecture extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 架构ID
     */
    @AutoId
    @JsonSerialize(using = ToStringSerializer.class)
    private Long architectureId;

    /**
     * 上级ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    /**
     * 上级名称
     */
    @Excel(name = "上级名称")
    private String parentName;

    /**
     * 祖籍列表
     */
    @Excel(name = "祖籍列表")
    private String ancestors;

    /**
     * 层级
     */
    @Excel(name = "层级")
    private Integer level;

    /**
     * 机构编码
     */
    @Excel(name = "机构编码")
    private String orgCode;

    /**
     * 节点机构编码
     */
    @Excel(name = "节点机构编码")
    private String nodeOrgCode;

    /**
     * 中文名称
     */
    @Excel(name = "中文名称")
    private String nameCn;

    /**
     * 英文名称
     */
    @Excel(name = "英文名称")
    private String nameEn;

    /**
     * 职能
     */
    @Excel(name = "职能")
    private String function;

    /**
     * 类型（部门/机构）
     */
    @Excel(name = "类型", readConverterExp = "部门|机构")
    private String type;

    /**
     * 删除标志（0代表存在 1代表删除）
     */
    private Integer isDelete;

    public void setArchitectureId(Long architectureId) {
        this.architectureId = architectureId;
    }

    public Long getArchitectureId() {
        return architectureId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Long getParentId() {
        return parentId;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    public String getAncestors() {
        return ancestors;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Integer getLevel() {
        return level;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setNodeOrgCode(String nodeOrgCode) {
        this.nodeOrgCode = nodeOrgCode;
    }

    public String getNodeOrgCode() {
        return nodeOrgCode;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setFunction(String function) {
        this.function = function;
    }

    public String getFunction() {
        return function;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("architectureId", getArchitectureId())
                .append("parentId", getParentId())
                .append("parentName", getParentName())
                .append("ancestors", getAncestors())
                .append("level", getLevel())
                .append("orgCode", getOrgCode())
                .append("nodeOrgCode", getNodeOrgCode())
                .append("nameCn", getNameCn())
                .append("nameEn", getNameEn())
                .append("function", getFunction())
                .append("type", getType())
                .append("isDelete", getIsDelete())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
