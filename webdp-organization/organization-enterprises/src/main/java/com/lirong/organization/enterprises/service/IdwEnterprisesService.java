package com.lirong.organization.enterprises.service;

import com.lirong.organization.enterprises.domain.IdwEnterprises;

import java.util.List;
import java.util.Map;

/**
 * 国防企业Service接口
 *
 * <AUTHOR>
 * @date 2021-03-17
 */
public interface IdwEnterprisesService {

    /**
     * 导入校验数据格式
     *
     * @param enterprisesList  机构数据列表
     * @param filePathIndexMap key 文件在压缩包中的相对路径 value 文件对应在filePathList中的索引
     * @param filePathList     上传后的文件路径
     * @param baseDir          临时文件夹目录
     * @param updateSupport    是否更新
     * @return 结果
     */
    public List<String> verifyImportEnterprises(List<IdwEnterprises> enterprisesList, Map<String, Integer> filePathIndexMap, List<String> filePathList, String baseDir, boolean updateSupport);


    /**
     * 导入国防企业
     *
     * @param updateSupport 是否支持更新, 如果已存在, 则进行更新
     * @param operName      操作用户
     * @return 结果
     */
    public String importEnterprises(boolean updateSupport, String operName);

    /**
     * 机构相关文件上传保存
     *
     * @param fileName 文件名称
     * @param fileUrl  文件路径
     * @return 结果
     */
    public String saveBatchUploadFile(String fileName, String fileUrl);

    /**
     * 根据机构编码查询
     *
     * @param orgCodes 机构编码
     * @return 结果
     */
    public List<IdwEnterprises> selectEnterprisesByOrgCodes(String[] orgCodes);
}
