package com.lirong.organization.financial.controller;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.lirong.common.annotation.Log;
import com.lirong.common.enums.BusinessType;
import com.lirong.organization.financial.domain.IdwOrgFinancial;
import com.lirong.organization.financial.service.IdwOrgFinancialService;
import com.lirong.common.core.controller.BaseController;
import com.lirong.common.core.domain.AjaxResult;
import com.lirong.common.utils.poi.ExcelUtil;
import com.lirong.common.core.page.TableDataInfo;

/**
 * 机构财务状况Controller
 *
 * <AUTHOR>
 * @date 2021-02-03
 */
@Controller
@RequestMapping("/organization/financial")
public class IdwOrgFinancialController extends BaseController {
    public static final Logger log = LoggerFactory.getLogger(IdwOrgFinancialController.class);

    private String prefix = "organization/financial";

    @Autowired
    private IdwOrgFinancialService idwOrgFinancialService;

    @RequiresPermissions("organization:financial:view")
    @GetMapping("/{orgCode}")
    public String financialList(@PathVariable("orgCode") String orgCode, ModelMap mmap) {
        try {
            orgCode = URLDecoder.decode(orgCode, "utf-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            log.info("机构产品&研究成果跳转列表页解析参数失败！");
        }
        mmap.put("orgCode", orgCode);
        return prefix + "/financialList";
    }

    /**
     * 查询机构财务状况列表
     */
    @RequiresPermissions("organization:financial:list")
    @PostMapping("/financialList")
    @ResponseBody
    public TableDataInfo financialList(IdwOrgFinancial idwOrgFinancial) {
        startPage();
        List<IdwOrgFinancial> list = idwOrgFinancialService.selectIdwOrgFinancialList(idwOrgFinancial);
        return getDataTable(list);
    }

    /**
     * 新增机构财务状况
     */
    @GetMapping("/addFinancial/{orgCode}")
    public String addProduct(@PathVariable("orgCode") String orgCode, ModelMap mmap) {
        try {
            orgCode = URLDecoder.decode(orgCode, "utf-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
            log.info("机构财务状况跳转新增页面解析机构编码失败！");
        }
        mmap.put("orgCode", orgCode);
        return prefix + "/addFinancial";
    }

    /**
     * 新增保存机构财务状况
     */
    @RequiresPermissions("organization:financial:add")
    @Log(title = "机构财务状况", businessType = BusinessType.INSERT)
    @PostMapping("/addFinancial")
    @ResponseBody
    public AjaxResult addFinancialSave(IdwOrgFinancial idwOrgFinancial) {
        return toAjax(idwOrgFinancialService.insertIdwOrgFinancial(idwOrgFinancial));
    }

    /**
     * 修改机构财务状况
     */
    @GetMapping("/editFinancial/{financialId}")
    public String editFinancial(@PathVariable("financialId") Long financialId, ModelMap mmap) {
        IdwOrgFinancial idwOrgFinancial = idwOrgFinancialService.selectIdwOrgFinancialById(financialId);
        mmap.put("idwOrgFinancial", idwOrgFinancial);
        return prefix + "/editFinancial";
    }

    /**
     * 修改保存机构财务状况
     */
    @RequiresPermissions("organization:financial:edit")
    @Log(title = "机构财务状况", businessType = BusinessType.UPDATE)
    @PostMapping("/editFinancial")
    @ResponseBody
    public AjaxResult editFinancialSave(IdwOrgFinancial idwOrgFinancial) {
        return toAjax(idwOrgFinancialService.updateIdwOrgFinancial(idwOrgFinancial));
    }

    /**
     * 删除机构财务状况
     */
    @RequiresPermissions("organization:financial:remove")
    @Log(title = "机构财务状况", businessType = BusinessType.DELETE)
    @PostMapping("/removeFinancial")
    @ResponseBody
    public AjaxResult removeFinancial(String ids) {
        return toAjax(idwOrgFinancialService.deleteIdwOrgFinancialByIds(ids));
    }

    /**
     * 导出机构财务状况列表
     */
    @RequiresPermissions("organization:financial:export")
    @Log(title = "机构财务状况", businessType = BusinessType.EXPORT)
    @PostMapping("/exportFinancial")
    @ResponseBody
    public AjaxResult exportFinancial(IdwOrgFinancial idwOrgFinancial) {
        List<IdwOrgFinancial> list = idwOrgFinancialService.selectIdwOrgFinancialList(idwOrgFinancial);
        ExcelUtil<IdwOrgFinancial> util = new ExcelUtil<IdwOrgFinancial>(IdwOrgFinancial.class);
        return util.exportExcel(list, "financial");
    }

    /**
     * 采集机构财务数据
     *
     * @param orgCode    机构编码
     * @param reutersKey 路透社机构唯一表示
     * @param year       年度
     * @return 结果
     */
    @PostMapping(value = {"/crawlerFinancial/{orgCode}/{reutersKey}", "/crawlerFinancial/{orgCode}/{reutersKey}/{year}"})
    @ResponseBody
    public AjaxResult crawlerFinancial(@PathVariable("orgCode") String orgCode, @PathVariable("reutersKey") String reutersKey, @PathVariable(value = "year", required = false) Integer year) {
        idwOrgFinancialService.crawlerFinancial(orgCode, reutersKey, year);
        return AjaxResult.success();
    }

    /**
     * 按年度抓取财务数据
     *
     * @param year
     * @return
     */
    @GetMapping(value = {"/crawlerFinancial", "/crawlerFinancial/{year}"})
    @ResponseBody
    public AjaxResult crawlerFinancial(@PathVariable(value = "year", required = false) Integer year) {
        idwOrgFinancialService.crawlerFinancial(year);
        return AjaxResult.success();
    }

}
