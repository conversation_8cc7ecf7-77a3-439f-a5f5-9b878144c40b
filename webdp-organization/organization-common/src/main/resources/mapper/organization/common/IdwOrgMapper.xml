<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lirong.organization.common.mapper.IdwOrgMapper">

    <resultMap type="com.lirong.organization.common.domain.IdwOrg" id="IdwOrgResult">
        <result property="orgId"    column="org_id"    />
        <result property="orgCode"    column="org_code"    />
        <result property="country"    column="country"    />
        <result property="orgNameCn"    column="org_name_cn"    />
        <result property="orgNameEn"    column="org_name_en"    />
        <result property="shortName"    column="short_name"    />
        <result property="industry"    column="industry"    />
        <result property="avatar"    column="avatar"    />
        <result property="establishTime"    column="establish_time"    />
        <result property="address"    column="address"    />
        <result property="addressEn"    column="address_en"    />
        <result property="telephone"    column="telephone"    />
        <result property="email"    column="email"    />
        <result property="profileCn"    column="profile_cn"    />
        <result property="profileEn"    column="profile_en"    />
        <result property="mainFunctions"    column="main_functions"    />
        <result property="area"    column="area"    />
        <result property="founder"    column="founder"    />
        <result property="facilities"    column="facilities"    />
        <result property="officialWebsite"    column="official_website"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="orgType"    column="org_type"    />
        <result property="orgTypeAlias"    column="org_type_alias"    />
        <result property="leader"    column="leader"    />
        <result property="leaderProfile"    column="leader_profile"    />
        <result property="troopsCategory"    column="troops_category"    />
        <result property="peopleCount"    column="people_count"    />
        <result property="baseEstablishmentRatio"    column="base_establishment_ratio"    />
        <result property="reutersKey"    column="reuters_key"    />
        <result property="tags"    column="tags"    />
        <result property="field"    column="field"    />
        <result property="uei"    column="uei"    />
        <result property="cybersecurityRating"    column="cybersecurity_rating"    />
        <result property="supplyChainsGrade"    column="supply_chains_grade"    />
        <result property="showHome"    column="show_home"    />
        <result property="orderNum"    column="order_num"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="source"    column="source"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <resultMap type="com.lirong.organization.common.vo.SimpleOrg" id="SimpleOrg">
        <result property="orgCode"    column="org_code"    />
        <result property="country"    column="country"    />
        <result property="orgNameCn"    column="org_name_cn"    />
        <result property="orgNameEn"    column="org_name_en"    />
        <result property="avatar"    column="avatar"    />
        <result property="profileCn"    column="profile_cn"    />
        <result property="profileEn"    column="profile_en"    />
        <result property="orgType"    column="org_type"    />
    </resultMap>

    <resultMap type="com.lirong.common.core.domain.Ztree" id="OrgTree">
        <result property="id"    column="id"    />
        <result property="pId"    column="pid"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="relatedCode"    column="related_code"    />
        <result property="name"    column="name"    />
        <result property="nameEn"    column="name_en"    />
        <result property="orgType"    column="org_type"    />
        <result property="isParent"    column="is_parent"    />
        <result property="open"    column="open"    />
        <result property="nocheck"    column="nocheck"    />
        <result property="country"    column="country"    />
    </resultMap>

    <select id="selectIdwOrgList" parameterType="com.lirong.organization.common.domain.IdwOrg" resultMap="IdwOrgResult">
        SELECT
            org_code,
            country,
            org_name_cn,
            org_name_en,
            avatar,
            establish_time,
            address,
            source,
            update_by,
            update_time
        FROM
            idw_org
        WHERE
            is_delete = 0
        <if test="country != null and country != ''"> AND country = #{country}</if>
        <if test="orgType != null and orgType != ''"> AND org_type = #{orgType}</if>
        <if test="orgNameCn != null  and orgNameCn != ''">
            AND (
                org_name_cn LIKE concat('%', #{orgNameCn}, '%') or org_name_en LIKE CONCAT('%', #{orgNameCn}, '%')
            )
        </if>
        <if test="orgNameEn != null  and orgNameEn != ''">
            AND (
                org_name_cn LIKE concat('%', #{orgNameEn}, '%') or org_name_en LIKE CONCAT('%', #{orgNameEn}, '%')
            )
        </if>
        order by order_num
    </select>

    <select id="selectOrgByOrgCode" resultMap="IdwOrgResult">
        SELECT
            *
        FROM
            idw_org
        WHERE
            is_delete = 0
            AND org_code = #{orgCode}
    </select>

    <!--获取传入分类最大的排序号-->
    <select id="selectMaxOrderNum" resultType="java.lang.Integer">
        SELECT
            IF( MAX( order_num ) IS NULL, 0, MAX( order_num ) ) AS order_num
        FROM
            idw_org
        WHERE
            is_delete = 0
            AND org_type = #{orgType}
    </select>

    <!--根据关键字和机构类型(包含)查询机构信息 like查询名称(中英文名称&机构编码)-->
    <select id="selectByKeywordAndIncludeOrgTypes" resultMap="SimpleOrg">
        SELECT
            org_code,
            country,
            org_name_cn,
            org_name_en,
            avatar,
            profile_cn,
            profile_en,
            org_type
        FROM
            idw_org
        WHERE
            is_delete = 0
        <if test="keyword != null and keyword != ''">
            AND (
                org_name_cn LIKE CONCAT( '%',#{keyword}, '%' )
                OR org_name_en LIKE CONCAT( '%', #{keyword}, '%' )
                OR  org_code LIKE CONCAT( '%', #{keyword}, '%' )
            )
        </if>
        <if test="orgTypes != null and orgTypes.length > 0">
            AND org_type in
            <foreach item="orgType" collection="orgTypes" open="(" separator="," close=")">
                #{orgType}
            </foreach>
        </if>
        ORDER BY order_num ASC
            LIMIT 20
    </select>


    <!--根据机构类型与机构名称查询-->
    <select id="selectByOrgTypesAndOrgName" resultMap="IdwOrgResult">
        SELECT
            *
        FROM
            idw_org
        WHERE
            is_delete = 0
            AND (
                org_name_cn LIKE CONCAT( '%',#{orgName}, '%' )
                OR org_name_en LIKE CONCAT( '%', #{orgName}, '%' )
            )
        <if test="orgTypes != null and orgTypes.length > 0">
            AND org_type IN
                <foreach item="orgType" collection="orgTypes" open="(" separator="," close=")">
                    #{orgType}
                </foreach>
        </if>
    </select>

    <!--根据机构编码和机构中英文名称查询-->
    <select id="selectOrgByOrgName" resultMap="IdwOrgResult">
         SELECT
            *
        FROM
            idw_org
        WHERE
            is_delete = 0
            AND (
        <if test="orgNameCn != null and orgNameCn != ''">
                org_name_cn = #{orgNameCn}
                OR org_name_en = #{orgNameCn}
        <if test="orgNameEn != null and orgNameEn != ''">
                OR org_name_cn = #{orgNameEn}
                OR org_name_en = #{orgNameEn}
        </if>
            )
        </if>
    </select>

    <!--根据头像与当前登录用户查询-->
    <select id="selectCountByAvatar" resultType="java.lang.Integer">
        SELECT
            COUNT( * ) AS count
        FROM
            idw_org
        WHERE
            is_delete = 0
            AND avatar = #{avatar}
            AND (
                create_by = #{userName}
                OR update_by = #{userName}
            )
    </select>

    <!--根据机构名称/别名查询机构编码-->
    <select id="selectOrgCodetByTags" resultType="java.lang.String">
        SELECT
            org_code
        FROM
            idw_org
        WHERE
            is_delete = 0
            AND FIND_IN_SET( #{tag}, tags )
    </select>

    <!--根据条件查询机构总数-->
    <select id="selectCountExcludeCreateUser" resultType="java.lang.Long">
        SELECT
            COUNT( * ) AS count
        FROM
            idw_org
        WHERE
            is_delete = 0
        <if test="createUserNotIs != null and createUserNotIs.length > 0">
            AND create_by NOT IN
            <foreach item="createUser" collection="createUserNotIs" open="(" separator="," close=")">
                #{createUser}
            </foreach>
        </if>
    </select>

    <!--根据机构类型统计-->
    <select id="loadOrgStatisticsByOrgTypeExcludeCreateUser" resultType="com.lirong.common.vo.StatisticsVO">
        SELECT
            org_type AS name,
            COUNT( * ) AS value
        FROM
            idw_org
        WHERE
            is_delete = 0
        <if test="createUserNotIs != null and createUserNotIs.length > 0">
            AND create_by NOT IN
            <foreach item="createUser" collection="createUserNotIs" open="(" separator="," close=")">
                #{createUser}
            </foreach>
        </if>
        GROUP BY
            org_type
    </select>

    <!--查询所有机构类型-->
    <select id="selectOrgTypeExcludeCreateUser" resultType="java.lang.String">
        SELECT
            org_type
        FROM
            idw_org
        WHERE
            is_delete = 0
        <if test="createUserNotIs != null and createUserNotIs.length > 0">
            AND create_by NOT IN
            <foreach item="createUser" collection="createUserNotIs" open="(" separator="," close=")">
                #{createUser}
            </foreach>
        </if>
        GROUP BY
            org_type
    </select>

    <!--获取所有文件路径-->
    <select id="selectAllFilePath" resultType="java.lang.String">
        SELECT
            avatar
        FROM
            idw_org
        WHERE
            is_delete = 0
            AND avatar IS NOT NULL
            AND avatar != ''
    </select>

    <!--根据多机构编码查询-->
    <select id="selectOrgByOrgCodes" resultMap="IdwOrgResult">
        SELECT
            *
        FROM
            idw_org
        WHERE
            is_delete = 0
            AND org_code in
        <foreach item="orgCode" collection="orgCodes" open="(" separator="," close=")">
            #{orgCode}
        </foreach>
    </select>

    <select id="selectAllReutersOrg" resultMap="IdwOrgResult">
        SELECT
            org_code,
            reuters_key
        FROM
            idw_org
        WHERE
            is_delete = 0
            AND reuters_key != ''
    </select>

    <!--查询机构员工最大数量-->
    <select id="selectMaxPeopleCount" resultType="java.lang.Integer">
        SELECT
            MAX( people_count ) AS max_people_count
        FROM
            idw_org
        WHERE
            is_delete = 0
            AND participate_ranking = TRUE
    </select>

    <!--查询机构员工最小数量-->
    <select id="selectMinPeopleCount" resultType="java.lang.Integer">
        SELECT
            MIN( people_count ) AS min_people_count
        FROM
            idw_org
        WHERE
            is_delete = 0
            AND participate_ranking = TRUE
    </select>

    <!--查询机构合同最大数量-->
    <select id="selectMaxContractCount" resultType="java.lang.Integer">
        SELECT
            MAX( c.count ) AS max_contract_count
        FROM
            (
            SELECT
                recipient_uei,
                COUNT( * ) AS count
            FROM
                contract_prime
            WHERE
                recipient_uei IN ( SELECT uei FROM idw_org WHERE is_delete = 0 AND participate_ranking = TRUE AND uei IS NOT NULL AND uei != '' )
                OR awarding_agency_code IN ( SELECT uei FROM idw_org WHERE is_delete = 0 AND participate_ranking = TRUE AND uei IS NOT NULL AND uei != '' )
                OR awarding_office_code IN ( SELECT uei FROM idw_org WHERE is_delete = 0 AND participate_ranking = TRUE AND uei IS NOT NULL AND uei != '' )
            GROUP BY
                recipient_uei
            ) c
    </select>

    <!--查询机构合同最小数量-->
    <select id="selectMinContractCount" resultType="java.lang.Integer">
        SELECT
            MIN( c.count ) AS min_contract_count
        FROM
            (
            SELECT
                recipient_uei,
                COUNT( * ) AS count
            FROM
                contract_prime
            WHERE
                recipient_uei IN ( SELECT uei FROM idw_org WHERE is_delete = 0 AND participate_ranking = TRUE AND uei IS NOT NULL AND uei != '' )
                OR awarding_agency_code IN ( SELECT uei FROM idw_org WHERE is_delete = 0 AND participate_ranking = TRUE AND uei IS NOT NULL AND uei != '' )
                OR awarding_office_code IN ( SELECT uei FROM idw_org WHERE is_delete = 0 AND participate_ranking = TRUE AND uei IS NOT NULL AND uei != '' )
            GROUP BY
                recipient_uei
            ) c
    </select>

    <!--根据机构UEi编码查询合同数量-->
    <select id="selectContractCounByUei" resultType="java.lang.Integer">
        SELECT
            COUNT( * ) AS count
        FROM
            contract_prime
        WHERE
            recipient_uei = #{uei}
    </select>

    <!--查询机构获奖产品最大数量-->
    <select id="selectMaxAwardCount" resultType="java.lang.Integer">
        SELECT
            MAX( a.count ) AS max_award_count
        FROM
            (
            SELECT
                name,
                COUNT( * ) AS count
            FROM
                rd_award_org
            WHERE
                name IN ( SELECT org_name_en FROM idw_org WHERE is_delete = 0 AND participate_ranking = TRUE )
             GROUP BY
                name
        ) a
    </select>

    <!--查询机构获奖产品最小数量-->
    <select id="selectMinAwardCount" resultType="java.lang.Integer">
        SELECT
            MIN( a.count ) AS min_award_count
        FROM
            (
            SELECT
                name,
                COUNT( * ) AS count
            FROM
                rd_award_org
            WHERE
                name IN ( SELECT org_name_en FROM idw_org WHERE is_delete = 0 AND participate_ranking = TRUE )
             GROUP BY
                name
            ) a
    </select>

    <!--根据机构英文名称查询获奖产品数量-->
    <select id="selectAwardCountByOrgName" resultType="java.lang.Integer">
        SELECT
            COUNT( * ) AS count
        FROM
            rd_award_org
        WHERE
            NAME = #{orgNameEn}
    </select>

    <!--查询排名机构-->
    <select id="selectScoreOrgList" resultMap="IdwOrgResult">
        SELECT
            *
        FROM
            idw_org
        WHERE
            is_delete = 0
            AND participate_ranking = TRUE
        <if test="isOrder">
            ORDER BY
                score DESC
        </if>
    </select>

    <!--查询机构发明专利最大数量-->
    <select id="selectMaxPatentCount" resultType="java.lang.Integer">
        SELECT
            MAX( p.count ) AS max_patent_count
        FROM
            (
            SELECT
                name,
                COUNT( * ) AS count
            FROM
                patent_org
            WHERE
                name IN ( SELECT org_name_en FROM idw_org WHERE is_delete = 0 AND participate_ranking = TRUE )
            GROUP BY
                name
            ) p
    </select>

    <!--查询机构发明专利最小数量-->
    <select id="selectMinPatentCount" resultType="java.lang.Integer">
        SELECT
            MIN( p.count ) AS min_patent_count
        FROM
            (
            SELECT
                name,
                COUNT( * ) AS count
            FROM
                patent_org
            WHERE
                name IN ( SELECT org_name_en FROM idw_org WHERE is_delete = 0 AND participate_ranking = TRUE )
            GROUP BY
                name
            ) p
    </select>

    <!--根据机构英文名称查询机构发明专利数量-->
    <select id="selectPatentCount" resultType="java.lang.Integer">
        SELECT
            COUNT( * ) AS count
        FROM
            patent_org
        WHERE
            name = #{orgNameEn}
    </select>

    <!--查询机构机构文献最大数量-->
    <select id="selectMaxOrganizationDocumentCount" resultType="java.lang.Integer">
        SELECT
            MAX( d.count ) AS max_document_count
        FROM
            (
            SELECT
                org_code,
                COUNT( * ) AS count
            FROM
                idw_org_document
            WHERE
                is_delete = 0
                AND org_code IN ( SELECT org_code FROM idw_org WHERE is_delete = 0 AND participate_ranking = TRUE )
            GROUP BY
            org_code
            ) d
    </select>

    <!--查询机构机构文献最小数量-->
    <select id="selectMinOrganizationDocumentCount" resultType="java.lang.Integer">
        SELECT
            MIN( d.count ) AS min_document_count
        FROM
            (
            SELECT
                org_code,
                COUNT( * ) AS count
            FROM
                idw_org_document
            WHERE
                is_delete = 0
                AND org_code IN ( SELECT org_code FROM idw_org WHERE is_delete = 0 AND participate_ranking = TRUE )
            GROUP BY
            org_code
            ) d
    </select>

    <!--根据机构编码查询机构文献数量-->
    <select id="selectOrganizationDocumentCountByOrgCode" resultType="java.lang.Integer">
        SELECT
            COUNT( * ) AS count
        FROM
            idw_org_document
        WHERE
            is_delete = 0
            AND org_code = #{orgCode}
    </select>

    <!--查询机构机构研究领域最大数量-->
    <select id="selectMaxOrganizationResearchFieldCount" resultType="java.lang.Integer">
        SELECT
            MAX( count ) AS max_org_research_field_count
        FROM
            (
            SELECT
                COUNT( * ) AS count
            FROM
                idw_org_research_field
            WHERE
                is_delete = 0
                AND org_code IN ( SELECT org_code FROM idw_org WHERE is_delete = 0 AND participate_ranking = TRUE )
            GROUP BY
                org_code
            ) f
    </select>

    <!--查询机构机构研究领域最小数量-->
    <select id="selectMinOrganizationResearchFieldCount" resultType="java.lang.Integer">
        SELECT
            MIN( count ) AS min_org_research_field_count
        FROM
            (
            SELECT
                COUNT( * ) AS count
            FROM
                idw_org_research_field
            WHERE
                is_delete = 0
                AND org_code IN ( SELECT org_code FROM idw_org WHERE is_delete = 0 AND participate_ranking = TRUE )
            GROUP BY
                org_code
            ) f
    </select>

    <!--根据机构编码查询机构研究领域数量-->
    <select id="selectOrganizationResearchFieldCountByOrgCode" resultType="java.lang.Integer">
        SELECT
            COUNT( * ) AS count
        FROM
            idw_org_research_field
        WHERE
            is_delete = 0
            AND org_code = #{orgCode}
    </select>

    <resultMap type="com.lirong.organization.common.vo.SupplyChainsAnalysisVO" id="SupplyChainsAnalysisVOResult">
        <result property="orgCode"    column="org_code"    />
        <result property="amount"    column="amount"    />
        <result property="contractCount"    column="contract_count"    />
        <result property="weaponryCount"    column="weaponry_count"    />
    </resultMap>

    <!--查询机构供应链排名列表-->
    <select id="selectUupplyChainsAnalysis" resultMap="SupplyChainsAnalysisVOResult">
        SELECT
            o.org_code,
            c.amount,
            c.contract_count,
            w.weaponry_count
        FROM
            idw_org o
            LEFT JOIN (
            SELECT
                contractor_parent_uei,
                COUNT( * ) AS contract_count,
                SUM( amount ) AS amount
            FROM
                idw_contract
            WHERE
                is_delete = 0
                AND contractor_parent_uei IS NOT NULL
                AND contractor_parent_uei != ''
            GROUP BY
                contractor_parent_uei
            ) c ON c.contractor_parent_uei = o.uei
            LEFT JOIN (
            SELECT
                c.contractor_parent_uei,
                COUNT( * ) AS weaponry_count
            FROM
                ( SELECT contractor_parent_uei, weaponry_code FROM idw_contract WHERE is_delete = 0 AND contractor_parent_uei IS NOT NULL AND contractor_parent_uei != '' GROUP BY contractor_parent_uei, weaponry_code ) c
            GROUP BY
                c.contractor_parent_uei
            ) w ON w.contractor_parent_uei = o.uei
        WHERE
            o.is_delete = 0
            AND o.show_home =TRUE
        ORDER BY
            c.amount DESC,
            c.contract_count DESC,
            w.weaponry_count DESC
    </select>

    <!--根据机构编码查询参与装备结构关键节点数量-->
    <select id="selectParticipateKeyNodeCountByOrgCode" resultType="java.lang.Integer">
        SELECT
            SUM( c.count ) AS count
        FROM
            (
            SELECT
                org_code,
                structure_code,
                COUNT( * ) AS count
            FROM
                idw_weaponry_contractor
            WHERE
                is_delete = 0
                AND org_code IS NOT NULL
                AND org_code != ''
                AND ( structure_code = '0' OR structure_code IN ( SELECT structure_code FROM idw_weaponry_structure WHERE is_delete = 0 AND participate_key_node = 1 ) )
            GROUP BY
                structure_code,
                org_code
            ) c
        WHERE
            org_code = #{orgCode}
        GROUP BY
            c.org_code
    </select>

    <!--查询供应链机构最小合同金额-->
    <select id="selectSupplyChainsMinAmount" resultType="java.math.BigDecimal">
        SELECT
            MIN( contract.amount ) AS min_amount
        FROM
            (
            SELECT
                contractor_parent_uei,
                SUM( amount ) AS amount
            FROM
                idw_contract
            WHERE
                is_delete = 0
                AND contractor_parent_uei IN ( SELECT uei FROM idw_org WHERE is_delete = 0 AND uei IS NOT NULL AND uei != '' )
            GROUP BY
            contractor_parent_uei
            ) contract
    </select>

    <!--查询供应链机构最大合同金额-->
    <select id="selectSupplyChainsMaxAmount" resultType="java.math.BigDecimal">
         SELECT
            MAX( contract.amount ) AS MAX
        FROM
            (
            SELECT
                contractor_parent_uei,
                SUM( amount ) AS amount
            FROM
                idw_contract
            WHERE
                is_delete = 0
                AND contractor_parent_uei IN ( SELECT uei FROM idw_org WHERE is_delete = 0 AND uei IS NOT NULL AND uei != '' )
            GROUP BY
                contractor_parent_uei
            ) contract
    </select>

    <!--查询供应链机构最小合同数量-->
    <select id="selectSupplyChainsMinContractCount" resultType="java.lang.Integer">
        SELECT
            MIN( contract.count ) AS min_contract_count
        FROM
            (
            SELECT
                contractor_parent_uei,
                COUNT( * ) AS count
            FROM
                idw_contract
            WHERE
                is_delete = 0
                AND contractor_parent_uei IN ( SELECT uei FROM idw_org WHERE is_delete = 0 AND uei IS NOT NULL AND uei != '' )
            GROUP BY
            contractor_parent_uei
            ) contract
    </select>

    <!--查询供应链机构最大合同数量-->
    <select id="selectSupplyChainsMaxContractCount" resultType="java.lang.Integer">
        SELECT
            MAX( contract.count ) AS max_contract_count
        FROM
            (
            SELECT
                contractor_parent_uei,
                COUNT( * ) AS count
            FROM
                idw_contract
            WHERE
                is_delete = 0
                AND contractor_parent_uei IN ( SELECT uei FROM idw_org WHERE is_delete = 0 AND uei IS NOT NULL AND uei != '' )
            GROUP BY
                contractor_parent_uei
            ) contract
    </select>

    <!--查询供应链机构最小装备数量-->
    <select id="selectSupplyChainsMinWeaponryCount" resultType="java.lang.Integer">
        SELECT
            MIN( contract.count ) AS min_weaponry_count
        FROM
            (
            SELECT
                COUNT( * ) AS count
            FROM
                (
                SELECT
                    contractor_parent_uei,
                    weaponry_code
                FROM
                    idw_contract
                WHERE
                    is_delete = 0
                    AND contractor_parent_uei IN ( SELECT uei FROM idw_org WHERE is_delete = 0 AND uei IS NOT NULL AND uei != '' )
                GROUP BY
                    contractor_parent_uei,
                    weaponry_code
                ) c
            GROUP BY
                c.contractor_parent_uei
            ) contract
    </select>

    <!--查询供应链机构最大装备数量-->
    <select id="selectSupplyChainsMaxWeaponryCount" resultType="java.lang.Integer">
        SELECT
            MAX( contract.count ) AS max_weaponry_count
        FROM
            (
            SELECT
                COUNT( * ) AS count
            FROM
                (
                SELECT
                    contractor_parent_uei,
                    weaponry_code
                FROM
                    idw_contract
                WHERE
                    is_delete = 0
                    AND contractor_parent_uei IN ( SELECT uei FROM idw_org WHERE is_delete = 0 AND uei IS NOT NULL AND uei != '' )
                GROUP BY
                    contractor_parent_uei,
                    weaponry_code
                ) c
            GROUP BY
                c.contractor_parent_uei
            ) contract
    </select>

    <!--查询供应链机构最小参与关键节点数量-->
    <select id="selectSupplyChainsMinParticipateKeyNodeCount" resultType="java.lang.Integer">
        SELECT
            MIN( c.count ) AS min_participate_key_node_count
        FROM
            (
            SELECT
                COUNT( * ) AS count
            FROM
                idw_weaponry_contractor
            WHERE
                is_delete = 0
                AND org_code IS NOT NULL
                AND org_code != ''
                AND ( structure_code = '0' OR structure_code IN ( SELECT structure_code FROM idw_weaponry_structure WHERE is_delete = 0 AND participate_key_node = 1 ) )
            GROUP BY
                structure_code,
                org_code
            ) c
    </select>

    <!--查询供应链机构最大参与关键节点数量-->
    <select id="selectSupplyChainsMaxParticipateKeyNodeCount" resultType="java.lang.Integer">
        SELECT
            MAX( c.count ) AS max_participate_key_node_count
        FROM
            (
            SELECT
                COUNT( * ) AS count
            FROM
                idw_weaponry_contractor
            WHERE
                is_delete = 0
                AND org_code IS NOT NULL
                AND org_code != ''
                AND ( structure_code = '0' OR structure_code IN ( SELECT structure_code FROM idw_weaponry_structure WHERE is_delete = 0 AND participate_key_node = 1 ) )
            GROUP BY
                structure_code,
                org_code
            ) c
    </select>

    <!--查询机构总数-->
    <select id="selectOrgCount" resultType="java.lang.Long">
        SELECT
            COUNT( * ) AS count
        FROM
            idw_org
        WHERE
            is_delete = 0
            AND org_code IN ( SELECT org_code FROM idw_org WHERE is_delete = 0 )
    </select>

    <!--根据机构编码查询当前发布机构最大版本-->
    <select id="selectOrgPubMaxVersionsByOrgCode" resultType="java.lang.Integer">
        SELECT
            IFNULL( MAX( version ), 0 ) AS max_version
        FROM
            idw_pub_org
        WHERE
            drgo_org_code = #{orgCode}
            OR org_code = #{orgCode}
    </select>

    <insert id="insertIdwOrg" parameterType="com.lirong.organization.common.domain.IdwOrg" useGeneratedKeys="true" keyProperty="orgId">
        insert into idw_org
        <trim prefix="(" suffix=")" suffixOverrides=",">
            is_delete,
            <if test="orgId != null">org_id,</if>
            <if test="orgCode != null and orgCode != ''">org_code,</if>
            <if test="country != null and country != ''">country,</if>
            <if test="orgNameCn != null and orgNameCn != ''">org_name_cn,</if>
            <if test="orgNameEn != null">org_name_en,</if>
            <if test="shortName != null">short_name,</if>
            <if test="industry != null">industry,</if>
            <if test="avatar != null">avatar,</if>
            <if test="establishTime != null">establish_time,</if>
            <if test="address != null">address,</if>
            <if test="addressEn != null">address_en,</if>
            <if test="telephone != null">telephone,</if>
            <if test="email != null">email,</if>
            <if test="profileCn != null">profile_cn,</if>
            <if test="profileEn != null">profile_en,</if>
            <if test="mainFunctions != null">main_functions,</if>
            <if test="area != null">area,</if>
            <if test="founder != null">founder,</if>
            <if test="facilities != null">facilities,</if>
            <if test="officialWebsite != null">official_website,</if>
            <if test="longitude != null and longitude != ''">longitude,</if>
            <if test="latitude != null and latitude != ''">latitude,</if>
            <if test="orgType != null and orgType != ''">org_type,</if>
            <if test="orgTypeAlias != null and orgTypeAlias != ''">org_type_alias,</if>
            <if test="leader != null">leader,</if>
            <if test="leaderProfile != null">leader_profile,</if>
            <if test="troopsCategory != null">troops_category,</if>
            <if test="peopleCount != null">people_count,</if>
            <if test="baseEstablishmentRatio != null">base_establishment_ratio,</if>
            <if test="reutersKey != null">reuters_key,</if>
            <if test="tags != null">tags,</if>
            <if test="field != null and field != ''">field,</if>
            <if test="uei != null and uei != ''">uei,</if>
            <if test="cybersecurityRating != null and cybersecurityRating != ''">cybersecurity_rating,</if>
            <if test="supplyChainsGrade != null and supplyChainsGrade != ''">supply_chains_grade,</if>
            <if test="showHome != null">show_home,</if>
            <if test="orderNum != null">order_num,</if>
            <if test="source != null and source != ''">source,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            0,
            <if test="orgId != null">#{orgId},</if>
            <if test="orgCode != null and orgCode != ''">#{orgCode},</if>
            <if test="country != null and country != ''">#{country},</if>
            <if test="orgNameCn != null and orgNameCn != ''">#{orgNameCn},</if>
            <if test="orgNameEn != null">#{orgNameEn},</if>
            <if test="shortName != null">#{shortName},</if>
            <if test="industry != null">#{industry},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="establishTime != null">#{establishTime},</if>
            <if test="address != null">#{address},</if>
            <if test="addressEn != null">#{addressEn},</if>
            <if test="telephone != null">#{telephone},</if>
            <if test="email != null">#{email},</if>
            <if test="profileCn != null">#{profileCn},</if>
            <if test="profileEn != null">#{profileEn},</if>
            <if test="mainFunctions != null">#{mainFunctions},</if>
            <if test="area != null">#{area},</if>
            <if test="founder != null">#{founder},</if>
            <if test="facilities != null">#{facilities},</if>
            <if test="officialWebsite != null">#{officialWebsite},</if>
            <if test="longitude != null and longitude != ''">#{longitude},</if>
            <if test="latitude != null and latitude != ''">#{latitude},</if>
            <if test="orgType != null and orgType != ''">#{orgType},</if>
            <if test="orgTypeAlias != null and orgTypeAlias != ''">#{orgTypeAlias},</if>
            <if test="leader != null">#{leader},</if>
            <if test="leaderProfile != null">#{leaderProfile},</if>
            <if test="troopsCategory != null">#{troopsCategory},</if>
            <if test="peopleCount != null">#{peopleCount},</if>
            <if test="baseEstablishmentRatio != null">#{baseEstablishmentRatio},</if>
            <if test="reutersKey != null">#{reutersKey},</if>
            <if test="tags != null">#{tags},</if>
            <if test="field != null and field != ''">#{field},</if>
            <if test="uei != null and uei != ''">#{uei},</if>
            <if test="cybersecurityRating != null and cybersecurityRating != ''">#{cybersecurityRating},</if>
            <if test="supplyChainsGrade != null and supplyChainsGrade != ''">#{supplyChainsGrade},</if>
            <if test="showHome != null">#{showHome},</if>
            <if test="orderNum != null">#{orderNum},</if>
            <if test="source != null and source != ''">#{source},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateIdwOrg" parameterType="com.lirong.organization.common.domain.IdwOrg">
        update idw_org
        <trim prefix="SET" suffixOverrides=",">
            <if test="country != null">country = #{country},</if>
            <if test="orgNameCn != null">org_name_cn = #{orgNameCn},</if>
            <if test="orgNameEn != null">org_name_en = #{orgNameEn},</if>
            <if test="shortName != null">short_name = #{shortName},</if>
            <if test="industry != null">industry = #{industry},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="establishTime != null">establish_time = #{establishTime},</if>
            <if test="address != null">address = #{address},</if>
            <if test="addressEn != null">addressEn = #{addressEn},</if>
            <if test="telephone != null">telephone = #{telephone},</if>
            <if test="email != null">email = #{email},</if>
            <if test="profileCn != null">profile_cn = #{profileCn},</if>
            <if test="profileEn != null">profile_en = #{profileEn},</if>
            <if test="mainFunctions != null">main_functions = #{mainFunctions},</if>
            <if test="area != null">area = #{area},</if>
            <if test="founder != null">founder = #{founder},</if>
            <if test="facilities != null">facilities = #{facilities},</if>
            <if test="officialWebsite != null">official_website = #{officialWebsite},</if>
            <choose>
                <when test="longitude!=null and longitude != ''">
                    longitude = #{longitude},
                </when>
                <otherwise>
                    longitude = NULL,
                </otherwise>
            </choose>
            <choose>
                <when test="latitude!=null and latitude != ''">
                    latitude = #{latitude},
                </when>
                <otherwise>
                    latitude = NULL,
                </otherwise>
            </choose>
            <if test="orgType != null">org_type = #{orgType},</if>
            <if test="orgTypeAlias != null">org_type_alias = #{orgTypeAlias},</if>
            <if test="leader != null">leader = #{leader},</if>
            <if test="leaderProfile != null">leader_profile = #{leaderProfile},</if>
            <if test="troopsCategory != null">troops_category = #{troopsCategory},</if>
            <if test="peopleCount != null">people_count = #{peopleCount},</if>
            <if test="baseEstablishmentRatio != null">base_establishment_ratio = #{baseEstablishmentRatio},</if>
            <if test="reutersKey != null">reuters_key = #{reutersKey},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="field != null">field = #{field},</if>
            <if test="uei != null">uei = #{uei},</if>
            <if test="cybersecurityRating != null">cybersecurity_rating = #{cybersecurityRating},</if>
            <if test="supplyChainsGrade != null">supply_chains_grade = #{supplyChainsGrade},</if>
            <if test="showHome != null">show_home = #{showHome},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="source != null">source = #{source},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
            where org_id = #{orgId}
    </update>

    <!--根据机构编码删除机构信息-->
    <update id="deleteOrgByOrgCodes">
        UPDATE idw_org
        SET update_by = #{loginName},
        update_time = sysdate(),
        IS_DELETE = 1
        WHERE
            is_delete = 0
            AND org_code IN
        <foreach item="orgCode" collection="orgCodes" open="(" separator="," close=")">
            #{orgCode}
        </foreach>
    </update>

    <!--根据徽章名称修改徽章路径-->
    <update id="updateAvatarByFileName">
        UPDATE idw_org
        SET avatar = #{fileUrl},
        update_by = #{userName},
        update_time = sysdate()
        WHERE
            is_delete = 0
            AND avatar = #{avatar}
            AND (
                create_by = #{userName}
                OR update_by = #{userName}
            )
    </update>

   <!--根据机构编码修改机构评分-->
    <update id="updateScoreByOrgCode">
        UPDATE idw_org
        SET score = #{score}
        WHERE
            is_delete = 0
            AND org_code = #{orgCode}
    </update>

   <!--根据机构编码修改机构排名-->
    <update id="updateRankingByOrgCode">
        UPDATE idw_org
        SET ranking = #{ranking}
        WHERE
            is_delete = 0
            AND org_code = #{orgCode}
    </update>

   <!--根据机构编码修改机构供应链评分-->
    <update id="updateSupplyChainsScoreByOrgCode">
        UPDATE idw_org
        SET supply_chains_score = #{score}
        WHERE
            is_delete = 0
            AND org_code = #{orgCode}
    </update>
</mapper>