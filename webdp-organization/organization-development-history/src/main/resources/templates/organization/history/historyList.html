<div class="container-div">
    <input type="hidden" value="机构发展历程列表">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="form-history">
                <input type="hidden" name="orgCode" th:value="${orgCode}">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>开始年份：</label>
                            <input type="text" name="startYear"/>
                        </li>
                        <li>
                            <label>结束年份：</label>
                            <input type="text" name="endYear"/>
                        </li>
                        <li>
                            <label>事件类型：</label>
                            <select name="type" th:with="type=${@dict.getType('sys_org_development_history_type')}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm"
                               onclick="$.table.search('form-history', 'bootstrap-table-history')"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm"
                               onclick="$.form.reset('form-history', 'bootstrap-table-history')"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar-history" role="group">
            <a class="btn btn-success" onclick="$.operate.add(null, 900, 700)"
               shiro:hasPermission="organization:history:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit(null, 900, 700)"
               shiro:hasPermission="organization:history:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()"
               shiro:hasPermission="organization:history:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table-history"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    let editFlag = [[${@permission.hasPermi('organization:history:edit')}]];
    let removeFlag = [[${@permission.hasPermi('organization:history:remove')}]];
    let typeDatas = [[${@dict.getType('sys_org_development_history_type')}]];
    let prefix = ctx + "organization/history";

    $(function () {
        let options = {
            id: "bootstrap-table-history",          // 指定表格ID
            toolbar: "toolbar-history",   // 指定工具栏ID
            formId: "form-history",
            url: prefix + "/historyList",
            createUrl: prefix + "/addHistory/" + orgCode,
            updateUrl: prefix + "/editHistory/{id}",
            removeUrl: prefix + "/removeHistory",
            exportUrl: prefix + "/export",
            importUrl: prefix + "/importData",
            importTemplateUrl: prefix + "/importTemplate",
            modalName: "机构发展历程",
            uniqueId: 'historyId',
            columns: [{
                checkbox: true
            },
                {
                    align: 'center',
                    title: "序号",
                    width: 60,
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'startYear',
                    title: '开始日期',
                    align: 'center',
                    width: 150,
                    formatter: function (value, row, index) {
                        let startYear = row.startYear;
                        if (!startYear) {
                            return null;
                        }
                        let startDate = startYear + '年';
                        if (row.startMonth) {
                            startDate += row.startMonth + '月';
                        }
                        if (row.startDay) {
                            startDate += row.startDay + '日';
                        }
                        return startDate;
                    }
                },
                {
                    field: 'endYear',
                    title: '结束日期',
                    align: 'center',
                    width: 150,
                    formatter: function (value, row, index) {
                        let endDate;
                        if (row.endYear) {
                            endDate = row.endYear + '年';
                        }
                        if (row.endMonth) {
                            endDate += row.endMonth + '月';
                        }
                        if (row.endDay) {
                            endDate += row.endDay + '日';
                        }
                        return endDate;
                    }
                },
                {
                    field: 'contentCn',
                    title: '发展历程',
                    formatter: function (value, row, index) {
                        return $.table.tooltip(row.contentCn ? row.contentCn : row.contentEn, 60);
                    }
                },
                {
                    field: 'type',
                    title: '事件类型',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(typeDatas, value);
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    width: 120,
                    formatter: function (value, row, index) {
                        let actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.historyId + '\', 900, 700)"><i class="fa fa-edit"></i>修改</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.historyId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });
</script>