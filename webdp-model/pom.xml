<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>webdp</artifactId>
        <groupId>com.lirong</groupId>
        <version>1.0.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>webdp-model</artifactId>
    <description>
        模型管理
    </description>

    <dependencies>
        <!--模型-->
        <dependency>
            <groupId>com.aliasi</groupId>
            <artifactId>lingpipe</artifactId>
            <version>4.1.2</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/src/lib/lingpipe-4.1.2.jar</systemPath>
        </dependency>

        <!-- 通用工具-->
        <dependency>
            <groupId>com.lirong</groupId>
            <artifactId>webdp-common</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-cli</groupId>
            <artifactId>commons-cli</artifactId>
            <version>1.4</version>
        </dependency>

        <dependency>
            <groupId>weka</groupId>
            <artifactId>weka</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/src/lib/weka.jar</systemPath>
        </dependency>

        <!-- jmrc依赖，缺失请执行lib\install.bat在本地仓库安装 -->
        <dependency>
            <groupId>jmrc</groupId>
            <artifactId>jmrc</artifactId>
            <version>1.0</version>
            <scope>system</scope>
            <systemPath>${pom.basedir}/src/lib/jmrc.jar</systemPath>
        </dependency>

    </dependencies>

</project>