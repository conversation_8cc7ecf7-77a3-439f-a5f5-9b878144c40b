package com.lirong.model.bigfive.model;

public class BigFiveScore {

    /**
     * 开放性分数
     */
    private double opennessScore;

    /**
     * 责任心分数
     */
    private double conscientiousnessScore;

    /**
     * 外倾性分数
     */
    private double extraversionScore;

    /**
     * 宜人性分数
     */
    private double agreeablenessScore;

    /**
     * 神经质性分数
     */
    private double neuroticismScore;

    public BigFiveScore() {
    }

    /**
     * 构造函数
     * @param extraversionScore         外倾性分数
     * @param neuroticismScore          神经质性分数
     * @param agreeablenessScore        宜人性分数
     * @param conscientiousnessScore    责任心分数
     * @param opennessScore             开放性分数
     */
    public BigFiveScore(double extraversionScore, double neuroticismScore, double agreeablenessScore, double conscientiousnessScore, double opennessScore) {
        this.opennessScore = opennessScore;
        this.conscientiousnessScore = conscientiousnessScore;
        this.extraversionScore = extraversionScore;
        this.agreeablenessScore = agreeablenessScore;
        this.neuroticismScore = neuroticismScore;
    }

    public double getOpennessScore() {
        return opennessScore;
    }

    public void setOpennessScore(double opennessScore) {
        this.opennessScore = opennessScore;
    }

    public double getConscientiousnessScore() {
        return conscientiousnessScore;
    }

    public void setConscientiousnessScore(double conscientiousnessScore) {
        this.conscientiousnessScore = conscientiousnessScore;
    }

    public double getExtraversionScore() {
        return extraversionScore;
    }

    public void setExtraversionScore(double extraversionScore) {
        this.extraversionScore = extraversionScore;
    }

    public double getAgreeablenessScore() {
        return agreeablenessScore;
    }

    public void setAgreeablenessScore(double agreeablenessScore) {
        this.agreeablenessScore = agreeablenessScore;
    }

    public double getNeuroticismScore() {
        return neuroticismScore;
    }

    public void setNeuroticismScore(double neuroticismScore) {
        this.neuroticismScore = neuroticismScore;
    }
}
