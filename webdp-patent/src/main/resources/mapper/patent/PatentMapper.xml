<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lirong.patent.mapper.PatentMapper">

    <resultMap type="com.lirong.patent.domain.Patent" id="PatentResult">
        <result property="id"    column="id"    />
        <result property="country"    column="country"    />
        <result property="patentNumber"    column="patent_number"    />
        <result property="title"    column="title"    />
        <result property="patentAbstract"    column="abstract"    />
        <result property="inventors"    column="inventors"    />
        <result property="applicationNumber"    column="application_number"    />
        <result property="publicationDate"    column="publication_date"    />
        <result property="fillDate"    column="fill_date"    />
        <result property="assignee"    column="assignee"    />
        <result property="primaryClass"    column="primary_class"    />
        <result property="otherClasses"    column="other_classes"    />
        <result property="internationalClasses"    column="international_classes"    />
        <result property="patentImagesUrl"    column="patent_Images_url"    />
        <result property="searchField"    column="search_field"    />
        <result property="storagePath"    column="storage_path"    />
        <result property="foreignReferences"    column="foreign_references"    />
        <result property="primaryExaminer"    column="primary_examiner"    />
        <result property="otherReferences"    column="other_references"    />
        <result property="agent"    column="agent"    />
        <result property="claims"    column="claims"    />
        <result property="description"    column="description"    />
        <result property="source"    column="source"    />
    </resultMap>

    <sql id="selectPatentVo">
        select id, country, patent_number, title, abstract, inventors, application_number, publication_date, fill_date, assignee, primary_class, other_classes, international_classes, patent_Images_url, search_field, storage_path, foreign_references, primary_examiner, other_references, agent, claims, description, source from patent
    </sql>

    <select id="selectPatentList" parameterType="com.lirong.patent.domain.Patent" resultMap="PatentResult">
        <include refid="selectPatentVo"/>
        <where>
            <if test="country != null  and country != ''"> and country = #{country}</if>
            <if test="title != null  and title != ''"> and title LIKE CONCAT( '%', #{title}, '%' ) </if>
            <if test="assignee != null  and assignee != ''"> and assignee LIKE CONCAT( '%', #{assignee}, '%' ) </if>
        </where>
    </select>

    <select id="selectPatentById" parameterType="Long" resultMap="PatentResult">
        <include refid="selectPatentVo"/>
        WHERE
            id = #{id}
    </select>

    <insert id="insertPatent" parameterType="com.lirong.patent.domain.Patent">
        insert into patent
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="country != null">country,</if>
            <if test="patentNumber != null">patent_number,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="abstract != null">abstract,</if>
            <if test="inventors != null">inventors,</if>
            <if test="applicationNumber != null">application_number,</if>
            <if test="publicationDate != null">publication_date,</if>
            <if test="fillDate != null">fill_date,</if>
            <if test="assignee != null">assignee,</if>
            <if test="primaryClass != null">primary_class,</if>
            <if test="otherClasses != null">other_classes,</if>
            <if test="internationalClasses != null">international_classes,</if>
            <if test="patentImagesUrl != null">patent_Images_url,</if>
            <if test="searchField != null">search_field,</if>
            <if test="storagePath != null">storage_path,</if>
            <if test="foreignReferences != null">foreign_references,</if>
            <if test="primaryExaminer != null">primary_examiner,</if>
            <if test="otherReferences != null">other_references,</if>
            <if test="agent != null">agent,</if>
            <if test="claims != null">claims,</if>
            <if test="description != null">description,</if>
            <if test="source != null">source,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="country != null">#{country},</if>
            <if test="patentNumber != null">#{patentNumber},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="abstract != null">#{abstract},</if>
            <if test="inventors != null">#{inventors},</if>
            <if test="applicationNumber != null">#{applicationNumber},</if>
            <if test="publicationDate != null">#{publicationDate},</if>
            <if test="fillDate != null">#{fillDate},</if>
            <if test="assignee != null">#{assignee},</if>
            <if test="primaryClass != null">#{primaryClass},</if>
            <if test="otherClasses != null">#{otherClasses},</if>
            <if test="internationalClasses != null">#{internationalClasses},</if>
            <if test="patentImagesUrl != null">#{patentImagesUrl},</if>
            <if test="searchField != null">#{searchField},</if>
            <if test="storagePath != null">#{storagePath},</if>
            <if test="foreignReferences != null">#{foreignReferences},</if>
            <if test="primaryExaminer != null">#{primaryExaminer},</if>
            <if test="otherReferences != null">#{otherReferences},</if>
            <if test="agent != null">#{agent},</if>
            <if test="claims != null">#{claims},</if>
            <if test="description != null">#{description},</if>
            <if test="source != null">#{source},</if>
        </trim>
    </insert>

    <update id="updatePatent" parameterType="com.lirong.patent.domain.Patent">
        update patent
        <trim prefix="SET" suffixOverrides=",">
            <if test="country != null">country = #{country},</if>
            <if test="patentNumber != null">patent_number = #{patentNumber},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="abstract != null">abstract = #{abstract},</if>
            <if test="inventors != null">inventors = #{inventors},</if>
            <if test="applicationNumber != null">application_number = #{applicationNumber},</if>
            <if test="publicationDate != null">publication_date = #{publicationDate},</if>
            <if test="fillDate != null">fill_date = #{fillDate},</if>
            <if test="assignee != null">assignee = #{assignee},</if>
            <if test="primaryClass != null">primary_class = #{primaryClass},</if>
            <if test="otherClasses != null">other_classes = #{otherClasses},</if>
            <if test="internationalClasses != null">international_classes = #{internationalClasses},</if>
            <if test="patentImagesUrl != null">patent_Images_url = #{patentImagesUrl},</if>
            <if test="searchField != null">search_field = #{searchField},</if>
            <if test="storagePath != null">storage_path = #{storagePath},</if>
            <if test="foreignReferences != null">foreign_references = #{foreignReferences},</if>
            <if test="primaryExaminer != null">primary_examiner = #{primaryExaminer},</if>
            <if test="otherReferences != null">other_references = #{otherReferences},</if>
            <if test="agent != null">agent = #{agent},</if>
            <if test="claims != null">claims = #{claims},</if>
            <if test="description != null">description = #{description},</if>
            <if test="source != null">source = #{source},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePatentByIds" parameterType="String">
        delete from patent where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!--批量删除专利机构-->
    <delete id="deletePatentOrgByIds" parameterType="String">
        delete from patent where patent_id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper>